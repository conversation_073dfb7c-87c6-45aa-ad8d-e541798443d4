# CodeEditor 组件库

## JsonEditorModal 组件

基于 Element UI 的 Dialog 组件封装的 JSON 编辑器弹框，集成了 sm-json-editor 编辑器功能。

### 功能特性

- ✅ 基于 el-dialog 的弹框展示
- ✅ 集成 sm-json-editor 编辑器
- ✅ JSON 格式验证和错误提示
- ✅ 格式化、压缩、清空、重置功能
- ✅ 实时状态显示（有效/无效、字符数）
- ✅ 预览功能
- ✅ 只读模式支持
- ✅ 自定义配置（宽度、高度、缩进等）
- ✅ 响应式设计
- ✅ 完整的错误处理

### 基础用法

```vue
<template>
  <div>
    <el-button type="primary" @click="showModal">打开JSON编辑器</el-button>

    <json-editor-modal
      :visible.sync="modalVisible"
      title="JSON编辑器"
      :value="jsonData"
      @ok="handleOk"
      @cancel="handleCancel"
    />
  </div>
</template>

<script>
import JsonEditorModal from "@/components/CodeEditor/JsonEditorModal.vue";

export default {
  components: {
    JsonEditorModal,
  },
  data() {
    return {
      modalVisible: false,
      jsonData: '{"name": "张三", "age": 25}',
    };
  },
  methods: {
    showModal() {
      this.modalVisible = true;
    },
    handleOk(jsonData) {
      this.jsonData = jsonData;
      this.modalVisible = false;
      this.$message.success("保存成功");
    },
    handleCancel() {
      this.modalVisible = false;
    },
  },
};
</script>
```

### Props 属性

| 属性名       | 类型          | 默认值        | 说明               |
| ------------ | ------------- | ------------- | ------------------ |
| visible      | Boolean       | false         | 控制弹框显示状态   |
| title        | String        | 'JSON 编辑器' | 弹框标题           |
| modalWidth   | String/Number | '800px'       | 弹框宽度           |
| value        | String        | ''            | JSON 数据内容      |
| showToolbar  | Boolean       | true          | 是否显示工具栏     |
| showError    | Boolean       | true          | 是否显示错误信息   |
| validate     | Boolean       | true          | 是否进行 JSON 验证 |
| editorHeight | String        | '400px'       | 编辑器高度         |
| readonly     | Boolean       | false         | 是否只读模式       |
| indentSize   | Number        | 2             | JSON 缩进大小      |
| allowEmpty   | Boolean       | false         | 是否允许空值       |

### Events 事件

| 事件名         | 说明                   | 回调参数           |
| -------------- | ---------------------- | ------------------ |
| ok             | 点击确定按钮时触发     | (jsonData: string) |
| cancel         | 点击取消按钮时触发     | -                  |
| update:visible | 弹框显示状态变化时触发 | (visible: boolean) |

### Methods 方法

| 方法名          | 说明               | 参数          |
| --------------- | ------------------ | ------------- |
| getEditor()     | 获取编辑器实例     | -             |
| setValue(value) | 设置 JSON 内容     | value: string |
| getValue()      | 获取当前 JSON 内容 | -             |
| isValidJson()   | 检查 JSON 是否有效 | -             |

### 使用场景

#### 1. 基础编辑模式

```vue
<json-editor-modal
  :visible="visible"
  title="编辑JSON配置"
  :value="configData"
  @ok="handleSave"
  @cancel="handleCancel"
/>
```

#### 2. 只读查看模式

```vue
<json-editor-modal
  :visible="visible"
  title="查看JSON配置"
  :value="configData"
  :readonly="true"
  :showToolbar="false"
  @cancel="handleCancel"
/>
```

#### 3. 自定义配置

```vue
<json-editor-modal
  :visible="visible"
  title="高级JSON编辑器"
  :value="configData"
  :modalWidth="1000"
  :editorHeight="'500px'"
  :indentSize="4"
  :allowEmpty="true"
  @ok="handleSave"
  @cancel="handleCancel"
/>
```

### 工具栏功能

- **格式化**: 将 JSON 格式化为标准格式
- **压缩**: 移除所有空格和换行符
- **清空**: 清空编辑器内容
- **重置**: 恢复到原始内容
- **状态显示**: 显示 JSON 有效性状态和字符数

### 错误处理

组件内置了完整的错误处理机制：

1. **JSON 格式验证**: 实时验证 JSON 格式
2. **错误提示**: 显示具体的错误信息
3. **状态反馈**: 通过颜色和图标显示验证状态
4. **操作保护**: 无效 JSON 时禁用相关操作

### 最佳实践

#### 1. 数据初始化

```javascript
// 确保传入的 JSON 数据是格式化的
const jsonData = JSON.stringify(originalData, null, 2);
```

#### 2. 错误处理

```javascript
handleOk(jsonData) {
  try {
    // 验证 JSON 格式
    JSON.parse(jsonData);

    // 保存数据
    this.saveData(jsonData);
    this.$message.success('保存成功');
  } catch (error) {
    this.$message.error('JSON 格式错误');
  }
}
```

#### 3. 性能优化

```javascript
// 大数据量时使用只读模式
<json-editor-modal
  :visible="visible"
  :value="largeJsonData"
  :readonly="true"
  :showToolbar="false"
/>
```

#### 4. 响应式处理

```javascript
// 监听窗口大小变化
mounted() {
  window.addEventListener('resize', this.handleResize);
},
beforeDestroy() {
  window.removeEventListener('resize', this.handleResize);
},
methods: {
  handleResize() {
    // 调整弹框大小
    this.modalWidth = window.innerWidth < 768 ? '95%' : '800px';
  }
}
```

### 样式定制

组件支持通过 CSS 变量进行样式定制：

```less
// 自定义主题色
.json-editor-modal-container {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --error-color: #f5222d;
}

// 自定义工具栏样式
.toolbar {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
}
```

### 注意事项

1. **数据格式**: 确保传入的 value 是有效的 JSON 字符串
2. **性能考虑**: 大数据量时建议使用只读模式
3. **浏览器兼容**: 支持主流浏览器，IE 需要额外处理
4. **内存管理**: 组件销毁时会自动清理资源
5. **事件处理**: 注意处理 ok 和 cancel 事件，避免内存泄漏

### 更新日志

- **v1.0.0**: 初始版本，支持基础 JSON 编辑功能
- **v1.1.0**: 新增工具栏和状态显示功能
- **v1.2.0**: 新增预览功能和错误处理优化
- **v1.3.0**: 新增响应式设计和样式优化
