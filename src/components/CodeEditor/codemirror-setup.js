// CodeMirror 模块初始化文件
// 统一管理CodeMirror的所有依赖，确保在生产环境正确加载

// 基础功能
import CodeMirror from 'codemirror';

// 引入基础样式
import 'codemirror/lib/codemirror.css';
// 引入主题样式
import 'codemirror/theme/monokai.css';
// 代码折叠
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/hint/show-hint.css';

// JavaScript 语言支持
import 'codemirror/mode/javascript/javascript.js';

// Java/C-like 语言支持
import 'codemirror/mode/clike/clike.js';

// 代码折叠功能
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';

// 搜索功能
import 'codemirror/addon/search/searchcursor.js';

// 自动完成功能
import 'codemirror/addon/hint/show-hint.js';

// 括号匹配
import 'codemirror/addon/edit/matchbrackets.js';

// 导出已配置的CodeMirror
export default CodeMirror;

// 确保模块正确注册
export const isCodeMirrorReady = () => {
  console.log('🚀 ~ isCodeMirrorReady ~ CodeMirror.modes.clike :', CodeMirror.modes.clike);
  return !!(CodeMirror.modes && CodeMirror.modes.javascript && CodeMirror.modes.clike && CodeMirror.commands.foldCode);
};
