<template>
  <div class="sm-code-editor">
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="toolbar">
      <el-button size="small" @click="formatCode">格式化</el-button>
      <el-button size="small" @click="compressCode">压缩</el-button>
      <el-button size="small" @click="clearCode">清空</el-button>
      <el-button size="small" @click="resetCode">重置</el-button>
      <el-button size="small" @click="toggleFullscreen">
        <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
        {{ isFullscreen ? '退出全屏' : '全屏' }}
      </el-button>
      <span class="status">
        <i :class="errorMessage ? 'el-icon-warning' : 'el-icon-success'" :style="{ color: errorMessage ? '#f56c6c' : '#67c23a' }"></i>
        {{ errorMessage ? '无效' : '有效' }} | {{ code.length }} 字符
      </span>
    </div>

    <!-- 编辑器容器 -->
    <div ref="codeEditor" class="editor-container" :class="{ fullscreen: isFullscreen }"></div>

    <!-- 错误信息 -->
    <div
      v-if="errorMessage && (showError || isFullscreen)"
      class="error-message"
      :class="{ 'fullscreen-error': isFullscreen }"
      style="color: #f56c6c"
    >
      <i class="el-icon-warning"></i> {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { validateJava } from './ValidateJava';
import { validateJson } from './validateJson';
// 直接导入CodeMirror
import CodeMirror from 'codemirror';

// 导入必要的模式和插件
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/monokai.css';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/clike/clike.js';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/edit/matchbrackets.js';
import 'codemirror/addon/edit/closebrackets.js';

export default {
  name: 'SmCodeEditor',
  props: {
    value: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'java', // java, json
    },
    // 是否展示错误信息
    showError: {
      type: Boolean,
      default: true,
    },
    // 是否进行代码校验
    validate: {
      type: Boolean,
      default: true,
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: false,
    },
    // 预编译接口
    api: {
      type: String,
      default: '',
    },
    scriptName: {
      type: String,
      default: '',
    },
    // 编辑器高度
    height: {
      type: String,
      default: '200px',
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      code: this.value || '',
      errorMessage: '',
      isFullscreen: false,
      codemirrorReady: false,
      cm: null, // CodeMirror实例
      originalCode: this.value || '', // 保存原始代码用于重置
      cmOptions: {
        // 编辑器配置选项
        mode: this.mode === 'java' ? 'text/x-java' : 'application/json', // 设置语言模式
        theme: 'monokai', // 主题
        lineNumbers: true, // 显示行号
        line: true, // 显示行背景
        tabSize: 2, // Tab大小
        indentUnit: 2, // 缩进单位
        smartIndent: true, // 智能缩进
        matchBrackets: true, // 匹配括号
        autoCloseBrackets: true, // 自动闭合括号
        foldGutter: true, // 代码折叠
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        readOnly: this.readonly, // 只读模式
        extraKeys: {
          // 快捷键
          'Ctrl-Space': 'autocomplete',
        },
      },
    };
  },
  watch: {
    value(newVal) {
      if (newVal !== this.code) {
        this.code = newVal;
        this.originalCode = newVal;
        if (this.cm) {
          this.cm.setValue(newVal);
        }
      }
    },
    code(newVal) {
      this.$emit('input', newVal);
      this.$emit('change', newVal);
    },
    mode(newVal) {
      if (this.cm) {
        const modeMap = {
          java: 'text/x-java',
          json: 'application/json',
        };
        this.cm.setOption('mode', modeMap[newVal] || 'text/x-java');
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCodeMirror();
    });

    // 监听全屏变化
    if (screenfull.isEnabled) {
      screenfull.on('change', () => {
        this.isFullscreen = screenfull.isFullscreen;
      });
    }
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听和清理CodeMirror实例
    if (screenfull.isEnabled) {
      screenfull.off('change');
    }
    if (this.cm) {
      this.cm.toTextArea();
    }
  },
  methods: {
    // 初始化CodeMirror编辑器
    initCodeMirror() {
      if (!this.$refs.codeEditor) return;

      // 创建CodeMirror实例
      this.cm = CodeMirror(this.$refs.codeEditor, {
        ...this.cmOptions,
        value: this.code,
      });

      // 设置高度
      this.cm.setSize(null, this.height);

      // 监听内容变化
      this.cm.on('change', (cm) => {
        const newValue = cm.getValue();
        if (newValue !== this.code) {
          this.code = newValue;
          if (this.validate) {
            this.validateCode();
          }
        }
      });

      // 触发ready事件
      this.onReady(this.cm);
      this.codemirrorReady = true;

      // 初始验证
      if (this.validate && this.code) {
        this.validateCode();
      }
    },

    // 验证代码
    validateCode() {
      if (!this.validate) {
        this.errorMessage = '';
        return;
      }

      try {
        if (this.mode === 'json') {
          const result = validateJson(this.code);
          this.errorMessage = result.isValid ? '' : result.error;
        } else if (this.mode === 'java') {
          const result = validateJava(this.code);
          this.errorMessage = result.isValid ? '' : result.error;
        }
      } catch (error) {
        this.errorMessage = error.message || '验证失败';
      }
    },

    // 格式化代码
    formatCode() {
      if (!this.cm) return;

      try {
        if (this.mode === 'json') {
          const parsed = JSON.parse(this.code);
          const formatted = JSON.stringify(parsed, null, 2);
          this.cm.setValue(formatted);
          this.code = formatted;
        }
        this.errorMessage = '';
      } catch (error) {
        this.errorMessage = 'JSON格式错误，无法格式化';
      }
    },

    // 压缩代码
    compressCode() {
      if (!this.cm) return;

      try {
        if (this.mode === 'json') {
          const parsed = JSON.parse(this.code);
          const compressed = JSON.stringify(parsed);
          this.cm.setValue(compressed);
          this.code = compressed;
        }
        this.errorMessage = '';
      } catch (error) {
        this.errorMessage = 'JSON格式错误，无法压缩';
      }
    },

    // 清空代码
    clearCode() {
      if (this.cm) {
        this.cm.setValue('');
        this.code = '';
        this.errorMessage = '';
      }
    },

    // 重置代码
    resetCode() {
      if (this.cm) {
        this.cm.setValue(this.originalCode);
        this.code = this.originalCode;
        if (this.validate) {
          this.validateCode();
        }
      }
    },

    // 切换全屏
    toggleFullscreen() {
      if (!screenfull.isEnabled) {
        this.$message.warning('您的浏览器不支持全屏功能');
        return;
      }

      if (screenfull.isFullscreen) {
        screenfull.exit();
      } else {
        screenfull.request(this.$refs.codeEditor);
      }
    },

    // 编辑器就绪回调
    onReady(cm) {
      this.$emit('ready', cm);
    },

    // 获取编辑器实例
    getCodeMirror() {
      return this.cm;
    },

    // 设置编辑器值
    setValue(value) {
      if (this.cm) {
        this.cm.setValue(value);
        this.code = value;
      }
    },

    // 获取编辑器值
    getValue() {
      return this.code;
    },
  },
};
</script>

<style lang="less" scoped>
.sm-code-editor {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;

    .el-button {
      margin-right: 8px;
    }

    .status {
      font-size: 12px;
      color: #606266;
      display: flex;
      align-items: center;

      i {
        margin-right: 4px;
      }
    }
  }

  .editor-container {
    position: relative;

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: white;
    }
  }

  .error-message {
    padding: 8px 12px;
    background-color: #fef0f0;
    border-top: 1px solid #fbc4c4;
    font-size: 12px;

    &.fullscreen-error {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10000;
    }

    i {
      margin-right: 4px;
    }
  }
}
</style>
