export function validateJava(code) {
  try {
    const errors = [];
    const lines = code.split('\n');

    // 检查基本语法
    checkBrackets(code, errors);
    checkSemicolons(lines, errors);
    checkJavaKeywords(lines, errors);
    checkMethodDeclarations(lines, errors);

    return {
      valid: errors.length === 0,
      errors: errors,
    };
  } catch (e) {
    return {
      valid: false,
      errors: [`Java代码错误: ${e.message}`],
    };
  }
}

function checkBrackets(code, errors) {
  const brackets = code.match(/[{}()[\]]/g) || [];
  const stack = [];
  const bracketPairs = { '}': '{', ')': '(', ']': '[' };

  for (let i = 0; i < brackets.length; i++) {
    const bracket = brackets[i];
    if ('{(['.includes(bracket)) {
      stack.push(bracket);
    } else {
      const last = stack.pop();
      if (last !== bracketPairs[bracket]) {
        errors.push(`括号不匹配: 位置 ${i + 1}`);
        break;
      }
    }
  }
  if (stack.length > 0) {
    errors.push(`未闭合的括号: ${stack.join(', ')}`);
  }
}

function checkSemicolons(lines, errors) {
  const skipPatterns = [
    /^\s*$/,
    /\s*[{}]\s*$/,
    /^\s*(package|import|public|private|protected|class|interface)\b/,
    /^\s*(\/\/|\/\*|\*|\*\/)/,
  ];

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    if (trimmedLine && !skipPatterns.some((pattern) => pattern.test(trimmedLine)) && !trimmedLine.endsWith(';')) {
      errors.push(`第${index + 1}行可能缺少分号`);
    }
  });
}

function checkJavaKeywords(lines, errors) {
  const keywords = ['class', 'interface', 'enum'];
  let hasMainStructure = false;

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    keywords.forEach((keyword) => {
      if (new RegExp(`\\b${keyword}\\b`).test(trimmedLine)) {
        hasMainStructure = true;
        if (!/\b[A-Z][a-zA-Z0-9]*\b/.test(trimmedLine)) {
          errors.push(`第${index + 1}行: ${keyword}名称应该以大写字母开头`);
        }
      }
    });
  });

  if (!hasMainStructure) {
    errors.push('缺少主要结构定义(class/interface/enum)');
  }
}

function checkMethodDeclarations(lines, errors) {
  const methodPattern = /\b(public|private|protected)?\s+\w+\s+\w+\s*\([^)]*\)\s*{?/;
  lines.forEach((line, index) => {
    if (methodPattern.test(line.trim())) {
      if (!/^[a-z]/.test(line.trim().split(/\s+/)[2])) {
        errors.push(`第${index + 1}行: 方法名应该以小写字母开头`);
      }
    }
  });
}
