<template>
  <div class="code-editor-container">
    <div ref="codeEditor" class="code-editor"></div>
    <div class="tool-bar" :class="{ 'fullscreen-toolbar': isFullscreen }">
      <el-tooltip content="预编译">
        <el-button
          type="primary"
          size="small"
          style="margin-top: 8px; margin-right: 8px"
          @click="preCompile"
        >
          <i class="el-icon-s-operation"></i>
        </el-button>
      </el-tooltip>
      <!-- <el-tooltip content="格式化">
        <el-button type="primary" size="small" style="margin-top: 8px; margin-right: 8px" @click="formatCode">
          <i class="el-icon-edit"></i>
        </el-button>
      </el-tooltip> -->
      <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'">
        <el-button
          type="primary"
          size="small"
          style="margin-top: 8px"
          @click="handleFullscreen"
        >
          <i
            :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
          ></i>
        </el-button>
      </el-tooltip>
    </div>
    <div
      v-if="errorMessage && (showError || isFullscreen)"
      class="error-message"
      :class="{ 'fullscreen-error': isFullscreen }"
      style="color: #f56c6c"
    >
      <i class="el-icon-warning"></i> {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import screenfull from "screenfull";
import { validateJava } from "./ValidateJava";
import { validateJson } from "./validateJson";
// 直接导入CodeMirror
import CodeMirror from "codemirror";

export default {
  name: "sm-code-editor",
  props: {
    value: {
      type: String,
      default: "",
    },
    mode: {
      type: String,
      default: "java", // java, json
    },
    // 是否展示错误信息
    showError: {
      type: Boolean,
      default: true,
    },
    // 是否进行代码校验
    validate: {
      type: Boolean,
      default: true,
    },
    // 预编译接口
    api: {
      type: String,
      default: "",
    },
    scriptName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      code: this.value || "",
      errorMessage: "",
      isFullscreen: false,
      codemirrorReady: false,
      cm: null, // CodeMirror实例
      cmOptions: {
        // 编辑器配置选项
        mode: this.mode === "java" ? "text/x-java" : "application/json", // 设置语言模式
        theme: "monokai", // 主题
        lineNumbers: true, // 显示行号
        line: true, // 显示行背景
        tabSize: 2, // Tab大小
        indentUnit: 2, // 缩进单位
        smartIndent: true, // 智能缩进
        matchBrackets: true, // 匹配括号
        autoCloseBrackets: true, // 自动闭合括号
        foldGutter: true, // 代码折叠
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        extraKeys: {
          // 快捷键
          "Ctrl-Space": "autocomplete",
        },
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.code) {
          this.code = val || "";
          // 更新CodeMirror内容，避免无限循环
          if (this.cm && this.cm.getValue() !== this.code) {
            this.cm.setValue(this.code);
          }
        }
      },
      immediate: true,
    },
    code(val) {
      this.$emit("input", val || "");
      this.$emit("change", val || "");
    },
  },
  async mounted() {
    // 动态导入CodeMirror模块，确保在生产环境正确加载
    try {
      await this.loadCodeMirrorModules();
      this.codemirrorReady = true;
      console.log("CodeMirror modules loaded successfully");

      // 初始化CodeMirror编辑器
      this.initCodeMirror();
    } catch (error) {
      console.error("CodeMirror modules load failed:", error);
    }

    // 监听全屏变化事件
    if (screenfull.isEnabled) {
      screenfull.on("change", () => {
        this.isFullscreen = screenfull.isFullscreen;
      });
    }
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听和清理CodeMirror实例
    if (screenfull.isEnabled) {
      screenfull.off("change");
    }
    if (this.cm) {
      this.cm.toTextArea();
    }
  },
  methods: {
    // 初始化CodeMirror编辑器
    initCodeMirror() {
      if (!this.$refs.codeEditor) return;

      // 创建CodeMirror实例
      this.cm = CodeMirror(this.$refs.codeEditor, {
        ...this.cmOptions,
        value: this.code,
      });

      // 监听内容变化
      this.cm.on("change", (cm, changeObj) => {
        const newValue = cm.getValue();
        if (newValue !== this.code) {
          this.code = newValue;
          if (this.validate) {
            this.validateCode();
          }
        }
      });

      // 触发ready事件
      this.onReady(this.cm);

      console.log("CodeMirror initialized successfully");
      console.log("Available modes:", Object.keys(CodeMirror.modes));
      console.log("Java mode available:", !!CodeMirror.modes["text/x-java"]);
    },

    // 动态加载CodeMirror模块
    async loadCodeMirrorModules() {
      // 动态导入JavaScript模块，避免打包时的路径问题
      const modules = [
        () => import("codemirror/mode/javascript/javascript.js"),
        () => import("codemirror/mode/clike/clike.js"),
        () => import("codemirror/addon/fold/foldcode.js"),
        () => import("codemirror/addon/fold/foldgutter.js"),
        () => import("codemirror/addon/fold/brace-fold.js"),
        () => import("codemirror/addon/search/searchcursor.js"),
        () => import("codemirror/addon/hint/show-hint.js"),
        () => import("codemirror/addon/edit/matchbrackets.js"),
      ];

      // 并发加载所有模块
      await Promise.all(modules.map((moduleLoader) => moduleLoader()));

      // 验证模块是否正确加载
      await this.$nextTick();
      this.validateModulesLoaded();
    },

    // 验证CodeMirror模块是否正确加载
    validateModulesLoaded() {
      if (!CodeMirror) {
        throw new Error("CodeMirror not available");
      }

      // 检查关键模块是否已加载
      const requiredModes = ["javascript", "clike"];
      const missingModes = requiredModes.filter(
        (mode) => !CodeMirror.modes[mode]
      );

      if (missingModes.length > 0) {
        console.warn("Missing CodeMirror modes:", missingModes);
      }

      // 检查代码折叠功能
      if (!CodeMirror.commands.foldCode) {
        console.warn("CodeMirror fold commands not available");
      }

      console.log("CodeMirror validation completed");
    },

    onReady(cm) {
      this.cm = cm;
      if (this.validate) {
        this.validateCode();
      }
    },

    validateCode() {
      if (!this.code) {
        this.clearError();
        return;
      }
      const validator =
        this.mode === "json" ? this.validateJsonCode : this.validateJava;
      validator();
    },
    validateJava() {
      const result = validateJava(this.code);
      this.handleValidationResult(result);
    },
    validateJsonCode() {
      const result = validateJson(this.code);
      this.handleValidationResult(result);
    },
    handleValidationResult(result) {
      if (!result.valid) {
        this.errorMessage = result.errors.join("; ");
        this.addErrorClass();
      } else {
        this.clearError();
      }
    },
    clearError() {
      this.errorMessage = "";
      this.removeErrorClass();
    },
    addErrorClass(className = "invalid") {
      if (this.cm) {
        this.cm.getWrapperElement().classList.add(className);
      }
    },
    removeErrorClass(className = "invalid") {
      if (this.cm) {
        this.cm.getWrapperElement().classList.remove(className);
      }
    },
    formatCode() {
      try {
        if (this.mode === "json") {
          // JSON格式化
          const obj = JSON.parse(this.code);
          this.code = JSON.stringify(obj, null, 2);
        } else {
          // Java代码格式化
          const lines = this.code?.split("\n") || [];
          // 移除多余空行
          const nonEmptyLines = lines.filter((line) => line.trim() !== "");
          // 处理每行缩进和空格
          const formattedLines = nonEmptyLines.map((line) => {
            // 移除行尾空格
            line = line.trimEnd();
            // 确保操作符前后有空格
            line = line.replace(/([=+\-*/<>])/g, " $1 ");
            // 移除多余空格
            line = line.replace(/\s+/g, " ");
            // 自动补全分号
            if (
              line.trim() &&
              !line.trim().endsWith(";") &&
              !line.trim().endsWith("{") &&
              !line.trim().endsWith("}") &&
              !line.trim().startsWith("//") &&
              !line.trim().startsWith("/*") &&
              !line.trim().startsWith("*") &&
              !line.trim().startsWith("@")
            ) {
              line = line + ";";
            }
            return line;
          });
          this.code = formattedLines.join("\n");
        }

        // 更新CodeMirror内容
        if (this.cm) {
          this.cm.setValue(this.code);
        }

        this.$message.success("格式化成功");
      } catch (e) {
        this.$message.error(`格式化失败: ${e.message}`);
      }
    },
    async preCompile() {
      // 预编译代码
      if (!this.code) {
        this.$message.warning("请输入代码内容");
        return;
      }
      if (!this.api) {
        this.$message.warning("未配置预编译接口");
        return;
      }
      const [res] = await this.$post(this.api, {
        scriptName: this.scriptName,
        scriptData: this.code,
      });
      if (res?.data === null) {
        this.$message.success("编译成功");
      } else {
        this.errorMessage = res?.data || "";
      }
    },
    handleFullscreen() {
      if (!screenfull.isEnabled) {
        this.$message.warning("当前浏览器不支持全屏");
        return;
      }
      const editorElement = this.cm.getWrapperElement();
      if (!this.isFullscreen) {
        screenfull.request(editorElement);
      } else {
        screenfull.exit();
      }
      // 触发重新渲染以调整大小
      this.cm.refresh();
    },
    codeValidate() {
      if (!this.validate) {
        return {
          valid: true,
          errorMsg: "",
        };
      }
      if (this.language === "java") {
        this.validateJava();
      } else if (this.language === "json") {
        this.validateJson(this.code);
      }
      return {
        valid: !this.errorMessage,
        errorMsg: this.errorMessage,
      };
    },
    // 清空代码块内容
    setValue(value = "") {
      if (this.cm) {
        this.cm.setValue(value);
        this.code = value;
      }
    },
  },
};
</script>

<style lang="less">
// 导入CodeMirror样式文件，确保在生产环境正确加载
@import "~codemirror/lib/codemirror.css";
@import "~codemirror/theme/monokai.css";
@import "~codemirror/addon/fold/foldgutter.css";
@import "~codemirror/addon/hint/show-hint.css";

.code-editor {
  // CodeMirror 会在这个容器内创建编辑器
  min-height: 120px;
  border: 1px solid #eee;

  // 确保CodeMirror实例的样式
  :deep(.CodeMirror) {
    height: 120px;
    padding: 8px;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", "Consolas", "source-code-pro",
      monospace;
    line-height: 2; // 缩减行高

    .CodeMirror-line {
      line-height: 2; // 具体行内容的行高
    }

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
      z-index: 9999;
    }
  }
}

.CodeMirror {
  height: 120px;
  border: 1px solid #eee;
  padding: 8px;
  line-height: 2; // 缩减行高

  .CodeMirror-line {
    line-height: 2; // 具体行内容的行高
  }

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100vh;
    z-index: 9999;
  }
}
.invalid {
  background: #fff1f0;
}
.error-message {
  margin-top: 8px;

  &.fullscreen-error {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10001;
    background: rgba(0, 0, 0, 0.8);
    color: #fff !important;
    padding: 8px 16px;
    border-radius: 4px;
  }
}
.code-editor-container {
  position: relative;
  .tool-bar {
    position: absolute;
    top: 8px;
    right: 16px;
    z-index: 2;
    transition: all 0.3s;

    &.fullscreen-toolbar {
      position: fixed;
      top: 20px;
      right: 30px;
      z-index: 10001;
    }
  }
}
</style>
