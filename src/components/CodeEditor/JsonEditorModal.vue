<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="title"
    :width="modalWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    @close="handleCancel"
  >
    <div class="json-editor-modal-container">
      <!-- 工具栏 -->
      <div class="toolbar" v-if="showToolbar">
        <div class="toolbar-buttons">
          <el-button
            type="primary"
            size="small"
            @click="handleFormat"
            :loading="formatting"
          >
            <i class="el-icon-edit"></i>
            格式化
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleCompress"
            :disabled="!jsonValue.trim()"
          >
            <i class="el-icon-minus"></i>
            压缩
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleClear"
            :disabled="!jsonValue.trim()"
          >
            <i class="el-icon-delete"></i>
            清空
          </el-button>
          <el-button type="default" size="small" @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </div>

        <div class="status-info">
          <el-tag
            :type="isValid ? 'success' : 'danger'"
            v-if="jsonValue.trim()"
          >
            <i :class="isValid ? 'el-icon-success' : 'el-icon-error'"></i>
            {{ isValid ? "有效JSON" : "无效JSON" }}
          </el-tag>
          <el-tag type="info" v-if="jsonValue.trim()">
            字符数: {{ jsonValue.length }}
          </el-tag>
        </div>
      </div>

      <!-- JSON编辑器 -->
      <div class="editor-container" :style="{ height: editorHeight }">
        <sm-json-editor
          ref="jsonEditor"
          v-model="jsonValue"
          :showError="showError"
          :validate="validate"
          :height="editorHeight"
          :readonly="readonly"
          :indentSize="indentSize"
          @change="handleJsonChange"
        />
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessage" class="error-container">
        <a-alert
          :message="errorMessage"
          type="error"
          show-icon
          closable
          @close="clearError"
        />
      </div>
    </div>

    <!-- 自定义底部按钮 -->
    <div slot="footer" v-if="showFooter" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        @click="handlePreview"
        :disabled="!isValid || !jsonValue.trim()"
      >
        <i class="el-icon-view"></i>
        预览
      </el-button>
      <el-button
        type="primary"
        @click="handleOk"
        :loading="loading"
        :disabled="!isValid"
      >
        <i class="el-icon-check"></i>
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import JsonEditor from "./JsonEditor.vue";
import { validateJson } from "./validateJson";

export default {
  name: "JsonEditorModal",
  components: {
    "sm-json-editor": JsonEditor,
  },
  props: {
    // 显示控制
    visible: {
      type: Boolean,
      default: false,
    },
    // 标题
    title: {
      type: String,
      default: "JSON编辑器",
    },
    // 弹框宽度
    modalWidth: {
      type: [String, Number],
      default: "800px",
    },
    // JSON数据
    value: {
      type: String,
      default: "",
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: true,
    },
    // 是否显示错误信息
    showError: {
      type: Boolean,
      default: true,
    },
    // 是否进行验证
    validate: {
      type: Boolean,
      default: true,
    },
    // 编辑器高度
    editorHeight: {
      type: String,
      default: "400px",
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // 缩进大小
    indentSize: {
      type: Number,
      default: 2,
    },
    // 是否允许空值
    allowEmpty: {
      type: Boolean,
      default: false,
    },
    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      formatting: false,
      jsonValue: this.value || "",
      errorMessage: "",
      isValid: true,
      originalValue: this.value || "",
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    },
    value: {
      handler(val) {
        this.jsonValue = val || "";
        this.originalValue = val || "";
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化数据
    initData() {
      this.jsonValue = this.value || "";
      this.originalValue = this.value || "";
      this.errorMessage = "";
      this.isValid = true;
      this.$nextTick(() => {
        this.validateJson();
      });
    },

    // JSON变化处理
    handleJsonChange(value) {
      this.jsonValue = value;
      this.validateJson();
    },

    // 验证JSON
    validateJson() {
      if (!this.jsonValue.trim()) {
        this.isValid = this.allowEmpty;
        this.errorMessage = this.allowEmpty ? "" : "JSON内容不能为空";
        return;
      }

      try {
        JSON.parse(this.jsonValue);
        this.isValid = true;
        this.errorMessage = "";
      } catch (error) {
        this.isValid = false;
        this.errorMessage = `JSON格式错误: ${error.message}`;
      }
    },

    // 格式化JSON
    async handleFormat() {
      if (!this.jsonValue.trim()) {
        this.$message.warning("请先输入JSON内容");
        return;
      }

      this.formatting = true;
      try {
        const parsed = JSON.parse(this.jsonValue);
        this.jsonValue = JSON.stringify(parsed, null, this.indentSize);
        this.$message.success("格式化成功");
      } catch (error) {
        this.$message.error("JSON格式错误，无法格式化");
      } finally {
        this.formatting = false;
      }
    },

    // 压缩JSON
    handleCompress() {
      if (!this.jsonValue.trim()) {
        this.$message.warning("请先输入JSON内容");
        return;
      }

      try {
        const parsed = JSON.parse(this.jsonValue);
        this.jsonValue = JSON.stringify(parsed);
        this.$message.success("压缩成功");
      } catch (error) {
        this.$message.error("JSON格式错误，无法压缩");
      }
    },

    // 清空内容
    handleClear() {
      this.$confirm({
        title: "确认清空",
        content: "确定要清空所有内容吗？",
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          this.jsonValue = "";
          this.errorMessage = "";
          this.isValid = this.allowEmpty;
        },
      });
    },

    // 重置内容
    handleReset() {
      this.$confirm({
        title: "确认重置",
        content: "确定要重置为原始内容吗？",
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          this.jsonValue = this.originalValue;
          this.validateJson();
        },
      });
    },

    // 预览JSON
    handlePreview() {
      if (!this.isValid || !this.jsonValue.trim()) {
        this.$message.warning("请先输入有效的JSON内容");
        return;
      }

      try {
        const parsed = JSON.parse(this.jsonValue);
        this.$info({
          title: "JSON预览",
          width: 600,
          content: (h) => {
            return h(
              "pre",
              {
                style: {
                  background: "#f5f5f5",
                  padding: "16px",
                  borderRadius: "4px",
                  maxHeight: "400px",
                  overflow: "auto",
                  fontSize: "12px",
                  lineHeight: "1.5",
                },
              },
              JSON.stringify(parsed, null, 2)
            );
          },
        });
      } catch (error) {
        this.$message.error("预览失败");
      }
    },

    // 清除错误
    clearError() {
      this.errorMessage = "";
    },

    // 确定处理
    async handleOk() {
      if (!this.allowEmpty && !this.jsonValue.trim()) {
        this.$message.warning("JSON内容不能为空");
        return;
      }

      if (!this.isValid) {
        this.$message.warning("请修正JSON格式错误");
        return;
      }

      this.loading = true;
      try {
        // 验证JSON格式
        if (this.jsonValue.trim()) {
          JSON.parse(this.jsonValue);
        }

        // 触发成功事件
        this.$emit("ok", this.jsonValue);
        this.$emit("update:visible", false);
        this.$message.success("保存成功");
      } catch (error) {
        this.$message.error("JSON格式错误");
      } finally {
        this.loading = false;
      }
    },

    // 取消处理
    handleCancel() {
      this.$emit("cancel");
      this.$emit("update:visible", false);
      // 重置为原始值
      this.jsonValue = this.originalValue;
      this.errorMessage = "";
      this.isValid = true;
    },

    // 获取编辑器实例
    getEditor() {
      return this.$refs.jsonEditor;
    },

    // 设置JSON内容
    setValue(value) {
      this.jsonValue = value || "";
      this.originalValue = value || "";
      this.validateJson();
    },

    // 获取JSON内容
    getValue() {
      return this.jsonValue;
    },

    // 检查是否有效
    isValidJson() {
      return this.isValid;
    },
  },
};
</script>

<style lang="less" scoped>
.json-editor-modal-container {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #dcdfe6;

    .toolbar-buttons {
      display: flex;
      gap: 8px;
    }

    .status-info {
      display: flex;
      gap: 8px;
    }
  }

  .editor-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  .error-container {
    margin-top: 16px;
  }
}

// 弹框内容区域样式调整
:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 16px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

// 响应式处理
@media (max-width: 768px) {
  .json-editor-modal-container {
    .toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .status-info {
        justify-content: center;
      }
    }
  }
}
</style>
