<template>
  <div class="json-editor-container">
    <codemirror
      v-model="code"
      :options="cmOptions"
      @ready="onReady"
      @input="onChange"
      @keydown="handleKeydown"
    />

    <!-- 空状态提示 -->
    <div v-if="!code.trim()" class="empty-state">
      <i class="el-icon-document"></i>
      <p>请输入JSON数据</p>
      <p class="empty-tip">支持格式化、压缩、验证等功能</p>
    </div>

    <div class="tool-bar" :class="{ 'fullscreen-toolbar': isFullscreen }">
      <el-tooltip content="格式化JSON">
        <el-button
          type="primary"
          size="small"
          style="margin-top: 8px; margin-right: 8px"
          @click="formatJson"
          :loading="formatting"
          :disabled="!code.trim()"
        >
          <i class="el-icon-edit"></i>
        </el-button>
      </el-tooltip>
      <el-tooltip content="压缩JSON">
        <el-button
          type="primary"
          size="small"
          style="margin-top: 8px; margin-right: 8px"
          @click="compressJson"
          :disabled="!code.trim()"
        >
          <i class="el-icon-minus"></i>
        </el-button>
      </el-tooltip>
      <el-tooltip content="清空内容">
        <el-button
          type="danger"
          size="small"
          style="margin-top: 8px; margin-right: 8px"
          @click="clearContent"
          :disabled="!code.trim()"
        >
          <i class="el-icon-delete"></i>
        </el-button>
      </el-tooltip>
      <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'">
        <el-button
          type="primary"
          size="small"
          style="margin-top: 8px"
          @click="handleFullscreen"
        >
          <i
            :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
          ></i>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 错误信息显示 -->
    <div
      v-if="errorMessage && (showError || isFullscreen)"
      class="error-message"
      :class="{ 'fullscreen-error': isFullscreen }"
    >
      <i class="el-icon-warning"></i> {{ errorMessage }}
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" :class="{ 'fullscreen-status': isFullscreen }">
      <span class="status-item">
        <i
          class="el-icon-success"
          v-if="isValid && code.trim()"
          style="color: #67c23a"
        ></i>
        <i
          class="el-icon-error"
          v-else-if="!isValid && code.trim()"
          style="color: #f56c6c"
        ></i>
        <i class="el-icon-info" v-else style="color: #409eff"></i>
        {{ getStatusText() }}
      </span>
      <span class="status-item" v-if="code"> 字符数: {{ code.length }} </span>
      <span class="status-item" v-if="isValid && parsedData">
        层级: {{ getMaxDepth(parsedData) }}
      </span>
    </div>
  </div>
</template>

<script>
import { codemirror } from "vue-codemirror";
// 引入基础样式
import "codemirror/lib/codemirror.css";
// 引入主题样式
import "codemirror/theme/monokai.css";
// 引入JSON语言模式
import "codemirror/mode/javascript/javascript.js";
// 代码折叠
import "codemirror/addon/fold/foldgutter.css";
import "codemirror/addon/fold/foldcode.js";
import "codemirror/addon/fold/foldgutter.js";
import "codemirror/addon/fold/brace-fold.js";
// 括号匹配
import "codemirror/addon/edit/closebrackets.js";
import "codemirror/addon/edit/matchbrackets.js";
// 搜索和替换
import "codemirror/addon/search/searchcursor.js";
import "codemirror/addon/search/search.js";
// 全屏
import screenfull from "screenfull";

export default {
  name: "sm-json-editor",
  components: {
    codemirror,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    // 是否展示错误信息
    showError: {
      type: Boolean,
      default: true,
    },
    // 是否进行代码校验
    validate: {
      type: Boolean,
      default: true,
    },
    // 编辑器高度
    height: {
      type: String,
      default: "300px",
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // 缩进空格数
    indentSize: {
      type: Number,
      default: 2,
    },
    // 是否在初始化时自动格式化
    formatOnInit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      code: this.value || "",
      errorMessage: "",
      isValid: true,
      parsedData: null,
      formatting: false,
      isFullscreen: false,
      hasFormatted: false, // 添加格式化标志位
      cmOptions: {
        // 编辑器配置选项
        mode: "application/json", // JSON模式
        theme: "monokai", // 主题
        lineNumbers: true, // 显示行号
        line: true, // 显示行背景
        tabSize: this.indentSize, // Tab大小
        indentUnit: this.indentSize, // 缩进单位
        smartIndent: true, // 智能缩进
        matchBrackets: true, // 匹配括号
        autoCloseBrackets: true, // 自动闭合括号
        foldGutter: true, // 代码折叠
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        readOnly: this.readonly, // 只读模式
        height: this.height, // 设置高度
        extraKeys: {
          // 快捷键
          "Ctrl-Space": "autocomplete",
          "Ctrl-F": "findPersistent",
          "Ctrl-Alt-F": () => this.formatJson(), // 格式化快捷键
          "Ctrl-Alt-C": () => this.compressJson(), // 压缩快捷键
        },
        // JSON特定配置
        json: true,
        // 自动补全
        hintOptions: {
          completeSingle: false,
        },
        // 占位符
        placeholder: "请输入JSON数据...",
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.code) {
          this.code = val || "";
          // 重置格式化标志位，允许新的格式化
          this.hasFormatted = false;
          // 如果启用格式化且有内容，在数据更新后触发格式化
          if (this.formatOnInit && this.code.trim() && this.cm) {
            this.$nextTick(() => {
              this.autoFormatOnInit();
            });
          }
        }
      },
      immediate: true,
    },
    code(val) {
      this.$emit("input", val || "");
      this.$emit("change", val || "");
    },
    readonly(val) {
      if (this.cm) {
        this.cm.setOption("readOnly", val);
      }
    },
    height(val) {
      if (this.cm) {
        this.cm.setSize("auto", val);
      }
    },
  },
  mounted() {
    // 监听全屏变化事件
    if (screenfull.isEnabled) {
      screenfull.on("change", () => {
        this.isFullscreen = screenfull.isFullscreen;
      });
    }
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    if (screenfull.isEnabled) {
      screenfull.off("change");
    }
  },
  methods: {
    onReady(cm) {
      this.cm = cm;

      // 设置编辑器高度
      this.cm.setSize("auto", this.height);

      // 重置格式化标志位
      this.hasFormatted = false;

      if (this.validate) {
        this.validateJson();
      }

      // 如果启用初始化格式化且有内容，则自动格式化
      if (this.formatOnInit && this.code.trim()) {
        console.log(
          "准备执行初始化格式化，formatOnInit:",
          this.formatOnInit,
          "code长度:",
          this.code.length
        );
        // 使用setTimeout确保CodeMirror完全初始化
        setTimeout(() => {
          this.autoFormatOnInit();
        }, 100);
      } else {
        console.log(
          "跳过初始化格式化，formatOnInit:",
          this.formatOnInit,
          "code长度:",
          this.code.length
        );
      }
    },
    onChange(newCode, changeObj) {
      this.code = newCode;
      if (this.validate) {
        this.validateJson();
      }
    },
    handleKeydown(cm, event) {
      // 处理特殊快捷键
      if (event.ctrlKey && event.altKey) {
        switch (event.key) {
          case "f":
          case "F":
            event.preventDefault();
            this.formatJson();
            break;
          case "c":
          case "C":
            event.preventDefault();
            this.compressJson();
            break;
        }
      }
    },
    // 初始化时自动格式化
    autoFormatOnInit() {
      if (!this.code.trim() || !this.cm || this.hasFormatted) {
        return;
      }

      try {
        // 先验证JSON格式
        const parsed = JSON.parse(this.code);
        // 格式化JSON
        const formatted = JSON.stringify(parsed, null, this.indentSize);

        // 检查是否需要格式化（避免对已经格式化的内容重复格式化）
        if (formatted === this.code) {
          this.hasFormatted = true;
          return;
        }

        // 直接设置CodeMirror的值，避免触发watch循环
        this.cm.setValue(formatted);
        // 更新内部状态
        this.code = formatted;
        this.hasFormatted = true;

        // 触发事件
        this.$emit("autoFormatted", formatted);
        this.$emit("input", formatted);
        this.$emit("change", formatted);

        console.log("初始化格式化完成");
      } catch (e) {
        // 如果格式化失败，不显示错误信息，因为这是初始化操作
        console.warn("初始化格式化失败:", e.message);
      }
    },
    validateJson() {
      if (!this.code.trim()) {
        this.clearError();
        this.isValid = true;
        this.parsedData = null;
        return;
      }

      try {
        this.parsedData = JSON.parse(this.code);
        this.isValid = true;
        this.clearError();
      } catch (e) {
        this.isValid = false;
        this.parsedData = null;
        this.errorMessage = `JSON格式错误: ${e.message}`;
        this.addErrorClass();
      }
    },
    clearError() {
      this.errorMessage = "";
      this.removeErrorClass();
    },
    addErrorClass(className = "invalid") {
      if (this.cm) {
        this.cm.getWrapperElement().classList.add(className);
      }
    },
    removeErrorClass(className = "invalid") {
      if (this.cm) {
        this.cm.getWrapperElement().classList.remove(className);
      }
    },
    async formatJson() {
      if (!this.code.trim()) {
        this.$message.warning("请输入JSON内容");
        return;
      }

      this.formatting = true;
      try {
        // 先验证JSON格式
        const parsed = JSON.parse(this.code);
        // 格式化JSON
        const formatted = JSON.stringify(parsed, null, this.indentSize);
        this.code = formatted;
        this.$message.success("格式化成功");
        this.$emit("formatted", formatted);
      } catch (e) {
        this.$message.error(`格式化失败: ${e.message}`);
      } finally {
        this.formatting = false;
      }
    },
    compressJson() {
      if (!this.code.trim()) {
        this.$message.warning("请输入JSON内容");
        return;
      }

      try {
        // 先验证JSON格式
        const parsed = JSON.parse(this.code);
        // 压缩JSON（去除所有空格）
        const compressed = JSON.stringify(parsed);
        this.code = compressed;
        this.$message.success("压缩成功");
        this.$emit("compressed", compressed);
      } catch (e) {
        this.$message.error(`压缩失败: ${e.message}`);
      }
    },
    clearContent() {
      this.$confirm({
        title: "确认清空",
        content: "确定要清空所有内容吗？",
        onOk: () => {
          this.code = "";
          this.$message.success("内容已清空");
          this.$emit("cleared");
        },
      });
    },
    handleFullscreen() {
      if (!screenfull.isEnabled) {
        this.$message.warning("当前浏览器不支持全屏");
        return;
      }
      const editorElement = this.cm.getWrapperElement();
      if (!this.isFullscreen) {
        screenfull.request(editorElement);
      } else {
        screenfull.exit();
      }
      // 触发重新渲染以调整大小
      this.cm.refresh();
    },
    // 获取JSON最大层级深度
    getMaxDepth(obj, depth = 0) {
      if (typeof obj !== "object" || obj === null) {
        return depth;
      }

      let maxDepth = depth;
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const currentDepth = this.getMaxDepth(obj[key], depth + 1);
          maxDepth = Math.max(maxDepth, currentDepth);
        }
      }
      return maxDepth;
    },
    // 获取验证结果
    getValidationResult() {
      return {
        valid: this.isValid,
        errorMessage: this.errorMessage,
        parsedData: this.parsedData,
      };
    },
    // 获取状态文本
    getStatusText() {
      if (!this.code.trim()) {
        return "请输入JSON数据";
      }
      if (this.isValid && this.code.trim()) {
        return "JSON格式正确";
      }
      if (!this.isValid && this.code.trim()) {
        return "JSON格式错误";
      }
      return "请输入JSON数据";
    },
    // 设置内容
    setValue(value = "") {
      if (this.cm) {
        this.cm.setValue(value);
        this.code = value;
      }
    },
    // 获取内容
    getValue() {
      return this.code;
    },
    // 插入JSON片段
    insertJsonSnippet(snippet) {
      if (this.cm) {
        const cursor = this.cm.getCursor();
        this.cm.replaceRange(snippet, cursor);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.json-editor-container {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;

  .CodeMirror {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 14px;
    line-height: 1.5;
    min-height: 200px; // 设置最小高度

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
      z-index: 9999;
    }
  }

  .invalid {
    background: #fff1f0;
    border-color: #ff4d4f;
  }

  .empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #999;
    padding: 20px;
    z-index: 1;
    pointer-events: none;
    user-select: none;

    .anticon {
      font-size: 48px;
      margin-bottom: 12px;
      opacity: 0.6;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .empty-tip {
      font-size: 12px;
      margin-top: 8px;
      opacity: 0.7;
    }
  }

  .tool-bar {
    position: absolute;
    top: 8px;
    right: 16px;
    z-index: 2;
    transition: all 0.3s;

    &.fullscreen-toolbar {
      position: fixed;
      top: 20px;
      right: 30px;
      z-index: 10001;
    }
  }

  .error-message {
    margin-top: 8px;
    padding: 8px 12px;
    background: #fff1f0;
    border: 1px solid #ffccc7;
    border-radius: 4px;
    color: #f5222d;

    &.fullscreen-error {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10001;
      background: rgba(0, 0, 0, 0.8);
      color: #fff !important;
      padding: 8px 16px;
      border-radius: 4px;
    }
  }

  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px;
    background: #fafafa;
    border-top: 1px solid #d9d9d9;
    font-size: 12px;
    color: #666;

    &.fullscreen-status {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10001;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      border-top: 1px solid #333;
    }

    .status-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

// 全屏模式下的样式调整
.json-editor-container.fullscreen {
  .CodeMirror {
    height: calc(100vh - 60px); // 减去状态栏高度
  }
}
</style>
