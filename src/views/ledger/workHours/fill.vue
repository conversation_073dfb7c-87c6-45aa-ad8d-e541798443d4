<!-- 工时填报 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['workHours:fill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['workHours:fill:batchAdd']"
          >导入</el-button
        >
      </template>
      <template #workRangeTime>
        <el-row>
          <el-col :span="6">
            <el-select
              v-model="params.rangeType"
              @change="handleRangeTypeChange"
            >
              <el-option label="周" value="week"> </el-option>
              <el-option label="月" value="month"> </el-option>
              <el-option label="年" value="year"> </el-option>
            </el-select>
          </el-col>
          <el-col :span="15" style="margin-left: 10px;">
            <el-date-picker
              type="week"
              valueFormat="yyyy-MM-dd"
              format="yyyy 第 WW 周"
              :pickerOptions="{
                firstDayOfWeek: 1,
              }"
              size="small"
              v-model="params.workRangeTime"
              v-if="params.rangeType === 'week'"
              key="week"
              placeholder="选择周"
            ></el-date-picker>
            <el-date-picker
              type="monthrange"
              valueFormat="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              v-model="params.workRangeTime"
              v-else-if="params.rangeType === 'month'"
              key="month"
            ></el-date-picker>
            <el-date-picker
              type="year"
              valueFormat="yyyy-MM-dd"
              v-model="params.workRangeTime"
              v-else-if="params.rangeType === 'year'"
              key="year"
              placeholder="选择年"
            ></el-date-picker>
          </el-col>
        </el-row>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      uploadApi="/workHours/fill/batchAdd"
      ref="batchUpload"
      title="批量导入工时"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
    <el-dialog
      title="工作时长"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="visible = false"
      append-to-body
      width="50%"
    >
      <GridTable
        ref="gridTable"
        :columns="workColumns"
        :tableData="workTableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryWorkData"
        :loading="workLoading"
        :tableId="tableId"
      >
      </GridTable>
    </el-dialog>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/workHours/fill.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listDept } from "@/api/common.js";
import BatchUpload from "./components/batchUpload.vue";
import moment from "moment";

export default {
  name: "ledgerList",
  components: { BatchUpload, GridTable },
  mixins: [exportMixin],
  data() {
    return {
      workLoading: false,
      tableId: "workHoursDetailList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      workColumns: [
        {
          title: "项目类型",
          field: "projectType",
        },
        {
          title: "工时类型",
          field: "workHourType",
        },
        {
          title: "工作日期",
          field: "workDate",
        },
        {
          title: "工作时长（h）",
          field: "workHour",
        },
        {
          title: "月工作天数（天）",
          field: "monthWorkDay",
        },
        {
          title: "工时备注",
          field: "remark",
        },
      ],
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      deptOptions: [],
      checkStatusOptions: [],
      workHourTypeOptions: [],
      projectTypeOptions: [],
      workTableData: [],
      sourceOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.getDicts("work_hour_status").then((response) => {
      this.checkStatusOptions = response.data;
    });
    this.getDicts("work_hour_source").then((response) => {
      this.sourceOptions = response.data;
    });
    this.getDicts("work_hour_type").then((response) => {
      this.workHourTypeOptions = response.data;
    });
    this.getDicts("work_hour_project_type").then((response) => {
      this.projectTypeOptions = response.data;
    });

    this.getTreeselect();
    // this.loadData();
  },
  methods: {
    checkPermission,
    //打开工作时长抽屉
    handleDetail(row) {
      this.visible = true;
      this.workTableData = [];
      this.searchForm.pageNum = 1;
      this.searchForm.recordId = row.recordId;
      this.queryWorkData();
    },
    //获取列表
    queryWorkData() {
      this.workLoading = true;
      let params = {
        ...this.searchForm,
      };
      api
        .detail(params)
        .then((res) => {
          this.workLoading = false;
          this.workTableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.workLoading = false;
        });
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      if (params.rangeType === "week") {
        params.workHourTimeStart =
          moment(params.workRangeTime)
            .isoWeekday(1)
            .format("YYYY-MM-DD") + " 00:00:00";
        params.workHourTimeEnd =
          moment(params.workRangeTime)
            .isoWeekday(7)
            .format("YYYY-MM-DD") + " 23:59:59";
      } else if (params.rangeType === "month") {
        params.workHourTimeStart =
          moment(params.workRangeTime[0])
            .startOf("month")
            .format("YYYY-MM-DD") + " 00:00:00";
        params.workHourTimeEnd =
          moment(params.workRangeTime[1])
            .endOf("month")
            .format("YYYY-MM-DD") + " 23:59:59";
      } else if (params.rangeType === "year") {
        params.workHourTimeStart =
          moment(params.workRangeTime)
            .startOf("year")
            .format("YYYY-MM-DD") + " 00:00:00";
        params.workHourTimeEnd =
          moment(params.workRangeTime)
            .endOf("year")
            .format("YYYY-MM-DD") + " 23:59:59";
      }
      // const arr = [
      //   {
      //     field: "workRangeTime",
      //     title: "工时周期",
      //     startFieldName: "activityStartDate",
      //     endFieldName: "activityEndDate",
      //   },
      // ];
      // arr.map((x) => {
      //   if (Array.isArray(params[x.field])) {
      //     params[x.startFieldName] = params[x.field][0] + " 00:00:00";
      //     params[x.endFieldName] = params[x.field][1] + " 23:59:59";
      //     delete params[x.field];
      //   }
      // });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.$refs.crud?.$refs.autoFilters?.$refs.ruleForm?.validate((valid) => {
        if (valid) {
          this.tablePage.currentPage = 1;
          this.loadData();
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.flattenArray(response.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    handleRangeTypeChange() {
      this.params.workRangeTime = undefined;
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "userName",
          title: "姓名",
          width: 180,
        },
        {
          field: "workNumber",
          title: "工号",
          width: 120,
        },
        {
          field: "workHourSource",
          title: "工时来源",
          width: 120,
        },
        {
          field: "deptName",
          title: "部门名称",
          width: 120,
        },
        {
          field: "projectType",
          title: "项目类型",
          width: 120,
        },
        {
          field: "projectCode",
          title: "项目编码",
          width: 120,
        },
        {
          field: "workHourCode",
          title: "工时编码",
          width: 120,
        },
        {
          field: "workHourName",
          title: "工时名称",
          width: 120,
        },
        {
          field: "workHourHome",
          title: "工时归属",
          width: 120,
        },
        {
          field: "projectProfitCenter",
          title: "项目利润中心",
          width: 120,
        },
        {
          field: "supportWorkHourCode",
          title: "支持工时编码",
          width: 120,
        },
        {
          field: "supportWorkHourName",
          title: "支持工时名称",
          width: 120,
        },
        {
          field: "workHourType",
          title: "工时类型",
          width: 120,
        },
        {
          field: "startTime",
          title: "工时开始日期",
          width: 120,
        },
        {
          field: "endTime",
          title: "工时结束日期",
          width: 120,
        },
        {
          field: "workHour",
          title: "工作时长（h）",
          width: 120,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleDetail(row),
                  }}
                  props={{
                    disabled: row.workHourSource != "运管",
                  }}
                >
                  {row.workHour || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "remark",
          title: "工时备注",
          width: 120,
        },
        {
          field: "status",
          title: "审批状态",
          width: 120,
        },
        {
          field: "applyNumber",
          title: "申请单号",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "userName",
            element: "el-input",
            title: "姓名",
            attrs: {
              placeholder: "姓名或工号查询",
            },
          },
          {
            field: "source",
            title: "工时来源",
            element: "el-select",
            props: {
              options: this.sourceOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "deptId",
            title: "部门名称",
            element: "el-select",
            props: {
              options: this.deptOptions,
              optionLabel: "deptName",
              optionValue: "deptId",
              filterable: true,
              defaultExpandLevel: 1,
            },
          },
          {
            field: "status",
            title: "审批状态",
            element: "el-select",
            props: {
              options: this.checkStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "rangeType",
            title: "周期维度",
            show: false,
            defaultValue: "week",
          },
          {
            field: "workRangeTime",
            title: "工时周期",
            element: "slot",
            slotName: "workRangeTime",
            props: {},
            colSpan: { span: 16 },
            defaultValue: moment()
              .subtract(1, "week")
              .format("YYYY-MM-DD"),
            rules: [
              { required: true, message: "请选择工时周期", trigger: "change" },
            ],
          },
          {
            field: "workHourCode",
            element: "el-input",
            title: "工时编码",
            attrs: {
              placeholder: "一级工时编码或一级工时名称",
            },
          },
          {
            field: "supportWorkHourCode",
            element: "el-input",
            title: "支持编码",
            attrs: {
              placeholder: "二级工时编码或二级工时名称",
            },
          },
          {
            field: "workHourType",
            title: "工时类型",
            element: "el-select",
            props: {
              options: this.workHourTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "projectType",
            title: "项目类型",
            element: "el-select",
            props: {
              options: this.projectTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
