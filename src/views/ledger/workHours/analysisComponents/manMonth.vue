<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      style="display: flex;justify-content: flex-end;"
    >
      <el-form-item label="" prop="timeRange">
        <el-date-picker
          v-model="form.timeRange"
          type="monthrange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          style="width: 360px;"
          @change="loadData"
          value-format="yyyy-MM"
          :clearable="false"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-card
      style="margin-bottom: 10px;overflow: visible;"
      v-loading="loading1"
    >
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各部门人力投入情况分析</span>
        <el-tooltip effect="dark" :content="1" placement="top">
          <div slot="content">
            <p>
              人月=筛选月份的填报工时h/筛选月份工作日天数，取整数，四舍五入
            </p>
            <p>
              人月环比=（筛选月份的人月－筛选月份的上月人月）/筛选月份的上月人月*100%，小数点后保留2位，四舍五入
            </p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <TwoYaxis
        :axisData="deptChartObj.dept"
        :serieData="deptChartObj.tendencyArr"
        chartStyle="height:320px"
        v-if="deptChartObj.dept && deptChartObj.dept.length > 0"
        isSmooth
        width="calc(100vw - 280px)"
      ></TwoYaxis>
      <el-empty v-else></el-empty>
    </el-card>
    <el-card
      style="margin-bottom: 10px;overflow: visible;"
      v-loading="loading2"
    >
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各业务人力投入情况分析</span>
        <el-tooltip effect="dark" :content="1" placement="top">
          <div slot="content">
            <p>
              人月=筛选月份的填报工时h/筛选月份工作日天数，取整数，四舍五入
            </p>
            <p>
              人月环比=（筛选月份的人月－筛选月份的上月人月）/筛选月份的上月人月*100%，小数点后保留2位，四舍五入
            </p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <TwoYaxis
        :axisData="businessChartObj.business"
        :serieData="businessChartObj.tendencyArr"
        chartStyle="height:320px"
        v-if="businessChartObj.business && businessChartObj.business.length > 0"
        isSmooth
        width="calc(100vw - 280px)"
      ></TwoYaxis>
      <el-empty v-else></el-empty>
    </el-card>
    <el-card
      style="margin-bottom: 10px;overflow: visible;"
      v-loading="loading3"
    >
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各业务人力投入趋势</span>
      </div>
      <LineChart
        :axisData="tendObj.time"
        :serieData="tendObj.tendencyArr"
        v-if="tendObj.time && tendObj.time.length > 0"
        height="300px"
        unit=""
        width="calc(100vw - 280px)"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
  </div>
</template>

<script>
import moment from "moment";
import TwoYaxis from "@/components/Echarts/twoYaxis.vue";
import LineChart from "@/components/Echarts/LineChart.vue";
import api from "@/api/ledger/workHours/analysis.js";

export default {
  components: {
    TwoYaxis,
    LineChart,
  },
  data() {
    return {
      tendObj: {
        time: [],
        tendencyArr: [
          // {
          //   name: "心电图",
          //   data: [1, 2, 3, 4, 5],
          // },
          // {
          //   name: "生活缴费",
          //   data: [10, 20, 30, 40, 50],
          // },
        ],
      },
      businessChartObj: {
        business: [],
        tendencyArr: [
          // {
          //   name: "人月",
          //   data: [1, 2, 3, 4, 5],
          //   showYAxis: true,
          //   yAxisIndex: 0,
          //   unit: "人月",
          //   type: "bar",
          //   barWidth: "50px",
          // },
          // {
          //   name: "环比",
          //   data: [10, 30, 20, 40, 60],
          //   showYAxis: true,
          //   yAxisIndex: 1,
          //   unit: "环比(%)",
          // },
        ],
      },
      deptChartObj: {
        dept: [],
        tendencyArr: [
          // {
          //   name: "人月",
          //   data: [1, 2, 3, 4, 5],
          //   showYAxis: true,
          //   yAxisIndex: 0,
          //   unit: "人月",
          //   type: "bar",
          //   barWidth: "50px",
          // },
          // {
          //   name: "环比",
          //   data: [10, 30, 20, 40, 60],
          //   showYAxis: true,
          //   yAxisIndex: 1,
          //   unit: "环比(%)",
          // },
        ],
      },
      form: {
        timeRange: [
          moment()
            .subtract(1, "month")
            .startOf("month")
            .format("yyyy-MM"),
          moment()
            .subtract(1, "month")
            .startOf("month")
            .format("yyyy-MM"),
        ],
      },
      loading1: false,
      loading2: false,
      loading3: false,
    };
  },
  computed: {
    pickerOptions() {
      return {
        shortcuts: [
          {
            text: "最近3个月",
            onClick(picker) {
              const start = moment()
                .subtract(2, "month")
                .startOf("month")
                .format("yyyy-MM");
              const end = moment()
                .endOf("month")
                .format("yyyy-MM");
              picker.$emit("pick", [start, end]); // 直接传递格式化字符串
            },
          },
          {
            text: "最近6个月",
            onClick(picker) {
              const start = moment()
                .subtract(5, "month")
                .startOf("month")
                .format("yyyy-MM");
              const end = moment()
                .endOf("month")
                .format("yyyy-MM");
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近1年",
            onClick(picker) {
              const start = moment()
                .subtract(11, "month")
                .startOf("month")
                .format("yyyy-MM");
              const end = moment()
                .endOf("month")
                .format("yyyy-MM");
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      };
    },
  },
  methods: {
    loadData() {
      this.monthDeptData();
      this.monthBusinessData();
      this.monthPersonData();
    },
    async monthDeptData() {
      this.loading1 = true;

      const res = await api
        .monthDeptData({
          startDate: moment(this.form.timeRange[0])
            ?.startOf("month")
            .format("YYYY-MM-DD"),
          endDate: moment(this.form.timeRange[1])
            ?.endOf("month")
            .format("YYYY-MM-DD"),
        })
        .finally(() => {
          this.loading1 = false;
        });
      this.deptChartObj = {
        dept: res.data?.map((x) => x.deptName),
        tendencyArr: [
          {
            name: "人月",
            data: res.data?.map((x) => x.mouthHour),
            showYAxis: true,
            yAxisIndex: 0,
            unit: "人月",
            type: "bar",
            barWidth: "50px",
          },
          {
            name: "环比",
            data: res.data?.map((x) => x.percent),
            showYAxis: true,
            yAxisIndex: 1,
            unit: "环比(%)",
          },
        ],
      };
    },
    async monthBusinessData() {
      this.loading2 = true;

      const res = await api
        .monthBusinessData({
          startDate: moment(this.form.timeRange[0])
            ?.startOf("month")
            .format("YYYY-MM-DD"),
          endDate: moment(this.form.timeRange[1])
            ?.endOf("month")
            .format("YYYY-MM-DD"),
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.businessChartObj = {
        business: res.data?.map((x) => x.businessTypeName),
        tendencyArr: [
          {
            name: "人月",
            data: res.data?.map((x) => x.mouthHour),
            showYAxis: true,
            yAxisIndex: 0,
            unit: "人月",
            type: "bar",
            barWidth: "50px",
          },
          {
            name: "环比",
            data: res.data?.map((x) => x.percent),
            showYAxis: true,
            yAxisIndex: 1,
            unit: "环比(%)",
          },
        ],
      };
    },
    async monthPersonData() {
      this.loading3 = true;

      const res = await api
        .monthPersonData({
          startDate: moment(this.form.timeRange[0])
            ?.startOf("month")
            .format("YYYY-MM-DD"),
          endDate: moment(this.form.timeRange[1])
            ?.endOf("month")
            .format("YYYY-MM-DD"),
        })
        .finally(() => {
          this.loading3 = false;
        });
      this.tendObj = {
        time: res.data?.[0]?.tendencyArr?.map((item) => {
          return item.day;
        }),
        tendencyArr: res.data?.map((item) => {
          return {
            name: item.businessTypeName,
            data: item.tendencyArr.map((val) => {
              return val.jobHour;
            }),
          };
        }),
      };
    },
  },
};
</script>

<style></style>
