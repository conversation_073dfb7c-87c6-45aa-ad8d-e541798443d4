<template>
  <el-card>
    <div slot="header" class="card-title-wrap">
      <div class="card-title-line"></div>
      <span>数据明细</span>
    </div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #timeRange>
        <el-row type="flex">
          <el-date-picker
            v-model="params.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 48%;"
            :clearable="false"
          ></el-date-picker>

          <el-radio-group
            v-model="params.timeRange"
            size="mini"
            style="width: 48%;"
          >
            <el-radio-button
              v-for="(x, i) in timeArr"
              :label="x.date"
              :key="i"
              >{{ x.title }}</el-radio-button
            >
          </el-radio-group>
        </el-row>
      </template>
    </BuseCrud>
  </el-card>
</template>

<script>
import moment from "moment";
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/workHours/analysis.js";
import { initParams } from "@/utils/buse.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { listDept, listAllUser } from "@/api/common.js";
export default {
  data() {
    return {
      //buse参数-s

      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 20,
      },
      operationType: "accept",
      //buse参数-e
      deptOptions: [],
      businessTypeOptions: [],
    };
  },
  computed: {
    tableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          //   slots: {
          //     buttons: "toolbar_buttons",
          //   },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
        footerMethod: this.footerMethod,
        footerSpanMethod: this.footerMergeMethod,
        showFooter: true,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 150,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
    tableColumn() {
      return [
        {
          field: "businessTypeStr",
          title: "业务类型",
        },
        {
          field: "deptName",
          title: "部门名称",
        },
        {
          field: "userName",
          title: "姓名",
        },
        {
          field: "fillJobHour",
          title: "填报工时（h）",
        },
        {
          field: "orderJobHour",
          title: "订单制工时（h）",
        },
        {
          field: "redundancyJobHour",
          title: "人力冗余时间（h）",
        },
        {
          field: "differencePercent",
          title: "差额幅度（%）",
        },
        {
          field: "differencePeople",
          title: "冗余人力",
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "timeRange",
            title: "时间",
            element: "slot",
            slotName: "timeRange",
            defaultValue: [
              moment()
                .subtract(29, "days")
                .format("YYYY-MM-DD"),
              moment().format("YYYY-MM-DD"),
            ],
            colSpan: { span: 24 },
          },
          {
            field: "businessTypes",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
              filterable: true,
              // collapseTags: true,
              multiple: true,
            },
          },
          {
            field: "deptIds",
            title: "部门名称",
            element: "el-select",
            props: {
              options: this.deptOptions,
              filterable: true,
              multiple: true,
              optionLabel: "deptName",
              optionValue: "deptId",
              defaultExpandLevel: 1,
            },
          },
          {
            field: "userIds",
            title: "姓名",
            element: "el-select",
            props: {
              options: this.userOptions,
              filterable: true,
              // collapseTags: true,
              multiple: true,
            },
          },
        ],
        params: this.params,
      };
    },
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.getTreeselect();
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    this.listAllUser();
    // this.loadData();
  },
  methods: {
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    async loadData() {
      console.log("loadData");
      const { timeRange } = this.params;
      let params = {
        ...this.params,
        startDate: timeRange[0],
        endDate: timeRange[1],
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const res = await api.detailList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 20,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.flattenArray(response.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    // 合并单元格方法（关键）
    footerMergeMethod({ $rowIndex, columnIndex }) {
      if ($rowIndex === 0) {
        // 只在表尾行生效
        if (columnIndex <= 2) {
          // 合并前三列
          return {
            rowspan: 1,
            colspan: columnIndex === 0 ? 3 : 0, // 首列合并3列，其他列隐藏
          };
        }
      }
    },

    // 表尾计算逻辑
    footerMethod() {
      return [
        [
          "合计",
          "",
          "",
          this.sumColumn("fillJobHour"),
          this.sumColumn("orderJobHour"),
          this.sumColumn("redundancyJobHour"),
          this.sumColumn("differencePercent"),
          this.sumColumn("differencePeople"),
        ],
      ];
    },
    // 通用列求和
    sumColumn(field) {
      const sum = this.tableData?.reduce((sum, row) => {
        const value = Number(row[field]) || 0;
        // 使用 parseFloat 和 toFixed 来确保精度
        return parseFloat((sum + value).toFixed(2));
      }, 0);
      return parseFloat(sum.toFixed(2)); // 最后再处理一次，确保返回值的精度
    },
  },
};
</script>

<style></style>
