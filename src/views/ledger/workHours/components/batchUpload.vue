<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleBatchCancel"
  >
    <el-form :model="batchForm" ref="batchForm" label-width="140px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="工时来源"
            prop="source"
            :rules="[{ required: true }]"
          >
            <el-radio-group
              v-model="batchForm.source"
              @change="handleSourceChange"
            >
              <el-radio label="1">运营</el-radio>
              <el-radio label="0">禅道</el-radio>
            </el-radio-group>
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            :label="
              batchForm.source === '1'
                ? '运管工时数据上传:'
                : '禅道工时数据上传:'
            "
            prop="files"
          >
            <el-upload
              ref="operationUpload"
              :limit="5"
              multiple
              accept=".xlsx, .xls"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-change="handleChangeFile"
              :auto-upload="false"
              :http-request="() => {}"
              :file-list="batchForm.files"
            >
              <el-button>选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                <slot name="tip">
                  上传格式支持xlxs、xls文件，单个文件{{
                    maxSizeText
                  }}以内，最多上传5个文件。
                </slot>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <slot name="extraForm" :params="extraData"></slot>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleBatchCancel"
        size="small"
        :loading="submitLoading"
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="handleBatchSubmit"
        size="small"
        :loading="submitLoading"
        >开始导入</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import api from "@/api/ledger/workHours/fill.js";

export default {
  props: {
    title: {
      type: String,
      default: "批量导入",
    },
    //上传时附带的额外参数
    extraData: {
      type: Object,
      default: () => {},
    },
    //上传接口
    uploadApi: {
      type: String,
      default: "",
      required: true,
    },
    //表格模版路径 eg:'/charging-maintenance-ui/static/公司机构导入模板.xlsx'
    templateUrl: {
      type: String,
      default: "",
    },
    maxSize: {
      type: Number,
      default: 2,
    },
    maxSizeText: {
      type: String,
      default: "2G",
    },
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      batchForm: {
        files: [], // 改为files
        source: "1",
      },
    };
  },
  computed: {
    upload() {
      let baseUrl = process.env.VUE_APP_BASE_API;
      return {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + this.uploadApi,
        updateAsCode: "",
      };
    },
  },
  methods: {
    handleSourceChange() {
      this.batchForm.files = [];
      this.$refs.operationUpload.clearFiles();
    },
    open() {
      this.visible = true;
    },
    handleChangeFile(file, fileList) {
      this.batchForm.files = fileList || [];
    },
    downExcel() {
      window.location.href = this.templateUrl;
    },
    async handleBatchSubmit() {
      if (this.batchForm.files?.length === 0) {
        this.$message.error("请上传至少一个文件！");
        return;
      }

      // 检查所有文件大小
      const checkFileSize = (file) => {
        if (file && file.size / 1024 / 1024 / 1024 > this.maxSize) {
          this.$message.error(
            `文件 ${file.name} 大小不能超过${this.maxSizeText}!`
          );
          return false;
        }
        return true;
      };

      // 检查所有文件
      for (const fileInfo of this.batchForm.files) {
        if (!checkFileSize(fileInfo)) {
          return;
        }
      }

      this.submitLoading = true;

      try {
        // 创建 FormData
        const formData = new FormData();

        // 添加所有文件
        this.batchForm.files.forEach((fileInfo, index) => {
          formData.append(`file`, fileInfo.raw);
        });

        formData.append("source", this.batchForm.source);

        // 发送请求
        const response = await api.batchImport(formData);

        if (response.success) {
          this.handleBatchCancel();
          this.$alert("导入成功", "导入结果", {
            type: "success",
            confirmButtonText: "我知道了",
            callback: () => {
              this.$emit("uploadSuccess");
            },
          });
        } else {
          this.$confirm(response.message, "导入失败！", {
            confirmButtonText: "重新上传",
            cancelButtonText: "取消",
            type: "error",
            center: true,
            dangerouslyUseHTMLString: true,
          })
            .then(() => {
              this.batchForm.files = [];
              this.$refs.operationUpload.clearFiles();
            })
            .catch(() => {
              this.handleBatchCancel();
              if (response.code == "60001") {
                this.$emit("uploadSuccess");
              }
            });
        }
      } catch (error) {
        this.$message.error("上传失败：" + (error.message || "未知错误"));
      } finally {
        this.submitLoading = false;
      }
    },
    handleBatchCancel() {
      this.visible = false;
      this.$refs.batchForm.resetFields();
      this.batchForm.files = [];
      this.$refs.operationUpload.clearFiles();
    },
  },
};
</script>

<style></style>
