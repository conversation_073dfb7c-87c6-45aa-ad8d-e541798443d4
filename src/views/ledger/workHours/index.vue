<!-- 工时管理 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <FillPage
      v-show="tabActiveTab === 'fill'"
      ref="fill"
      :searchParams="searchParams"
    ></FillPage>
    <OrderPage
      v-show="tabActiveTab === 'order'"
      ref="order"
      @jump="handleJump"
    ></OrderPage>
    <AccountPage
      v-show="tabActiveTab === 'account'"
      ref="account"
    ></AccountPage>
    <AnalysisPage
      v-show="tabActiveTab === 'analysis'"
      ref="analysis"
    ></AnalysisPage>
    <CodePage v-show="tabActiveTab === 'code'" ref="code"></CodePage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import FillPage from "./fill.vue";
import OrderPage from "./order.vue";
import AccountPage from "./account.vue";
import AnalysisPage from "./analysis.vue";
import CodePage from "./code.vue";
export default {
  name: "ledgerWorkHours",
  components: { FillPage, OrderPage, AccountPage, AnalysisPage, CodePage },
  data() {
    return {
      tabActiveTab: "fill",
      topTabDict: [
        {
          value: "fill",
          label: "填报工时",
          show: () => {
            return this.checkPermission(["workHours:fill:list"]);
          },
        },
        {
          value: "order",
          label: "订单制工时",
          show: () => {
            return this.checkPermission(["workHours:order:list"]);
          },
        },
        {
          value: "account",
          label: "工时核算",
          show: () => {
            return this.checkPermission(["workHours:account:list"]);
          },
        },
        {
          value: "analysis",
          label: "数据分析",
          show: () => {
            return this.checkPermission(["workHours:analysis:list"]);
          },
        },
        {
          value: "code",
          label: "工时编码",
          show: () => {
            return this.checkPermission(["workHours:code:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    // if (Object.keys(this.$route.params)?.length > 0) {
    //   this.params = { ...this.params, ...this.$route.params };
    // }
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // this.tabActiveTab = "originalData";
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
