<!-- 工时编码 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['workHours:code:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchMark"
          v-has-permi="['workHours:code:batchMark']"
          >批量标记</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['workHours:code:batchAdd']"
          >导入</el-button
        >
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入工时编码"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/workHours/code.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listDept } from "@/api/common.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import Timeline from "@/components/Timeline/index.vue";

import moment from "moment";
import { queryTreeList } from "@/api/ledger/businessType.js";

export default {
  name: "ledgerList",
  components: { BatchUpload, Timeline },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/workHour/code/importExcel",
        url: "/charging-maintenance-ui/static/工时编码导入模版.xlsx",
        extraData: {},
      },

      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "mailId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      workBelongOptions: [],
      businessTypeOptions: [],
      recordList: [],
      selectedData: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };

    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    // 下拉菜单类型，00工时归属，01对应业务类型
    api.getDropdownList({ dropDownType: "00" }).then((res) => {
      this.workBelongOptions = res.data?.map((x) => {
        return { value: x.name, label: x.name };
      });
    });
    // this.loadData();
  },
  methods: {
    checkPermission,
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    handleBatchMark() {
      if (this.selectedData?.length == 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.operationType = "batchMark";
      this.$refs.crud.switchModalView(true, "batchMark", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    handleLog(row) {
      this.operationType = "log";
      api.queryLog({ businessId: row.pkWorkHourCodeId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    rowEdit(row) {
      this.operationType = "update";
      // 下拉菜单类型，00工时归属，01对应业务类型
      api
        .getDropdownList({ dropDownType: "01", businessType: row.businessType })
        .then((res) => {
          this.businessTypeOptions = res.data?.map((x) => {
            return { id: x.id, typeName: x.name };
          });
        });
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        workHourCodeId: row.pkWorkHourCodeId,
        orderType: row.orderType == "否" ? "0" : "1",
      });
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "operateTime",
          title: "操作时间",
          startFieldName: "operateTimeStart",
          endFieldName: "operateTimeEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        if (crudOperationType === "batchMark") {
          params = {
            ...params,
            workHourCodeIdList: this.selectedData?.map(
              (x) => x.pkWorkHourCodeId
            ),
          };
        }
        // crudOperationType:update/batchMark
        const res = await api[crudOperationType](params).catch(() => {
          resolve(false);
        });
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          if (crudOperationType === "batchMark") {
            this.$refs.crud.tableDeselectHandler();
          }
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },

    querySearch(queryString, cb, apiName) {
      api[apiName]({
        name: queryString,
        dropDownType: "00",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.name || x };
        });
        cb(result);
      });
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    tableColumn() {
      return [
        {
          type: "checkbox",
          width: 60,
          fixed: "left",
        },
        {
          field: "projectCode",
          title: "项目编码",
          width: 150,
        },
        {
          field: "workHourCode",
          title: "工时编码",
          width: 180,
        },
        {
          field: "workHourName",
          title: "工时名称",
          width: 150,
        },
        {
          field: "projectProfitCenter",
          title: "项目利润中心",
          width: 150,
        },
        {
          field: "supportWorkHourCode",
          title: "支持工时编码",
          width: 150,
        },
        {
          field: "supportWorkHourName",
          title: "支持工时名称",
          width: 150,
        },
        {
          field: "workHourHome",
          title: "工时归属",
          width: 120,
        },
        {
          field: "businessTypeName",
          title: "工单台账对应业务类型",
          width: 120,
        },
        {
          field: "orderType",
          title: "是否订单制",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{
                    color: row.orderType === "是" ? "#029c7c" : "#888888",
                  }}
                >
                  {row.orderType}
                </div>,
              ];
            },
          },
        },
        {
          field: "operateTime",
          title: "操作时间",
          width: 120,
        },
        {
          field: "operatorName",
          title: "操作人",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "workHourCode",
            element: "el-input",
            title: "工时编码",
          },
          {
            field: "workHourHome",
            title: "工时归属",
            element: "el-select",
            props: {
              options: this.workBelongOptions,
              // optionLabel: "dictLabel", //自定义选项名
              // optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "businessType",
            title: "对应业务类型",
            element: "el-select",
            props: {
              filterable: true,
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
            },
          },
          {
            field: "operateTime",
            title: "操作时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "workHourName",
            element: "el-input",
            title: "工时名称",
          },
          {
            field: "supportWorkHourCode",
            element: "el-input",
            title: "支持工时编码",
          },
          {
            field: "supportWorkHourName",
            element: "el-input",
            title: "支持工时名称",
          },
          {
            field: "orderType",
            title: "是否订单制",
            element: "el-select",
            props: {
              options: [
                { value: "0", label: "否" },
                { value: "1", label: "是" },
              ],
              // optionLabel: "dictLabel", //自定义选项名
              // optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "orderType",
            element: "el-radio-group",
            title: "是否订单制",
            props: {
              options: [
                { value: "1", label: "订单制" },
                { value: "0", label: "非订单制" },
              ],
            },
            defaultValue: "1",
            rules: [{ required: true, message: "请选择是否订单制" }],
          },
          {
            field: "workHourHome",
            element: "el-autocomplete",
            title: "工时归属",
            attrs: {
              maxlength: 100,
            },
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "getDropdownList");
              },
            },
          },
          {
            field: "businessType",
            element: "el-select",
            title: "工单台账对应业务类型",
            props: {
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
            },
          },
        ],
        batchMark: [
          {
            field: "orderType",
            element: "el-radio-group",
            title: "是否订单制",
            props: {
              options: [
                { value: "1", label: "订单制" },
                { value: "0", label: "非订单制" },
              ],
            },
            defaultValue: "1",
            rules: [{ required: true, message: "请选择是否订单制" }],
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["workHours:code:edit"]),
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["workHours:code:log"]);
            },
          },
          {
            title: "批量标记工时",
            typeName: "batchMark",
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "180px",
        },
      };
    },
  },
};
</script>

<style></style>
