<template>
  <div>
    <el-tabs v-model="activeTab" @tab-click="loadData">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="item in filterTabList"
        :key="item.name"
      >
        <component :ref="item.name" :is="item.component"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import Chart from "./analysisComponents/chart.vue";
import Detail from "./analysisComponents/detail.vue";
import ManMonth from "./analysisComponents/manMonth.vue";
export default {
  components: {
    Chart,
    Detail,
    ManMonth,
  },
  data() {
    return {
      activeTab: "chart",
      tabList: [
        {
          label: "图表",
          name: "chart",
          component: "Chart",
          show: () => {
            return checkPermission(["workHours:analysis:chart"]);
          },
        },
        {
          label: "明细",
          name: "detail",
          component: "Detail",
          show: () => {
            return checkPermission(["workHours:analysis:detail"]);
          },
        },
        {
          label: "人月",
          name: "manMonth",
          component: "ManMonth",
          show: () => {
            return checkPermission(["workHours:analysis:manMonth"]);
          },
        },
      ],
    };
  },
  computed: {
    filterTabList() {
      const list = this.tabList.filter((x) => x.show());
      this.activeTab = list[0]?.name;
      return list;
    },
  },

  methods: {
    loadData() {
      console.log("halo", this.$refs[this.activeTab][0], this.$refs);
      this.$refs[this.activeTab]?.[0]?.loadData();
    },
  },
};
</script>

<style></style>
