### 数据源配置表单（钉钉 & 运管中间表）

#### Card 1：数据获取渠道

| 表单项     | 控件类型 | 必填 | 默认值 | 显示规则 | 说明                                 |
| ---------- | -------- | ---- | ------ | -------- | ------------------------------------ |
| 数据源渠道 | 下拉框   | 是   | 钉钉   | 通用     | 选项：  - 钉钉（默认）  - 运管中间表 |

------

#### Card 2：数据获取信息

| 表单项                                 | 控件类型   | 必填                 | 显示条件         | 动态规则               | 说明                                                         |
| -------------------------------------- | ---------- | -------------------- | ---------------- | ---------------------- | ------------------------------------------------------------ |
| **钉钉工单ID**                         | 文本输入框 | 是仅【钉钉】         | 仅【钉钉】       | -                      | 输入提示：请输入钉钉工单类型对应的ID编码                     |
| **钉钉工单名称**                       | 文本输入框 | 仅【钉钉】           | 仅【钉钉】       | -                      | 100字符以内，输入提示：请输入钉钉工单类型的名称，如新电途-活动工单 |
| **推送的前置条件**                     | 单选框组   | 仅【钉钉】           | 仅【钉钉】       | 默认"到节点推送"       | 选项：  • ◯ 工单创建成功后（选择后隐藏节点名称）  • ◉ 到节点推送（选择后显示节点名称） |
| **节点名称**                           | 文本输入框 | 仅【钉钉】           | 仅【钉钉】       | 仅在"到节点推送"时显示 | 输入提示：请输入钉钉工单节点名称，流程到该节点时，将推送数据给维保通 |
| **表名**                               | 文本输入框 | 仅【运管中间表】     | 仅【运管中间表】 | -                      |                                                              |
| **前置条件**                           | 文本输入框 | 仅【运管中间表】     | 仅【运管中间表】 | -                      | 输入提示：自定义取数的SQL规则                                |
| **需同步的字段名称**                   | 文本输入框 | 钉钉：是  中间表：是 | 通用             | -                      | 输入提示：多个字段名称用，号分割，比如“配置反馈，活动城市，活动类型” |
| **同步到维保通工单台账的数据映射关系** | 动态表格   | 是                   | 通用             |                        | 见下方映射表说明                                             |

##### 映射表格规则

| 渠道                      | 字段         | 控件类型   | 必填 | 交互规则                                                     |
| ------------------------- | ------------ | ---------- | ---- | ------------------------------------------------------------ |
| **钉钉/运管中间表都一致** | 支持部门     | 下拉框     | 是   | 工单台账部门列表（单选），选择后获取业务类型下拉选项，交互方式同工单台账-创建工单 |
|                           | 业务类型     | 级联选择器 | 是   | 调取工单台账业务类型，允许选择到三级，每级单选，交互方式同创建工单时选择业务类型，选择后获取工单类型和紧急程度下拉选项，单选 |
|                           | 工单类型     | 级联选择器 | 是   | 调取工单台账工单类型，允许选择到三级，每级单选，交互方式同创建工单时选择工单类型，单选 |
|                           | 紧急程度     | 下拉框     | 是   | 根据所选的业务类型显示对应的紧急程度，交互方式同创建工单     |
|                           | 问题描述     | 自定义     | 是   | 输入提示语：{审批单号}为【审批单号】；{活动名称}为【活动名称】 |
|                           | 需求来源     | 下拉+输入  | 是   | 选项为工单台账中的需求来源类型                               |
|                           | 创建人       | 下拉+输入  | 是   | 选择的项为维保通系统内的所有账号的用户昵称                   |
|                           | 创建时间     | 下拉框     | 是   | 选项：  - 推送时间  - 上一节点审批通过时间                   |
|                           | 工单创建方式 | 下拉+输入  | 是   | 选项为工单台账中的工单创建方式类型                           |


------

#### Card 3：数据外传信息

| 表单项                 | 控件类型   | 必填                               | 显示条件                           | 动态规则 | 说明                                                         |
| ---------------------- | ---------- | ---------------------------------- | ---------------------------------- | -------- | ------------------------------------------------------------ |
| **维保通是否需要推送** | 单选框组   | 是                                 | 通用                               | 默认"否" | 选项：  • ◉ 否（选择时隐藏后续字段）  • ◯ 是（选择时显示后续字段） |
| **外传的前置条件**     | 下拉选项   | 仅选择"是"时必填                   | 仅选择"是"时显示                   |          | 根据上面所选的工单类型，下拉展示该工单类型的所有节点名称     |
| **需推送的字段名称**   | 文本输入框 | 仅选择"是"时必填                   | 仅选择"是"时显示                   | -        | 输入提示：多个字段名称用，号分割，比如“配置反馈，活动城市，活动类型” |
| **中间表侧字段处置**   | 文本输入框 | 仅选择"是"时且仅【运管中间表】必填 | 仅选择"是"时且仅【运管中间表】显示 |          |                                                              |

------

### 关键交互逻辑说明

1. **数据源渠道切换**：

    - 选择 **钉钉** → 显示钉钉专用字段（工单ID/名称、节点名称等）
    - 选择 **运管中间表** → 显示表名/SQL条件字段

2. **前置条件动态显隐**（钉钉专属）：

    ```
    graph TD  
      A[选择'工单创建成功后'] --> B[隐藏节点名称字段]  
      C[选择'到节点推送'] --> D[显示节点名称字段]  
    ```

3. **外传信息动态显隐**（通用）：

    ```
    graph TD  
      E[选择'否'] --> F[隐藏外传条件/字段/处置规则]  
      G[选择'是'] --> H[显示所有外传字段]  
    ```
