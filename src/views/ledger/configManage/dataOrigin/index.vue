<!-- 数据来源配置管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['ledger:dataOrigin:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/configManage/dataOrigin.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "dataOriginPage",
  components: { Timeline },
  mixins: [exportMixin],
  data() {
    return {
      recordList: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "dataSourceChannel",
          title: "数据源渠道",
          width: 150,
        },
        {
          field: "dingTalkWorkOrderName",
          title: "钉钉工单名称",
          width: 200,
        },
        {
          field: "syncFieldNames",
          title: "需同步的字段名称",
          width: 250,
        },
        {
          field: "needPush",
          title: "维保通是否需要推送",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue === 1 ? "是" : "否";
          },
        },
        {
          field: "createByName",
          title: "创建人",
          width: 120,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateByName",
          title: "修改人",
          width: 120,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
        {
          field: "status",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return cellValue === 1 ? "启用" : "禁用";
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "dataSourceChannel",
            title: "数据源渠道",
            element: "el-select",
            props: {
              options: [
                { label: "钉钉", value: "钉钉" },
                { label: "运管中间表", value: "运管中间表" },
              ],
              optionLabel: "label",
              optionValue: "value",
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "dingTalkWorkOrderName",
            title: "钉钉工单名称",
            element: "el-input",
            attrs: {
              placeholder: "请输入钉钉工单名称",
            },
          },
          {
            field: "needPush",
            title: "维保通是否需要推送",
            element: "el-select",
            props: {
              options: [
                { label: "是", value: 1 },
                { label: "否", value: 0 },
              ],
              optionLabel: "label",
              optionValue: "value",
              clearable: true,
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "启用", value: 1 },
                { label: "禁用", value: 0 },
              ],
              optionLabel: "label",
              optionValue: "value",
              clearable: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: checkPermission(["ledger:dataOrigin:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["ledger:dataOrigin:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:dataOrigin:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.loadData();
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ id: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      return new Promise((resolve) => {
        let params = { ...formParams };
        api
          .update(params)
          .then((res) => {
            if (res.code === "10000") {
              this.$message.success("提交成功");
              this.loadData();
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      // 跳转到新增页面
      this.$router.push({
        path: "/ledger/configManage/dataOrigin/add",
        query: { type: "add" },
      });
    },
    rowEdit(row) {
      // 跳转到编辑页面
      this.$router.push({
        path: "/ledger/configManage/dataOrigin/edit",
        query: { type: "edit", id: row.id },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
