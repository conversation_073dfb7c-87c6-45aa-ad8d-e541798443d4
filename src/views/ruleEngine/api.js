//该文件无效，仅用来查看api接口路径
const a = {
  // 规则链
  rule_chain_list: `/chain/page`, // 规则链列表
  rule_chain_enable: `/chain/updateEnable`, // 规则链启用禁用
  rule_chain_add: `/chain/insert`, // 规则链新增
  rule_chain_update: `/chain/update`, // 规则链编辑
  rule_chain_detail: `/chain/detail`, // 规则链详情
  rule_chain_delete: `/chain/delete`, // 规则链删除
  rule_chain_el_preview: `/chain/generateLogicFlowEL`, // el表达式预览

  rule_chain_node_debug: `/chain/queryNodeDataList`, // 规则链节点数据列表
  rule_chain_debug: `/chain/chainInitialContext`, // 规则链调试数据
  rule_chain_node_self_test: `/chain/nodeSelfTest`, // 节点自测
  rule_chain_entity: `/chain/queryChainInstanceList`, // 规则链实例
  rule_chain_all: `/chain/queryChainList`, // 规则链列表
  rule_chain_version_list: `/chain/version/list`, // 规则链版本列表
  rule_chain_version_detail: `/chain/version/detail`, // 规则链版本创建
  rule_chain_version_compare: `/chain/version/compare`, // 规则链版本比较
  rule_chain_version_rollback: `/chain/version/rollback`, // 规则链版本回滚
};
