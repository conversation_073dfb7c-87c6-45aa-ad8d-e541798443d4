<!-- 上下文数据Tab -->
<template>
  <div class="context-data-tab">
    <!-- 数据快照卡片 -->
    <el-card class="context-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据快照（JSON）</span>
      </div>

      <div v-loading="loading" class="json-content">
        <div v-if="jsonData" class="json-display">
          <pre>{{ formatJsonData(jsonData) }}</pre>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无数据快照" />
        </div>
      </div>
    </el-card>

    <!-- 关联业务对象 -->
    <div class="business-object-section">
      <h3 class="section-title">关联业务对象</h3>
      <div class="business-object-content">
        <p class="placeholder-text">关联业务对象内容将在后续开发中实现</p>
      </div>
    </div>
  </div>
</template>

<script>
import api from "@/api/ruleEngine/resultSearch.js";

export default {
  name: "ContextDataTab",
  props: {
    alarmId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      jsonData: null,
    };
  },
  created() {
    this.loadContextData();
  },
  methods: {
    // 加载上下文数据
    async loadContextData() {
      this.loading = true;
      try {
        // 调用API获取上下文数据
        // const res = await api.getContextData({ alarmId: this.alarmId });
        // this.jsonData = res.data;

        // 暂时使用模拟数据
        this.jsonData = {
          alarmId: this.alarmId,
          timestamp: "2024-01-15 14:30:25",
          deviceInfo: {
            deviceId: "CHG001",
            deviceName: "充电桩001",
            location: "停车场A区",
            status: "charging",
          },
          sensorData: {
            temperature: 85.5,
            voltage: 220.3,
            current: 32.1,
            power: 7.2,
          },
          ruleContext: {
            ruleName: "温度异常检测",
            ruleId: "RULE_TEMP_001",
            threshold: 80,
            severity: "high",
          },
          metadata: {
            source: "sensor_monitor",
            version: "1.0.0",
            processedAt: "2024-01-15 14:30:26",
          },
        };

        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        this.$message.error("加载上下文数据失败");
        console.error(error);
      }
    },

    // 格式化JSON数据
    formatJsonData(data) {
      if (!data) return "";
      return JSON.stringify(data, null, 2);
    },
  },
};
</script>

<style lang="less" scoped>
.context-data-tab {
  padding: 20px;

  .context-card {
    margin-bottom: 20px;
  }

  .card-title-wrap {
    display: flex;
    align-items: center;

    .card-title-line {
      width: 4px;
      height: 16px;
      background: #00b099;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .json-content {
    min-height: 300px;

    .json-display {
      pre {
        background-color: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", "Consolas",
          "source-code-pro", monospace;
        font-size: 13px;
        line-height: 1.5;
        padding: 16px;
        margin: 0;
        overflow: auto;
        max-height: 400px;
        white-space: pre;
        word-wrap: normal;
        color: #333;

        &:hover {
          border-color: #00b099;
          box-shadow: 0 0 0 2px rgba(0, 176, 153, 0.1);
        }
      }
    }

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }

  .business-object-section {
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-left: 12px;
      border-left: 4px solid #00b099;
    }

    .business-object-content {
      padding: 20px;
      background-color: #fafafa;
      border-radius: 6px;
      min-height: 80px;

      .placeholder-text {
        color: #999;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }
    }
  }
}
</style>
