<!-- 执行日志Tab -->
<template>
  <div class="execution-log-tab">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>执行日志</span>
      </div>

      <div class="content-area">
        <ExecutionLogTable
          :alarmId="alarmId"
          :tableHeight="500"
          :showPagination="true"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import ExecutionLogTable from "./ExecutionLogTable.vue";

export default {
  name: "ExecutionLogTab",
  components: {
    ExecutionLogTable,
  },
  props: {
    alarmId: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.execution-log-tab {
  padding: 20px;

  .card-title-wrap {
    display: flex;
    align-items: center;

    .card-title-line {
      width: 4px;
      height: 16px;
      background: #00b099;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .content-area {
    padding: 0;
  }
}
</style>
