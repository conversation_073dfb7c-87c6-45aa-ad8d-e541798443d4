<!-- 预警概览Tab -->
<template>
  <div class="overview-tab">
    <!-- 基本信息卡片 -->
    <el-card class="info-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="basicForm"
        :config="basicInfoConfig"
        :params="basicInfoData"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        :preview="true"
      />
    </el-card>

    <!-- 规则信息卡片 -->
    <el-card class="info-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>规则信息</span>
      </div>
      <DynamicForm
        ref="ruleForm"
        :config="ruleInfoConfig"
        :params="ruleInfoData"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        :preview="true"
      />
    </el-card>

    <!-- 数据比对卡片 -->
    <el-card class="info-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据比对</span>
      </div>
      <DynamicForm
        ref="compareForm"
        :config="compareInfoConfig"
        :params="compareInfoData"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        :preview="true"
      />
    </el-card>

    <!-- 同规则预警趋势卡片 -->
    <el-card class="info-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>同规则预警趋势</span>
      </div>

      <!-- 时间范围选择器 -->
      <div class="trend-controls">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleDateRangeChange"
          style="margin-bottom: 20px;"
        />
        <el-button
          type="primary"
          @click="loadTrendData"
          style="margin-left: 10px;"
        >
          查询
        </el-button>
      </div>

      <!-- 趋势图表 -->
      <LineChart
        v-if="chartData.axisData.length > 0"
        :axisData="chartData.axisData"
        :serieData="chartData.serieData"
        height="350px"
        :showTooltip="true"
        lineType="line"
        unit="次"
      />
      <div v-else class="no-data">
        <el-empty description="暂无数据" />
      </div>
    </el-card>
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";

export default {
  name: "OverviewTab",
  components: {
    LineChart,
  },
  props: {
    alarmId: {
      type: String,
      required: true,
    },
    alarmData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dateRange: [],
      basicInfoData: {},
      ruleInfoData: {},
      compareInfoData: {},
      chartData: {
        axisData: [],
        serieData: [],
      },
      // 基本信息表单配置
      basicInfoConfig: [
        {
          field: "alarmCode",
          title: "告警编码",
          element: "el-input",
        },
        {
          field: "alarmContent",
          title: "告警内容名称",
          element: "el-input",
        },
        {
          field: "riskLevel",
          title: "风险等级",
          element: "el-input",
        },
        {
          field: "alarmTime",
          title: "告警时间",
          element: "el-input",
        },
      ],
      // 规则信息表单配置
      ruleInfoConfig: [
        {
          field: "ruleName",
          title: "规则名称",
          element: "el-input",
        },
        {
          field: "ruleDesc",
          title: "规则描述",
          element: "el-input",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 3,
          },
        },
        {
          field: "ruleVersion",
          title: "规则版本",
          element: "el-input",
        },
        {
          field: "nodeName",
          title: "节点名称",
          element: "el-input",
        },
      ],
      // 数据比对表单配置
      compareInfoConfig: [
        {
          field: "triggerValue",
          title: "触发值",
          element: "el-input",
        },
        {
          field: "threshold",
          title: "阈值",
          element: "el-input",
        },
      ],
    };
  },
  watch: {
    alarmData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.initData();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 设置默认时间范围（最近7天）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7);

    this.dateRange = [this.formatDate(startDate), this.formatDate(endDate)];
  },
  mounted() {
    this.loadTrendData();
  },
  methods: {
    // 初始化数据
    initData() {
      this.basicInfoData = {
        alarmCode: this.alarmData.alarmCode || "",
        alarmContent: this.alarmData.alarmContent || "",
        riskLevel: this.alarmData.riskLevel || "",
        alarmTime: this.alarmData.alarmTime || "",
      };

      this.ruleInfoData = {
        ruleName: this.alarmData.ruleName || "",
        ruleDesc: this.alarmData.ruleDesc || "",
        ruleVersion: this.alarmData.ruleVersion || "",
        nodeName: this.alarmData.nodeName || "",
      };

      this.compareInfoData = {
        triggerValue: this.alarmData.triggerValue || "",
        threshold: this.alarmData.threshold || "",
      };

      this.$emit("dataLoaded", {
        basicInfo: this.basicInfoData,
        ruleInfo: this.ruleInfoData,
        compareInfo: this.compareInfoData,
      });
    },

    // 时间范围变化
    handleDateRangeChange(dateRange) {
      this.dateRange = dateRange;
    },

    // 加载趋势数据
    async loadTrendData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning("请选择时间范围");
        return;
      }

      try {
        // 这里调用API获取趋势数据
        // const res = await api.getTrendData({
        //   alarmId: this.alarmId,
        //   startDate: this.dateRange[0],
        //   endDate: this.dateRange[1],
        // });

        // 暂时使用模拟数据
        this.chartData = {
          axisData: [
            "8月1日",
            "8月2日",
            "8月3日",
            "8月4日",
            "8月5日",
            "8月6日",
            "8月7日",
          ],
          serieData: [
            {
              name: "预警次数",
              data: [120, 200, 150, 80, 70, 110, 130],
              color: "#5470C6",
            },
          ],
        };
      } catch (error) {
        this.$message.error("加载趋势数据失败");
        console.error(error);
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style lang="less" scoped>
.overview-tab {
  .info-card {
    margin-bottom: 20px;

    .card-title-wrap {
      display: flex;
      align-items: center;

      .card-title-line {
        width: 4px;
        height: 16px;
        background-color: #409eff;
        margin-right: 8px;
      }

      span {
        font-weight: 500;
        font-size: 16px;
      }
    }
  }

  .trend-controls {
    margin-bottom: 20px;
  }

  .no-data {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
