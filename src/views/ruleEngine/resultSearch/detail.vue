<!-- 告警详情页面 -->
<template>
  <div class="alarm-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack">
          返回
        </el-button>
        <h2 class="page-title">告警内容</h2>
      </div>
      <div class="header-right">
        <el-button type="danger" @click="handleRelease">解除</el-button>
      </div>
    </div>

    <!-- Tab 切换区域 -->
    <el-card class="tab-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="预警概览" name="overview">
          <OverviewTab
            :alarmId="alarmId"
            :alarmData="alarmData"
            @dataLoaded="handleOverviewDataLoaded"
          />
        </el-tab-pane>
        <el-tab-pane label="规则逻辑" name="ruleLogic">
          <RuleLogicTab :alarmId="alarmId" />
        </el-tab-pane>
        <el-tab-pane label="上下文数据" name="contextData">
          <ContextDataTab :alarmId="alarmId" />
        </el-tab-pane>
        <el-tab-pane label="处理记录" name="processRecord">
          <ProcessRecordTab :alarmId="alarmId" />
        </el-tab-pane>
        <el-tab-pane label="执行日志" name="executionLog">
          <ExecutionLogTab :alarmId="alarmId" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 解除弹窗 -->
    <BaseFormModal
      ref="releaseModal"
      modalTitle="解除预警"
      :config="releaseFormConfig"
      @modalConfirm="handleReleaseConfirm"
      modalWidth="50%"
    />
  </div>
</template>

<script>
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import OverviewTab from "./components/OverviewTab.vue";
import RuleLogicTab from "./components/RuleLogicTab.vue";
import ContextDataTab from "./components/ContextDataTab.vue";
import ProcessRecordTab from "./components/ProcessRecordTab.vue";
import ExecutionLogTab from "./components/ExecutionLogTab.vue";
import api from "@/api/ruleEngine/resultSearch.js";

export default {
  name: "AlarmDetail",
  components: {
    BaseFormModal,
    OverviewTab,
    RuleLogicTab,
    ContextDataTab,
    ProcessRecordTab,
    ExecutionLogTab,
  },
  data() {
    return {
      statusOptions: [],
      activeTab: "overview",
      alarmId: "",
      alarmData: {},
      // 解除弹窗表单配置
    };
  },
  computed: {
    releaseFormConfig() {
      return [
        {
          field: "status",
          title: "解除进度",
          element: "el-select",
          props: {
            //这里是通过接口异步获取，也可以直接在这写死
            options: this.statusOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            filterable: true,
          },
          rules: [
            {
              required: true,
              message: "请选择解除进度",
            },
          ],
        },
        {
          field: "urgencyDefinition",
          title: "解除原因",
          props: {
            type: "textarea",
          },
          attrs: {
            placeholder: "500个字符以内",
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
          },
        },
      ];
    },
  },
  created() {
    this.alarmId = this.$route.query.id;
    if (!this.alarmId) {
      this.$message.error("缺少告警ID参数");
      this.goBack();
      return;
    }
    this.loadAlarmBasicInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载告警基本信息
    async loadAlarmBasicInfo() {
      try {
        // 这里调用API获取告警基本信息
        // const res = await api.getAlarmDetail({ id: this.alarmId });
        // this.alarmData = res.data;

        // 暂时使用模拟数据
        this.alarmData = {
          alarmCode: "ALM20231205001",
          alarmContent: "设备温度异常超过阈值的预警提醒",
          riskLevel: "高风险",
          alarmTime: "2023-12-05 14:30:25",
          ruleName: "设备温度监控规则",
          ruleDesc: "监控设备温度是否超过设定阈值，当温度超过阈值时触发预警",
          ruleVersion: "v1.0",
          nodeName: "温度传感器节点",
          triggerValue: "85°C",
          threshold: "80°C",
        };
      } catch (error) {
        this.$message.error("加载告警信息失败");
        console.error(error);
      }
    },

    // Tab 切换事件
    handleTabClick(tab) {
      console.log("切换到tab:", tab.name);
      // 这里可以根据需要调用不同的API
    },

    // 概览数据加载完成回调
    handleOverviewDataLoaded(data) {
      // 可以在这里处理概览数据加载完成后的逻辑
      console.log("概览数据加载完成:", data);
    },

    // 解除按钮点击
    handleRelease() {
      this.$refs.releaseModal.open({
        alarmId: this.alarmId,
      });
    },

    // 解除确认
    async handleReleaseConfirm(formData) {
      try {
        // 调用解除API
        const res = await api.update({
          urgencyId: this.alarmId,
          releaseReason: formData.releaseReason,
        });

        if (res.code === "10000") {
          this.$message.success("解除成功");
          this.goBack();
        }
      } catch (error) {
        this.$message.error("解除失败");
        console.error(error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-detail-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      .page-title {
        margin: 0 0 0 10px;
        font-size: 20px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .tab-container {
    min-height: 600px;

    /deep/ .el-tabs__content {
      padding-top: 20px;
    }
  }
}
</style>
