export const mockDebugList = [
  {
    chainId: 'TestChain',
    chainInstanceId: '2cd28aad34364e77b902db00d72019e2',
    instanceDate: null,
    nodeId: 'testNode',
    nodeInstanceId: '8f81f592266a4ea08701b0f8d3a86052',
    dataCode: 'inputData',
    dataValue: JSON.stringify({
      id: 1,
      name: '测试数据',
      status: 'active',
      timestamp: '2025-01-20 10:30:00'
    }),
    dataType: 'INPUT',
    dataVersion: '1',
    startTime: '2025-01-20 10:30:00.123',
    endTime: '2025-01-20 10:30:01.456',
    successFlag: true,
    errorMsg: '',
    remark: '',
    params: null,
  },
  {
    chainId: 'TestChain',
    chainInstanceId: '2cd28aad34364e77b902db00d72019e2',
    instanceDate: null,
    nodeId: 'testNode',
    nodeInstanceId: '8f81f592266a4ea08701b0f8d3a86052',
    dataCode: 'outputData',
    dataValue: JSON.stringify({
      result: 'success',
      processedData: {
        count: 100,
        items: ['item1', 'item2', 'item3']
      }
    }),
    dataType: 'OUTPUT',
    dataVersion: '1',
    startTime: '2025-01-20 10:30:01.456',
    endTime: '2025-01-20 10:30:02.789',
    successFlag: true,
    errorMsg: '',
    remark: '',
    params: null,
  },
  {
    chainId: 'TestChain',
    chainInstanceId: '2cd28aad34364e77b902db00d72019e2',
    instanceDate: null,
    nodeId: 'errorNode',
    nodeInstanceId: '9f81f592266a4ea08701b0f8d3a86053',
    dataCode: 'errorData',
    dataValue: JSON.stringify({
      error: 'Processing failed',
      details: 'Invalid input format'
    }),
    dataType: 'ERROR',
    dataVersion: '1',
    startTime: '2025-01-20 10:30:02.789',
    endTime: '2025-01-20 10:30:03.012',
    successFlag: false,
    errorMsg: '节点执行失败：数据格式错误',
    remark: '',
    params: null,
  }
];
