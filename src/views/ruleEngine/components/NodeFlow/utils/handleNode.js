// 定义需要查找的节点类型
export const FIND_NODE_TYPES = ['FOR', 'WHILE', 'ITERATOR', 'PAR'];

/**
 * 查找最近的指定类型节点
 * @param {Object} params
 * @param {string} params.id - 当前节点ID
 * @param {Array<string>} [params.findNodeTypes] - 要查找的节点类型数组，默认为FIND_NODE_TYPES
 * @param {Object} params.flowData - 流程图数据
 * @returns {Object} 找到的节点信息 {id: string, type: string}
 */
export function findNearestNodeByType({ id, findNodeTypes = FIND_NODE_TYPES, flowData }) {
  const visited = new Set(); // 记录已访问的节点
  const nodes = flowData.nodes;
  const edges = flowData.edges;

  function findPreviousNodes(nodeId) {
    // 如果已经访问过该节点，返回null避免循环
    if (visited.has(nodeId)) return null;
    visited.add(nodeId);

    // 获取当前节点
    const currentNode = nodes.find((node) => node.id === nodeId);
    if (!currentNode) return null;

    // 如果当前节点是要查找的类型之一，返回该节点信息
    if (findNodeTypes.includes(currentNode.properties?.nodeType)) {
      return {
        id: currentNode.id,
        type: currentNode.properties?.nodeType,
      };
    }

    // 获取所有指向当前节点的边
    const incomingEdges = edges.filter((edge) => edge.targetNodeId === nodeId);

    // 如果没有入边，返回null
    if (!incomingEdges.length) return null;

    // 如果当前节点是并行结束节点，需要检查所有入边
    if (currentNode.properties?.nodeType === 'PAR') {
      for (const edge of incomingEdges) {
        const result = findPreviousNodes(edge.sourceNodeId);
        if (result) return result;
      }
      return null;
    }

    // 对于普通节点，只需要检查第一条入边
    return findPreviousNodes(incomingEdges[0].sourceNodeId);
  }

  return findPreviousNodes(id);
}
