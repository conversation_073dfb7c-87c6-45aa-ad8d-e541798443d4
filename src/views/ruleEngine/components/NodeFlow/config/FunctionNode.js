import { h, CircleNode, CircleNodeModel } from '@logicflow/core';
import { nodeIcons } from './icons';

class FunctionNodeModel extends CircleNodeModel {
  initNodeData(data) {
    super.initNodeData(data);

    // 设置节点大小
    this.r = 22; // 圆的半径

    // 设置四个方向的锚点
    this.anchorsOffset = [
      [this.r, 0], // 右
      [-this.r, 0], // 左
      [0, this.r], // 下
      [0, -this.r], // 上
    ];
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const { properties } = this;

    if (properties.color) {
      style.stroke = properties.color;
    }

    style.strokeWidth = 2;
    style.fill = '#fff';

    return style;
  }

  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.hover.r = 8;
    style.hover.fill = '#fff';
    style.hover.stroke = style.stroke;
    return style;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = '#00b099';
    style.hover.stroke = '#00b099';
    return style;
  }
}

class FunctionNode extends CircleNode {
  getShape() {
    const { model } = this.props;
    const { x, y, r, properties } = model;
    const style = model.getNodeStyle();

    return h('g', {}, [
      // 主圆形
      h('circle', {
        ...style,
        cx: x,
        cy: y,
        r,
      }),
      // 图标
      h('path', {
        fill: style.stroke,
        d: nodeIcons[properties.icon],
        transform: `matrix(0.018,0,0,0.018,${x - 10},${y - 10})`,
        style: {
          pointerEvents: 'none',
          transition: 'all 0.3s ease',
        },
      }),
    ]);
  }
}

export default {
  type: 'function-node',
  view: FunctionNode,
  model: FunctionNodeModel,
};
