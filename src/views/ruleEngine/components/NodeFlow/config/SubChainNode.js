import { h, RectNode, RectNodeModel } from "@logicflow/core";
import { nodeIcons } from "./icons";

class SubChainNodeModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data);
    this.width = data.properties.width || 150;

    // 根据文字内容动态计算高度
    const textContent = data.text?.value || data.properties?.name || "";
    const estimatedLines = this.calculateTextLines(textContent, 100); // 100是textWidth
    this.height = Math.max(40, 20 + estimatedLines * 16); // 基础高度40，每行16px

    this.radius = 4;

    // 设置只有左右两个锚点
    const { width } = this;
    this.anchorsOffset = [
      [-width / 2, 0], // 左侧锚点
      [width / 2, 0], // 右侧锚点
    ];
  }

  // 计算文字行数的辅助方法
  calculateTextLines(text, maxWidth) {
    if (!text) return 1;

    // 简单的字符宽度估算（中文字符约12px，英文字符约6px）
    const getCharWidth = (char) => {
      return /[\u4e00-\u9fa5]/.test(char) ? 12 : 6;
    };

    let currentLineWidth = 0;
    let lines = 1;

    for (let i = 0; i < text.length; i++) {
      const charWidth = getCharWidth(text[i]);
      currentLineWidth += charWidth;

      if (currentLineWidth > maxWidth) {
        lines++;
        currentLineWidth = charWidth;
      }
    }

    return lines;
  }

  // 更新文字时重新计算节点高度
  updateText(value) {
    super.updateText(value);
    const estimatedLines = this.calculateTextLines(value, 100);
    this.height = Math.max(40, 20 + estimatedLines * 16);

    // 重新设置锚点位置
    const { width } = this;
    this.anchorsOffset = [
      [-width / 2, 0], // 左侧锚点
      [width / 2, 0], // 右侧锚点
    ];
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const { properties } = this;

    if (properties.color) {
      style.stroke = properties.color;
    }

    return style;
  }

  getTextStyle() {
    // 返回空样式，禁用默认文字渲染，使用自定义的getText方法
    return {
      fontSize: 0,
      fill: "transparent",
    };
  }

  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.hover.r = 8;
    style.hover.fill = "#fff";
    style.hover.stroke = style.stroke;
    return style;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = "#00b099"; // 修改选中边框颜色
    style.hover.stroke = "#00b099";
    return style;
  }
}

class SubChainNode extends RectNode {
  getShape() {
    const { model } = this.props;
    const { x, y, width, height, radius, properties } = model;
    const style = model.getNodeStyle();

    const elements = [
      h("rect", {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
      }),
      h("path", {
        fill: style.stroke,
        d: nodeIcons[properties.icon],
        transform: `matrix(0.018,0,0,0.018,${x - width / 2 + 12},${y - 10})`,
        style: {
          pointerEvents: "none",
        },
      }),
    ];

    // 添加自定义文字
    const textElement = this.getText();
    if (textElement) {
      elements.push(textElement);
    }

    return h("g", {}, elements);
  }

  // 重写getText方法来自定义文字渲染
  getText() {
    const { model } = this.props;
    const { x, y, width } = model;
    const textContent = model.text?.value || "";

    if (!textContent) return null;

    const textPaddingLeft = 36;
    const maxWidth = 100;
    const fontSize = 12;
    const lineHeight = 16;

    // 分割文字为多行
    const lines = this.wrapText(textContent, maxWidth);
    const totalHeight = lines.length * lineHeight;
    const startY = y - totalHeight / 2 + lineHeight / 2;

    return h(
      "g",
      {},
      lines.map((line, index) =>
        h(
          "text",
          {
            x: x - width / 2 + textPaddingLeft,
            y: startY + index * lineHeight,
            fontSize: fontSize,
            fill: "#333",
            fontWeight: 500,
            textAnchor: "start",
            dominantBaseline: "text-before-edge",
            style: {
              fontFamily:
                '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
              pointerEvents: "none",
            },
          },
          line
        )
      )
    );
  }

  // 文字换行辅助方法
  wrapText(text, maxWidth) {
    if (!text) return [];

    const getCharWidth = (char) => {
      return /[\u4e00-\u9fa5]/.test(char) ? 12 : 6;
    };

    const lines = [];
    let currentLine = "";
    let currentWidth = 0;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const charWidth = getCharWidth(char);

      if (currentWidth + charWidth > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = char;
        currentWidth = charWidth;
      } else {
        currentLine += char;
        currentWidth += charWidth;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }
}

export default {
  type: "sub-chain-node",
  view: SubChainNode,
  model: SubChainNodeModel,
};
