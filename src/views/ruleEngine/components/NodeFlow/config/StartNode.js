import { CircleNode, CircleNodeModel } from '@logicflow/core';

class StartNodeModel extends CircleNodeModel {
  initNodeData(data) {
    super.initNodeData(data);

    // 设置节点的大小
    this.r = 25;

    // 只设置右侧锚点
    this.anchorsOffset = [
      [this.r, 0], // 只保留右侧锚点
    ];
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const { properties } = this;

    style.fill = '#fff';
    style.stroke = '#999';
    style.strokeWidth = 2;

    return style;
  }

  // 重写锚点样式
  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.hover.r = 8;
    style.hover.fill = '#fff';
    style.hover.stroke = '#999';
    return style;
  }

  // 设置文本样式
  getTextStyle() {
    const style = super.getTextStyle();
    return {
      ...style,
      fontSize: 14,
      color: '#333',
    };
  }

  // 添加源节点规则
  getSourceRules() {
    const rules = super.getSourceRules();
    const onlyAsSource = {
      message: '开始节点只能作为起点',
      validate: () => true,
    };
    rules.push(onlyAsSource);
    return rules;
  }

  // 添加目标节点规则
  getTargetRules() {
    const rules = super.getTargetRules();
    const notAsTarget = {
      message: '开始节点不能作为终点',
      validate: () => false,
    };
    rules.push(notAsTarget);
    return rules;
  }

  // 不允许被删除
  isAllowDelete() {
    return false;
  }
}

// 继承原生圆形节点View
class StartNode extends CircleNode {}

export default {
  type: 'start-node',
  view: StartNode,
  model: StartNodeModel,
};
