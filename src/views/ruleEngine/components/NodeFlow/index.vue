<template>
  <div class="node-flow">
    <node-panel
      v-if="!isDebug"
      @drag-start="onDragStart"
      :chainName="defaultData.chainName"
    />
    <entity-panel
      v-else
      :chainId="chainId"
      :chainName="defaultData.chainName"
      :version="defaultData.version"
      @selected-chain-instance-id="handleSelectEntity"
      @version-view="handleVersionView"
    />
    <div class="editor-container">
      <flow-toolbar
        :is-selecting="isSelecting"
        :show-expression-preview="showExpressionPreview"
        @selection-change="handleOpenSelection"
        @debug="handleDebug"
        @debug-close="handleDebugClose"
        @version-control="versionModalVisible = true"
        @export="handleExport"
        @import="handleImport"
        @toggle-expression="toggleExpressionPreview"
      />
      <div id="logic-flow-container" ref="container"></div>
    </div>
    <node-drawer
      v-model="drawerVisible"
      :nodeData="currentNode"
      :isDebug="isDebug"
      :chainId="chainId"
      :chainInstanceId="selectedChainInstanceId"
      :chainName="defaultData.chainName"
      @save="handleNodeSave"
      @close="onDrawerClose"
    />
    <edge-modal
      :visible.sync="edgeDrawerVisible"
      :edgeData="currentEdge"
      @save="handleEdgeSave"
    />
    <expression-preview
      v-show="showExpressionPreview"
      :expression="expression"
      @refresh="handleExpressionRefresh"
    />
    <version-modal
      :visible.sync="versionModalVisible"
      :chainId="chainId"
      :chainName="defaultData.chainName"
      @restore="handleRestore"
      :currentVersionData="defaultData"
    />
    <test-modal
      :visible.sync="testModalVisible"
      :selectionInfo="selectionInfo"
      :chainInstanceId="selectedChainInstanceId"
      :chainId="chainId"
    />
  </div>
</template>

<script>
import "@logicflow/core/dist/style/index.css";
import { getLogicFlowConfig } from "./config/logicFlowConfig";
import { generateLogicFlowEL } from "@/api/ruleEngine/index.js";
import NodeDrawer from "./components/NodeDrawer.vue";
import NodePanel from "./components/NodePanel.vue";
import EntityPanel from "./components/EntityPanel.vue";
import EdgeModal from "./components/EdgeModal.vue";
import ExpressionPreview from "./components/ExpressionPreview.vue";
import FlowToolbar from "./components/FlowToolbar.vue";
import VersionModal from "./components/VersionModal.vue";
import TestModal from "./components/TestModal.vue";

export default {
  name: "sm-node-flow",
  components: {
    NodeDrawer,
    NodePanel,
    EntityPanel,
    EdgeModal,
    ExpressionPreview,
    FlowToolbar,
    VersionModal,
    TestModal,
  },
  props: {
    defaultData: {
      type: Object,
      default: () => ({
        chainId: "",
        chainName: "",
        expression: "",
        nodes: [],
        edges: [],
      }),
    },
    chainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      lf: null,
      graphData: {
        nodes: [],
        edges: [],
      },
      drawerVisible: false,
      currentNode: {},
      hasSelected: false,
      edgeDrawerVisible: false,
      versionModalVisible: false,
      currentEdge: null,
      expression: "",
      dragItem: null,
      originFlowData: null,
      // 框选状态
      isSelecting: false,
      // 当前选中的实例Id
      selectedChainInstanceId: null,
      // 控制表达式预览的显示/隐藏
      showExpressionPreview: true,
      // 调试模式状态
      isDebugMode: false,
      // 原始数据
      originalData: null,
      // 是否在查看历史版本
      isViewingHistory: false,
      // 自测弹窗
      testModalVisible: false,
      // 框选画布信息
      selectionInfo: {},
    };
  },
  computed: {
    isDebug: {
      get() {
        return this.isDebugMode;
      },
      set(value) {
        this.isDebugMode = value;
      },
    },
  },
  mounted() {
    this.initLogicFlow();
    document.addEventListener("keydown", this.handleKeyDown);
    const { nodes, edges } = this.defaultData;
    this.originFlowData = {
      nodes,
      edges,
    };
    // 从localStorage读取表达式预览的显示状态
    const savedShowExpressionPreview = localStorage.getItem(
      "showExpressionPreview"
    );
    if (savedShowExpressionPreview !== null) {
      this.showExpressionPreview = savedShowExpressionPreview === "true";
    }
    // 确保初始化时关闭调试模式
    this.isDebug = false;
    localStorage.setItem("isDebug", "false");
  },

  watch: {
    defaultData: {
      handler(newData) {
        if (this.lf && newData?.nodes?.length) {
          this.lf.render(newData);
          const graphData = {
            nodes: newData.nodes || [],
            edges: newData.edges || [],
          };
          this.graphData = graphData;
          // 更新数据后重新保存原始数据
          this.originFlowData = graphData;

          // 如果版本发生变化，关闭调试模式
          if (newData.version !== this.currentVersion) {
            this.currentVersion = newData.version;
            this.handleDebugClose();
          }
        }
      },
      deep: true,
    },
  },
  methods: {
    initLogicFlow() {
      if (!this.$refs.container) {
        this.$message.error("容器ref不存在");
        return;
      }

      this.lf = getLogicFlowConfig(this.$refs.container, this);

      if (!this.lf) {
        this.$message.error("初始化LogicFlow失败");
        return;
      }

      if (this.defaultData?.nodes?.length) {
        this.graphData = {
          nodes: this.defaultData.nodes || [],
          edges: this.defaultData.edges || [],
        };
      } else {
        const startNode = {
          type: "start-node",
          x: 300,
          y: 200,
          properties: {
            text: "start",
            saved: true,
          },
          text: {
            x: 300,
            y: 200,
            value: "start",
          },
        };
        this.graphData = {
          nodes: [startNode],
          edges: [],
        };
      }

      this.lf.render(this.graphData);
      setTimeout(() => {
        this.getLatestExpression();
      }, 3000);
    },

    onDragStart(item) {
      console.log("item", item);
      this.dragItem = item;
      this.lf.dnd.startDrag({
        type: item.type,
        text: item.label,
        properties: {
          nodeType: item.nodeType,
          name: item.label,
          saved: false,
          ...item.properties,
        },
      });
    },

    handleNodeSave(formData) {
      if (this.currentNode && this.currentNode.id) {
        try {
          this.lf.updateText(this.currentNode.id, formData.name);
          const properties = {
            ...this.currentNode.properties,
            nodeType: this.currentNode.properties.nodeType,
            name: formData.name,
            description: formData.description,
            saved: true,
            ...formData,
          };
          this.lf.setProperties(this.currentNode.id, properties);

          // 触发表达式更新
          this.lf.emit("text:update");

          this.drawerVisible = false;
          this.$message.success("节点信息保存成功");
        } catch (error) {
          this.$message.error("节点保存失败：" + error.message);
        }
      }
    },

    onDrawerClose() {
      if (this.currentNode && this.currentNode.id) {
        const node = this.lf.getNodeModelById(this.currentNode.id);
        if (node && !node.properties.saved) {
          this.lf.deleteNode(this.currentNode.id);
        }
      }
      this.drawerVisible = false;
    },

    handleEdgeSave(data) {
      try {
        this.lf.setProperties(data.id, data.properties);
        this.lf.updateText(data.id, data.properties.edgeType);
        this.lf.emit("text:update");
        this.$message.success("连线配置保存成功");
      } catch (error) {
        this.$message.error("保存连线配置失败");
      }
    },

    handleKeyDown(event) {
      const isCtrlOrCmd = event.ctrlKey || event.metaKey;
      if (isCtrlOrCmd) {
        switch (event.key.toLowerCase()) {
          case "s":
            event.preventDefault();
            this.saveFlowData();
            break;
          case "z":
            event.preventDefault();
            this.lf.undo();
            break;
          case "y":
            event.preventDefault();
            this.lf.redo();
            break;
          case "a":
            event.preventDefault();
            {
              const { nodes, edges } = this.lf.getGraphData();
              // 排除开始节点，只选择其他节点
              nodes
                .filter((node) => node.type !== "start-node")
                .forEach((node) => {
                  this.lf.selectElementById(node.id, true);
                });
              // 选择所有边
              edges.forEach((edge) => {
                this.lf.selectElementById(edge.id, true);
              });
            }
            break;
        }
      }
    },

    // 判断节点数量是否更改
    isNodeCountChanged() {
      const { edges, nodes } = this.lf.getGraphData();
      const {
        edges: originalEdges,
        nodes: originalNodes,
      } = this.originFlowData;
      // 判断长度
      if (
        nodes.length !== originalNodes.length ||
        edges.length !== originalEdges.length
      ) {
        console.log("节点或连线数量发生变化");
        return true;
      }
      return false;
    },

    async saveFlowData() {
      const flowData = this.lf.getGraphData();

      // 检查是否有节点
      if (
        !flowData.nodes.length ||
        (flowData.nodes.length === 1 && flowData.nodes[0].type === "start-node")
      ) {
        this.$message.error("请至少添加一个业务节点");
        return;
      }

      // 检查是否有未连接的节点
      const unconnectedNodes = flowData.nodes.filter((node) => {
        if (node.type === "start-node") return false;
        return !flowData.edges.some(
          (edge) =>
            edge.sourceNodeId === node.id || edge.targetNodeId === node.id
        );
      });

      if (unconnectedNodes.length > 0) {
        this.$message.error("存在未连接的节点，请检查");
        return;
      }

      // 检查 switch 节点的连线是否都有值
      const switchNodes = flowData.nodes.filter(
        (node) => node.properties?.nodeType === "SWITCH"
      );
      for (const switchNode of switchNodes) {
        const outgoingEdges = flowData.edges.filter(
          (edge) => edge.sourceNodeId === switchNode.id
        );
        const invalidEdge = outgoingEdges.find((edge) => {
          // 如果是连接到超时节点的边，不需要检查 edgeType
          const targetNode = flowData.nodes.find(
            (node) => node.id === edge.targetNodeId
          );
          if (targetNode && targetNode.properties?.nodeType === "TIMEOUT") {
            return false;
          }
          const edgeType = edge.properties?.edgeType || edge.text?.value;
          return !edgeType || edgeType.trim() === "";
        });

        if (invalidEdge) {
          this.$message.error(
            `Switch节点 "${switchNode.text?.value ||
              switchNode.properties?.name}" 的连线缺少分支条件，请检查`
          );
          return;
        }
      }

      // 检查 IF 节点是否有 true 连线
      const ifNodes = flowData.nodes.filter(
        (node) => node.properties?.nodeType === "IF"
      );
      for (const ifNode of ifNodes) {
        const outgoingEdges = flowData.edges.filter(
          (edge) => edge.sourceNodeId === ifNode.id
        );
        const hasTrueEdge = outgoingEdges.some((edge) => {
          const edgeType = edge.properties?.edgeType || edge.text?.value;
          return edgeType === "true";
        });

        if (!hasTrueEdge) {
          this.$message.error(
            `IF节点 "${ifNode.text?.value ||
              ifNode.properties?.name}" 缺少 true 分支连线，请检查`
          );
          return;
        }
      }

      // 检查 while、for、ITERATOR 节点是否有 DO 连线
      const loopNodes = flowData.nodes.filter((node) =>
        ["WHILE", "FOR", "ITERATOR"].includes(node.properties?.nodeType)
      );
      for (const loopNode of loopNodes) {
        const outgoingEdges = flowData.edges.filter(
          (edge) => edge.sourceNodeId === loopNode.id
        );
        const hasDoEdge = outgoingEdges.some((edge) => {
          const edgeType = edge.properties?.edgeType || edge.text?.value;
          return edgeType === "DO";
        });

        if (!hasDoEdge) {
          this.$message.error(
            `${loopNode.properties?.nodeType}节点 "${loopNode.text?.value ||
              loopNode.properties?.name}" 缺少 DO 连线，请检查`
          );
          return;
        }
      }

      const processedData = {
        nodes: flowData.nodes.map((node) => ({
          ...node,
          properties: {
            ...node.properties,
            name: node.text?.value || node.properties?.name || "",
          },
        })),
        edges: flowData.edges.map((edge) => ({
          ...edge,
          properties: {
            ...edge.properties,
            edgeType: edge.text?.value || edge.properties?.edgeType || "",
          },
        })),
        expression: this.expression,
      };

      this.$emit("save", processedData);
    },

    handleOpenSelection() {
      if (this.isSelecting) {
        this.lf.extension.selectionSelect.closeSelectionSelect();
      } else {
        this.lf.extension.selectionSelect.openSelectionSelect();
      }
      this.isSelecting = !this.isSelecting;
    },

    handleSelectEntity(chainInstanceId) {
      this.selectedChainInstanceId = chainInstanceId;
    },

    handleExpressionRefresh() {
      this.getLatestExpression();
    },

    handleDebug() {
      // 如果在查看历史版本，先恢复到当前版本
      if (this.isViewingHistory) {
        this.handleVersionView({ isHistoryVersion: false });
      }
      this.isDebug = true;
      localStorage.setItem("isDebug", "true");
      // 通知工具栏更新状态
      this.$emit("debug-status-change", true);
    },

    handleDebugClose() {
      this.isDebug = false;
      localStorage.setItem("isDebug", "false");
      // 确保显示当前版本数据
      if (this.isViewingHistory) {
        this.handleVersionView({ isHistoryVersion: false });
      }
      // 通知工具栏更新状态
      this.$emit("debug-status-change", false);
    },

    handleRestore(record) {
      this.$emit("restore", record);
    },

    // 获取最新的表达式
    async getLatestExpression() {
      if (!this.lf) {
        return;
      }
      const flowData = this.lf.getGraphData();
      if (flowData.edges.length === 0) {
        this.$message.warning("无连线，无法生成表达式");
        this.expression = "";
        return;
      }
      // 请求接口
      try {
        const res = await generateLogicFlowEL({
          ...flowData,
        });
        if (res && res.success) {
          this.expression = res.data;
        } else {
          // 模拟生成表达式（备用）
          this.expression = `IF(${flowData.nodes.length} > 0) THEN { ... } ELSE { ... }`;
        }
      } catch (error) {
        console.error("生成表达式失败:", error);
        // 模拟生成表达式（备用）
        this.expression = `IF(${flowData.nodes.length} > 0) THEN { ... } ELSE { ... }`;
      }
    },

    // 处理导出
    handleExport() {
      try {
        const flowData = this.lf.getGraphData();
        // 添加额外信息
        const exportData = {
          ...flowData,
          chainName: this.defaultData.chainName,
          exportTime: new Date().toISOString(),
          version: "1.0",
        };

        // 创建Blob对象
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: "application/json",
        });

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${this.defaultData.chainName ||
          "flow"}_${new Date().getTime()}.json`;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.$message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败：" + error.message);
      }
    },

    // 处理导入
    handleImport(data) {
      this.$confirm("导入新数据将覆盖当前画布，是否继续？", "确认导入", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          try {
            // 更新画布数据
            this.lf.render(data);
            this.graphData = {
              nodes: data.nodes || [],
              edges: data.edges || [],
            };

            // 更新表达式
            this.getLatestExpression();

            this.$message.success("导入成功");
          } catch (error) {
            console.error("导入失败:", error);
            this.$message.error("导入失败：" + error.message);
          }
        })
        .catch(() => {
          // 用户取消导入
        });
    },

    // 切换表达式预览的显示/隐藏
    toggleExpressionPreview() {
      this.showExpressionPreview = !this.showExpressionPreview;
      // 保存状态到localStorage
      localStorage.setItem("showExpressionPreview", this.showExpressionPreview);
    },

    // 处理版本查看
    handleVersionView(versionData) {
      if (versionData.isHistoryVersion) {
        // 保存当前数据
        if (!this.originalData) {
          this.originalData = {
            nodes: [...this.graphData.nodes],
            edges: [...this.graphData.edges],
          };
        }

        // 更新画布数据
        this.graphData = {
          nodes: versionData.nodes || [],
          edges: versionData.edges || [],
        };
        this.lf.render(this.graphData);

        // 更新状态
        this.isViewingHistory = true;

        // 更新表达式
        this.expression = versionData.expression || "";

        // 显示提示信息
        this.$message.info(
          `正在查看版本 ${versionData.versionInfo.version} 的数据`
        );
      } else {
        // 恢复原始数据
        if (this.originalData) {
          this.graphData = {
            nodes: [...this.originalData.nodes],
            edges: [...this.originalData.edges],
          };
          this.lf.render(this.graphData);
          this.originalData = null;
        }

        // 更新状态
        this.isViewingHistory = false;

        // 重新获取表达式
        this.getLatestExpression();
      }
    },

    showTestModal(node) {
      this.selectionInfo = node;
      this.testModalVisible = true;
    },
  },

  beforeDestroy() {
    if (this.lf) {
      this.lf.clearData();
      this.lf = null;
    }
    document.removeEventListener("keydown", this.handleKeyDown);
  },
};
</script>

<style lang="less" scoped>
.node-flow {
  margin: 24px;
  display: flex;

  .editor-container {
    position: relative;
    flex: 1;
    height: calc(100vh - 48px);
    background: #fff;

    #logic-flow-container {
      width: 100%;
      height: 100%;
    }
    .toolbar {
      position: absolute;
      top: 24px;
      left: 24px;
      right: 0;
      z-index: 19;
    }
  }
}
/deep/ .lf-control {
  .custom-save-icon {
    background: url("data:image/svg+xml;base64,PHN2ZyB0PSIxNzA0OTU4NTg1NDU3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NjMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNODkzLjQ0IDMyNy42OGwtMTkyLTE5MkM2ODYuNzIgMTIxLjYgNjY4LjE2IDExMiA2NDguMzIgMTEySDIwNC44Yy00NC44IDAtODEuOTIgMzYuNDgtODEuOTIgODEuOTJ2NjM2LjE2YzAgNDQuOCAzNi40OCA4MS45MiA4MS45MiA4MS45Mmg2MTQuNGM0NC44IDAgODEuOTItMzYuNDggODEuOTItODEuOTJWMzcxLjJjMC0xNi42NC05LjYtMzIuNjQtMjMuNjgtNDMuNTJ6TTUxMiA3NjhjLTg4LjMyIDAtMTYwLTcxLjY4LTE2MC0xNjBzNzEuNjgtMTYwIDE2MC0xNjAgMTYwIDcxLjY4IDE2MCAxNjAtNzEuNjggMTYwLTE2MCAxNjB6IG0xNjAtNDQ4SDIwNC44VjE5My45MmgzODR2MTI4YzAgMTcuOTIgMTQuMDggMzIgMzIgMzJoNTEuMlYzMjB6IiBmaWxsPSIjNjY2NjY2IiBwLWlkPSIzNDY0Ij48L3BhdGg+PC9zdmc+")
      no-repeat center center / contain !important;
    transition: all 0.3s ease;
  }

  .custom-back-icon {
    background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIwIDExSDcuODNsNS41OS01LjU5TDEyIDRsLTggOCA4IDggMS40MS0xLjQxTDcuODMgMTNIMjB2LTJ6IiBmaWxsPSIjNjY2NjY2Ii8+PC9zdmc+")
      no-repeat center center / contain !important;
    transition: all 0.3s ease;
  }
  .custom-debug-icon {
    background: url("data:image/svg+xml;base64,PHN2ZyB0PSIxNzA0OTU4NTg1NDU3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NjMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNOTU3LjQ0IDUxMmwtMTI4LTEyOHYtMTI4YzAtMzUuMi0yOC44LTY0LTY0LTY0aC0xMjhjLTI1LjYtMjUuNi02Ny4yLTI1LjYtOTIuOCAwbC0xMjggMTI4aC0xMjhjLTM1LjIgMC02NCAyOC44LTY0IDY0djEyOGwtMTI4IDEyOGgtMTI4YzI1LjYgMjUuNiAyNS42IDY3LjIgMCA5Mi44bDEyOCAxMjhoMTI4YzM1LjIgMCA2NCAyOC44IDY0IDY0djEyOGwtMTI4IDEyOGgxMjhjMzUuMiAwIDY0LTI4LjggNjQtNjR2LTEyOGwxMjgtMTI4YzI1LjYtMjUuNiAyNS42LTY3LjIgMC05Mi44ek01MTIgNzA0Yy0xMDYuNCAwLTE5Mi04NS42LTE5Mi0xOTJzODUuNi0xOTIgMTkyLTE5MiAxOTIgODUuNiAxOTIgMTkyLTg1LjYgMTkyLTE5MiAxOTJ6IiBmaWxsPSIjNjY2NjY2IiBwLWlkPSIzNDY0Ij48L3BhdGg+PC9zdmc+")
      no-repeat center center / contain !important;
    transition: all 0.3s ease;
  }
}
</style>
