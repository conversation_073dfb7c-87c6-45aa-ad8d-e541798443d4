<template>
  <div class="entity-panel">
    <div class="node-panel-header">
      <el-tooltip :content="chainName">
        <span class="chain-name">{{ chainName || "未命名" }}</span>
      </el-tooltip>
      <el-button
        v-if="isViewingHistory"
        size="small"
        icon="el-icon-arrow-left"
        @click="handleBackToCurrent"
      >
        返回当前版本
      </el-button>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange" size="small">
      <el-tab-pane label="当前版本" name="current">
        <el-table
          :data="currentEntities"
          :loading="loading"
          @row-click="handleRowClick"
          row-key="chainInstanceId"
          size="small"
          height="400"
        >
          <el-table-column
            prop="chainInstanceId"
            label="链实例ID"
            width="180"
          />
          <el-table-column prop="instanceDate" label="开始时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.instanceDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="successFlag" label="状态">
            <template slot-scope="scope">
              <el-tag :type="scope.row.successFlag ? 'success' : 'danger'">
                {{ scope.row.successFlag ? "成功" : "失败" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-view"
                @click="viewContext(scope.row)"
                >参数</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="currentEntities.length"
          :current-page="currentPagination.current"
          :page-size="currentPagination.pageSize"
          :total="currentPagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleCurrentSizeChange"
          @current-change="handleCurrentPaginationChange"
          style="margin-top: 16px; text-align: right;"
        />
      </el-tab-pane>

      <el-tab-pane label="历史版本" name="history">
        <el-table
          :data="versionData.history.versions"
          :loading="loading"
          @expand-change="handleExpand"
          row-key="version"
          size="small"
          height="400"
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-table
                :data="props.row.entities"
                :loading="props.row.loading"
                @row-click="handleRowClick"
                row-key="chainInstanceId"
                size="small"
                style="margin: 0 20px;"
              >
                <el-table-column
                  prop="chainInstanceId"
                  label="链实例ID"
                  width="120"
                />
                <el-table-column prop="instanceDate" label="开始时间">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.instanceDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="successFlag" label="状态">
                  <template slot-scope="scope">
                    <el-tag
                      :type="scope.row.successFlag ? 'success' : 'danger'"
                    >
                      {{ scope.row.successFlag ? "成功" : "失败" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      icon="el-icon-view"
                      @click="viewContext(scope.row)"
                      >参数</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                v-if="props.row.entities.length"
                :current-page="historyEntityPagination.current"
                :page-size="historyEntityPagination.pageSize"
                :total="historyEntityPagination.total"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next"
                @size-change="
                  (size) => handleHistoryEntitySizeChange(size, props.row)
                "
                @current-change="
                  (page) => handleHistoryEntityPaginationChange(page, props.row)
                "
                style="margin-top: 16px; text-align: right;"
              />
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本号" />
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="createUser" label="创建人" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-view"
                @click="handleVersionView(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="versionData.history.versions.length"
          :current-page="historyVersionPagination.current"
          :page-size="historyVersionPagination.pageSize"
          :total="historyVersionPagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleHistoryVersionSizeChange"
          @current-change="handleHistoryVersionPaginationChange"
          style="margin-top: 16px; text-align: right;"
        />
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      title="调试信息"
      :visible.sync="debugModalVisible"
      width="60%"
      @close="closeModal"
    >
      <json-viewer
        :value="debugData"
        :expand-depth="5"
        copyable
        boxed
        sort
        theme="jv-light"
      />
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import JsonViewer from "vue-json-viewer";
import "vue-json-viewer/style.css";
import {
  queryChainInstanceList,
  getVersionList,
  chainInitialContext,
  getVersionDetail,
} from "@/api/ruleEngine/index.js";

export default {
  name: "EntityPanel",
  components: {
    JsonViewer,
  },
  props: {
    chainId: {
      type: String,
      default: "",
    },
    chainName: {
      type: String,
      default: "",
    },
    version: {
      type: Number,
    },
  },
  data() {
    return {
      // 调试相关
      debugData: {},
      debugModalVisible: false,

      // 当前选中的tab
      activeTab: "current",

      // 加载状态
      loading: false,

      // 是否在查看历史版本
      isViewingHistory: false,

      // 版本数据
      versionData: {
        current: {
          versions: [], // 当前版本列表
        },
        history: {
          versions: [], // 历史版本列表
        },
      },

      // 当前版本实例分页
      currentPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 历史版本分页
      historyVersionPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 历史版本实例分页
      historyEntityPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 展开的版本记录
      expandedVersions: [],

      // 当前选中的实例ID
      selectedChainInstanceId: "",
    };
  },
  computed: {
    // 获取当前版本的实例列表
    currentEntities() {
      const currentVersion = this.versionData.current.versions[0];
      return currentVersion ? currentVersion.entities : [];
    },
  },
  watch: {
    version: {
      handler(newVal) {
        if (this.activeTab === "current" && newVal) {
          this.getCurrentVersionEntities();
          // 版本变化时关闭历史版本查看
          this.isViewingHistory = false;
        }
      },
      immediate: true,
    },
    selectedChainInstanceId: {
      handler(newVal) {
        this.$emit("selected-chain-instance-id", newVal);
      },
    },
  },
  methods: {
    moment,

    formatTime(time) {
      return moment(time).format("YYYY-MM-DD HH:mm:ss");
    },

    // 获取当前版本实例列表
    async getCurrentVersionEntities() {
      this.loading = true;
      try {
        const res = await queryChainInstanceList({
          chainId: this.chainId,
          chainVersion: this.version,
          pageNum: this.currentPagination.current,
          pageSize: this.currentPagination.pageSize,
        });
        if (res && res.success) {
          // 将数据格式化为与历史版本一致的结构
          this.versionData.current.versions = [
            {
              version: this.version,
              entities: res.data || [],
              loading: false,
            },
          ];
          this.currentPagination.total = res.total;

          // 设置默认选中第一个实例
          if (
            res.data &&
            res.data.length > 0 &&
            !this.selectedChainInstanceId
          ) {
            this.selectedChainInstanceId = res.data[0].chainInstanceId;
          }
        }
      } catch (error) {
        this.$message.error("获取实例列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 获取历史版本列表
    async getHistoryVersions() {
      this.loading = true;
      try {
        const res = await getVersionList({
          chainId: this.chainId,
          pageNum: this.historyVersionPagination.current,
          pageSize: this.historyVersionPagination.pageSize,
        });
        if (res && res.success) {
          this.versionData.history.versions = (res.data || []).map(
            (version) => ({
              ...version,
              entities: [],
              loading: false,
            })
          );
          this.historyVersionPagination.total = res.total;

          // 自动展开第一个版本
          if (res.data && res.data.length > 0) {
            this.expandedVersions = [res.data[0].version];
            this.getVersionEntities(res.data[0].version);
          }
        }
      } catch (error) {
        this.$message.error("获取历史版本列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 获取历史版本的实例列表
    async getVersionEntities(version) {
      const targetVersion = this.versionData.history.versions.find(
        (v) => v.version === version
      );
      if (!targetVersion) return;

      targetVersion.loading = true;
      try {
        const res = await queryChainInstanceList({
          chainId: this.chainId,
          chainVersion: version,
          pageNum: this.historyEntityPagination.current,
          pageSize: this.historyEntityPagination.pageSize,
        });
        if (res && res.success) {
          targetVersion.entities = res.data || [];
          this.historyEntityPagination.total = res.total;

          // 自动选中展开版本的第一个实例
          if (res.data && res.data.length > 0) {
            this.selectedChainInstanceId = res.data[0].chainInstanceId;
          }
        }
      } catch (error) {
        this.$message.error("获取版本实例列表失败");
      } finally {
        targetVersion.loading = false;
      }
    },

    // 处理当前版本分页变化
    handleCurrentPaginationChange(page) {
      this.currentPagination.current = page;
      this.getCurrentVersionEntities();
    },

    // 处理当前版本页面大小变化
    handleCurrentSizeChange(size) {
      this.currentPagination.pageSize = size;
      this.currentPagination.current = 1;
      this.getCurrentVersionEntities();
    },

    // 处理历史版本分页变化
    handleHistoryVersionPaginationChange(page) {
      this.historyVersionPagination.current = page;
      this.expandedVersions = []; // 清空展开的版本
      this.getHistoryVersions();
    },

    // 处理历史版本页面大小变化
    handleHistoryVersionSizeChange(size) {
      this.historyVersionPagination.pageSize = size;
      this.historyVersionPagination.current = 1;
      this.expandedVersions = []; // 清空展开的版本
      this.getHistoryVersions();
    },

    // 处理历史版本实例分页变化
    handleHistoryEntityPaginationChange(page, record) {
      this.historyEntityPagination.current = page;
      this.getVersionEntities(record.version);
    },

    // 处理历史版本实例页面大小变化
    handleHistoryEntitySizeChange(size, record) {
      this.historyEntityPagination.pageSize = size;
      this.historyEntityPagination.current = 1;
      this.getVersionEntities(record.version);
    },

    // 处理Tab切换
    handleTabChange(tab) {
      this.activeTab = tab.name;
      this.isViewingHistory = false;
      this.selectedChainInstanceId = ""; // 清空选中状态

      if (tab.name === "current") {
        this.currentPagination.current = 1;
        this.getCurrentVersionEntities();
        // 切换到当前版本时，通知父组件
        this.$emit("version-view", {
          isHistoryVersion: false,
        });
      } else {
        this.historyVersionPagination.current = 1;
        this.expandedVersions = [];
        this.getHistoryVersions();
      }
    },

    // 处理展开/收起
    handleExpand(row, expandedRows) {
      if (expandedRows.includes(row)) {
        this.expandedVersions = [row.version];
        this.historyEntityPagination.current = 1;
        this.selectedChainInstanceId = ""; // 清空当前选中状态
        this.getVersionEntities(row.version);
      } else {
        this.expandedVersions = [];
        this.selectedChainInstanceId = ""; // 收起时也清空选中状态
      }
    },

    // 处理行点击
    handleRowClick(row) {
      this.selectedChainInstanceId = row.chainInstanceId;
    },

    // 查看调试信息
    async viewContext(record) {
      const res = await chainInitialContext({
        chainId: this.chainId,
        chainInstanceId: record.chainInstanceId,
      });
      if (res && res.success) {
        this.debugData = res.data.params ? JSON.parse(res.data.params) : {};
        this.debugModalVisible = true;
      } else {
        this.$message.error("获取调试信息失败");
      }
    },

    // 查看版本详情
    async handleVersionView(record) {
      const { id } = record;
      const res = await getVersionDetail({
        id,
      });
      if (res && res.success) {
        this.isViewingHistory = true;
        // 发送事件给父组件
        this.$emit("version-view", {
          ...res.data,
          isHistoryVersion: true,
          versionInfo: record,
        });
      }
    },

    // 返回当前版本
    handleBackToCurrent() {
      this.isViewingHistory = false;
      this.$emit("version-view", {
        isHistoryVersion: false,
      });
    },

    // 关闭调试弹窗
    closeModal() {
      this.debugModalVisible = false;
      this.debugData = {};
    },
  },
  mounted() {
    // 确保初始化时是当前版本tab
    this.activeTab = "current";
    this.handleTabChange({ name: "current" });
  },
};
</script>

<style lang="less" scoped>
.entity-panel {
  width: 560px;
  height: 100%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 8px;
  margin-right: 24px;

  .node-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .chain-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  /deep/ .el-table--small {
    margin: -8px;
  }

  // 优化嵌套表格样式
  /deep/ .el-table {
    // 主表格样式
    .el-table__header-wrapper th {
      background: #fafafa;
      font-weight: 500;
    }

    // 表格行hover效果
    .el-table__body tr:hover > td {
      background: #f5f5f5;
    }

    // 展开图标样式
    .el-table__expand-icon {
      border-radius: 2px;
      margin-right: 8px;

      &:hover {
        background: #f0f0f0;
      }
    }

    // 展开行样式
    .el-table__expanded-cell {
      background: #fafafa;

      // 内嵌表格样式
      .el-table--small {
        margin: 0;
        border: 1px solid #f0f0f0;
        border-radius: 4px;

        .el-table__header-wrapper th {
          background: #f5f5f5;
          padding: 8px 16px;
        }

        .el-table__body td {
          padding: 8px 16px;
        }
      }
    }

    // 分页器样式
    .el-pagination {
      margin: 16px 0 0;
      padding: 0;
      text-align: right;
    }

    // 选中行样式
    .selected-row {
      background-color: #e6f7ff;

      &:hover > td {
        background-color: #e6f7ff !important;
      }
    }
  }

  // 表格操作按钮样式
  /deep/ .el-button--text {
    padding: 0 8px;
    height: 24px;
    line-height: 24px;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }
  }

  // 状态标签样式
  /deep/ .el-tag {
    margin-right: 0;
    min-width: 48px;
    text-align: center;
  }
}
</style>
