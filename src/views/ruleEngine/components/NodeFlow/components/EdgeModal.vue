<template>
  <el-dialog
    title="连线配置"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="false"
    class="edge-modal"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="连线类型" prop="edgeType">
        <!-- Switch节点使用可输入的选择框 -->
        <el-select
          v-if="
            edgeData &&
              edgeData.sourceNode &&
              edgeData.sourceNode.properties.nodeType === 'SWITCH'
          "
          v-model="formData.edgeType"
          style="width: 100%"
          placeholder="请选择分支条件"
          allow-create
          filterable
          @change="handleEdgeTypeChange"
        >
          <el-option
            v-for="tag in computedAvailableTags"
            :key="tag"
            :value="tag"
          >
            {{ tag }}
          </el-option>
        </el-select>

        <!-- 其他节点使用普通选择框 -->
        <el-select
          v-else
          v-model="formData.edgeType"
          style="width: 100%"
          placeholder="请选择连线类型"
        >
          <el-option
            v-for="(tag, index) in computedAvailableTags"
            :key="tag + index"
            :value="tag"
          >
            {{ tag }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 添加异常处理配置 -->
      <el-form-item label="异常处理" prop="catchStart">
        <el-radio-group v-model="formData.catchStart">
          <el-radio label="true">开始</el-radio>
          <el-radio label="false">结束</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 添加跳出循环体配置 -->
      <el-form-item label="跳出循环体" prop="breakLoop">
        <el-switch v-model="formData.breakLoop" />
      </el-form-item>

      <el-form-item>
        <common-code-form
          :formRequired="false"
          :formData="formData"
          codeTips="连线代码需要返回一个布尔值(true/false)。例如: true 或 false"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import CommonCodeForm from "./nodeForms/CommonCodeForm.vue";
import { getElNodeConfigByType } from "../config/nodeTypes";

export default {
  name: "EdgeDrawer",
  components: {
    CommonCodeForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    edgeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      formData: {
        edgeType: undefined, // 连线类型
        scriptCode: "",
        scriptName: "",
        isBuiltIn: false,
        catchStart: undefined, // 异常处理配置
        breakLoop: false, // 是否跳出循环体
      },
      rules: {
        edgeType: [
          { required: true, message: "请选择连线类型" },
          {
            validator: (rule, value, callback) => {
              if (!value || value.trim() === "") {
                callback(new Error("请选择连线类型"));
              }
              callback();
            },
          },
        ],
        catchStart: [{ required: true, message: "请选择异常处理时机" }],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    computedAvailableTags() {
      const nodeType = this.edgeData?.sourceNode?.properties?.nodeType;
      const elConfig = getElNodeConfigByType(nodeType);
      return elConfig?.edgeTags || [];
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 获取已保存的边属性
        const edgeType = this.edgeData.properties?.edgeType || "";
        const catchStart = this.edgeData.properties?.catchStart || undefined;
        const breakLoop = this.edgeData.properties?.breakLoop || false;

        this.formData.edgeType = edgeType || undefined;
        this.formData.catchStart = catchStart;
        this.formData.breakLoop = breakLoop;

        // 其他表单项保持不变
        this.formData.scriptCode = this.edgeData.properties?.scriptCode || "";
        this.formData.scriptName = this.edgeData.properties?.scriptName || "";
        this.formData.isBuiltIn = this.edgeData.properties?.isBuiltIn || false;
      }
    },
  },
  methods: {
    isSwitchNode() {
      return this.edgeData?.sourceNode?.properties?.nodeType === "SWITCH";
    },
    handleEdgeTypeChange(value) {
      // Switch节点的特殊处理
      this.formData.edgeType = value;
    },

    handleSave() {
      this.loading = true;
      this.$refs.form.validate((valid) => {
        if (valid) {
          try {
            const data = {
              ...this.edgeData,
              properties: {
                ...this.edgeData.properties,
                edgeType: this.formData.edgeType,
                catchStart: this.formData.catchStart,
                breakLoop: this.formData.breakLoop,
                scriptCode: this.formData.scriptCode,
                scriptName: this.formData.scriptName,
                isBuiltIn: this.formData.isBuiltIn,
              },
            };
            this.$emit("save", data);
            this.handleClose();
          } catch (error) {
            this.$message.error("保存失败：" + error.message);
          }
        }
        this.loading = false;
      });
    },
    handleClose() {
      this.formData.edgeType = undefined;
      this.formData.catchStart = undefined;
      this.formData.breakLoop = false;
      this.formData.scriptCode = "";
      this.formData.scriptName = "";
      this.formData.isBuiltIn = false;
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="less" scoped>
.edge-modal {
  /deep/ .el-dialog__body {
    padding: 24px 24px 8px;
  }

  /deep/ .el-form-item {
    margin-bottom: 24px;
  }

  /deep/ .el-select {
    .el-input__inner {
      background-color: #fff;
    }
  }

  /deep/ .el-form-item__label {
    text-align: right;
    padding-right: 12px;
    color: #333;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
