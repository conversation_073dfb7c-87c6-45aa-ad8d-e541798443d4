<template>
  <div>
    <el-dialog
      title="规则链版本"
      :visible.sync="dialogVisible"
      width="80%"
      @close="closeModal"
      :close-on-click-modal="false"
    >
      <div class="version-container">
        <!-- 版本列表 -->
        <el-table
          :data="versionList"
          :loading="loading"
          row-key="id"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="version" label="版本号" min-width="200" />
          <el-table-column prop="id" label="版本ID" min-width="200" />
          <el-table-column prop="createUser" label="创建人" min-width="200" />
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="180"
            sortable="custom"
          />
          <el-table-column label="操作" width="300">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-refresh"
                @click="handleCompare(scope.row)"
              >
                与当前比较
              </el-button>
              <el-button
                size="mini"
                icon="el-icon-refresh-left"
                @click="handleRestore(scope.row)"
              >
                还原版本
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="versionList.length"
          :current-page="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 16px; text-align: right;"
        />
      </div>
    </el-dialog>

    <!-- 版本对比弹窗 -->
    <el-dialog
      title="版本对比"
      :visible.sync="compareModalVisible"
      width="90%"
      @close="closeCompareModal"
      :close-on-click-modal="false"
    >
      <div class="compare-container">
        <json-diff
          :json-source-left="currentVersionData"
          :json-source-right="rollbackVersionData"
          :show-heading="true"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import JsonDiff from "vue-json-diff";
import {
  getVersionList,
  rollbackVersion,
  compareVersion,
} from "@/api/ruleEngine/index.js";

export default {
  name: "VersionModal",
  components: {
    JsonDiff,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    chainId: {
      type: String,
      default: "",
    },
    chainName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      versionList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 当前版本数据
      currentVersionData: {},
      // 回滚版本数据
      rollbackVersionData: {},
      compareModalVisible: false,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadVersionList();

        // 初始化分页数据
        this.pagination.current = 1;
        this.pagination.pageSize = 10;
        this.pagination.total = 0;

        // 清空版本列表
        this.versionList = [];

        // 清空对比数据
        this.currentVersionData = {};
        this.rollbackVersionData = {};
        this.compareModalVisible = false;
      }
    },
  },
  methods: {
    closeModal() {
      this.$emit("update:visible", false);
    },

    // 加载版本列表
    async loadVersionList() {
      if (!this.chainId) return;
      this.loading = true;
      try {
        const res = await getVersionList({
          chainId: this.chainId,
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
        });

        if (res && res.success) {
          this.versionList = res.data || [];
          this.pagination.total = res.total || 0;
        }
      } catch (error) {
        this.$message.error("获取版本列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 处理表格变化
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.loadVersionList();
    },

    // 处理分页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.current = 1;
      this.loadVersionList();
    },

    // 处理当前页变化
    handleCurrentChange(page) {
      this.pagination.current = page;
      this.loadVersionList();
    },

    // 处理排序变化
    handleSortChange(sorter) {
      // 可以根据需要实现排序逻辑
      console.log("Sort change:", sorter);
    },

    // 版本对比
    async handleCompare(record) {
      try {
        const res = await compareVersion({
          chainId: this.chainId,
          historyId: record.id,
        });
        const { currentInfo, rollbackInfo } = res?.data || {};
        this.currentVersionData = currentInfo;
        this.rollbackVersionData = rollbackInfo;
        this.compareModalVisible = true;
      } catch (error) {
        console.error("加载版本对比数据失败:", error);
      }
    },

    // 关闭对比弹窗
    closeCompareModal() {
      this.compareModalVisible = false;
      this.currentVersionData = {};
      this.rollbackVersionData = {};
    },

    // 恢复版本
    async handleRestore(record) {
      this.$confirm(`确定要还原到版本 ${record.version} 吗？`, "确认还原", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          this.loading = true;
          try {
            const res = await rollbackVersion({
              chainId: this.chainId,
              historyId: record.id,
            });

            if (res && res.success) {
              this.$message.success("版本还原成功");
              this.$emit("restore", record);
              this.closeModal();
            }
          } catch (error) {
            this.$message.error("版本还原失败");
          } finally {
            this.loading = false;
          }
        })
        .catch(() => {
          // 用户取消
        });
    },
  },
};
</script>

<style lang="less" scoped>
.version-container {
  .el-table {
    margin-bottom: 16px;
  }
}

.compare-container {
  height: 600px;
  overflow: auto;

  :deep(.json-diff) {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 12px;
    line-height: 1.5;
  }
}
</style>
