<template>
  <div class="test-components">
    <h2>规则引擎组件测试页面</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>NodeDrawer 组件测试</span>
      </div>
      <el-button @click="testNodeDrawer">测试 NodeDrawer</el-button>
      
      <node-drawer
        v-model="drawerVisible"
        :nodeData="testNodeData"
        :isDebug="true"
        chainId="test-chain"
        chainInstanceId="test-instance"
        chainName="测试规则链"
        @save="handleSave"
        @close="handleClose"
      />
    </el-card>

    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>DebugTable 组件测试</span>
      </div>
      <debug-table :debug-list="mockDebugData" />
    </el-card>

    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>NodeForms 组件测试</span>
      </div>
      <el-tabs v-model="activeFormTab">
        <el-tab-pane label="IF表单" name="IF">
          <i-f-form :formData="ifFormData" @update:formData="updateIfFormData" />
        </el-tab-pane>
        <el-tab-pane label="THEN表单" name="THEN">
          <t-h-e-n-form :formData="thenFormData" @update:formData="updateThenFormData" />
        </el-tab-pane>
        <el-tab-pane label="SWITCH表单" name="SWITCH">
          <s-w-i-t-c-h-form :formData="switchFormData" @update:formData="updateSwitchFormData" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-card>
      <div slot="header">
        <span>测试结果</span>
      </div>
      <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script>
import NodeDrawer from './NodeDrawer.vue';
import DebugTable from './DebugTable.vue';
import IFForm from './nodeForms/IFForm.vue';
import THENForm from './nodeForms/THENForm.vue';
import SWITCHForm from './nodeForms/SWITCHForm.vue';
import { mockDebugList } from '../mock.js';

export default {
  name: 'TestComponents',
  components: {
    NodeDrawer,
    DebugTable,
    IFForm,
    THENForm,
    SWITCHForm,
  },
  data() {
    return {
      drawerVisible: false,
      activeFormTab: 'IF',
      testNodeData: {
        properties: {
          nodeType: 'IF',
          name: '测试IF节点',
          description: '这是一个测试IF节点',
          scriptName: 'test-script',
        },
        description: '测试节点描述',
      },
      mockDebugData: mockDebugList,
      ifFormData: {
        isBuiltIn: false,
        scriptName: '',
        scriptCode: 'return a > b;',
        condition: 'a > b',
      },
      thenFormData: {
        idType: 'NORMAL',
        paramType: 'normal',
        isBuiltIn: false,
        scriptName: '',
        scriptCode: 'console.log("执行THEN节点");',
      },
      switchFormData: {
        switchType: 'condition',
        conditions: [
          {
            name: '条件1',
            expression: 'value > 10',
            outputLabel: 'high',
          },
          {
            name: '条件2', 
            expression: 'value <= 10',
            outputLabel: 'low',
          }
        ],
      },
      testResults: {
        nodeDrawer: null,
        forms: {
          if: null,
          then: null,
          switch: null,
        }
      }
    };
  },
  methods: {
    testNodeDrawer() {
      this.drawerVisible = true;
    },
    handleSave(formData) {
      this.testResults.nodeDrawer = formData;
      this.$message.success('NodeDrawer 保存成功');
      console.log('NodeDrawer 保存数据:', formData);
    },
    handleClose() {
      this.drawerVisible = false;
    },
    updateIfFormData(newData) {
      this.ifFormData = { ...this.ifFormData, ...newData };
      this.testResults.forms.if = this.ifFormData;
    },
    updateThenFormData(newData) {
      this.thenFormData = { ...this.thenFormData, ...newData };
      this.testResults.forms.then = this.thenFormData;
    },
    updateSwitchFormData(newData) {
      this.switchFormData = { ...this.switchFormData, ...newData };
      this.testResults.forms.switch = this.switchFormData;
    },
  },
};
</script>

<style lang="less" scoped>
.test-components {
  padding: 20px;
  
  pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
  }
}
</style>
