<template>
  <el-dialog
    title="自测"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleCancel"
    :close-on-click-modal="false"
  >
    <div class="code-block-container">
      <div
        class="mode-indicator"
        :class="{ 'code-mode': form.params, 'api-mode': !form.params }"
      >
        <el-tooltip :content="getTooltipText" placement="top">
          <div class="mode-content">
            <div class="mode-icon">
              <i
                :class="form.params ? 'el-icon-document' : 'el-icon-connection'"
              />
            </div>
            <span class="mode-text">{{
              form.params ? "自定义代码块模式" : "实例上下文模式"
            }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="代码块" prop="params">
        <sm-code-editor
          v-model="form.params"
          mode="json"
          :validate="false"
          :show-error="false"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleTest">开始自测</el-button>
      </el-form-item>
    </el-form>

    <div v-if="testResults.length > 0" class="test-results">
      <el-card
        v-for="(result, index) in testResults"
        :key="index"
        :class="['result-card', { success: result.success }]"
        style="margin-bottom: 16px;"
      >
        <div slot="header" class="card-title">
          <div class="title-left">
            <i
              :class="result.success ? 'el-icon-success' : 'el-icon-warning'"
            />
            <span>{{ result.title }}</span>
          </div>
          <el-tooltip content="复制结果" placement="top">
            <el-button
              type="text"
              class="copy-btn"
              @click="() => copyContent(result)"
            >
              <i class="el-icon-document-copy" />
            </el-button>
          </el-tooltip>
        </div>
        <div v-if="result.data">
          <pre class="result-content">{{ result.data }}</pre>
        </div>
        <div v-else class="error-message">{{ result.error || "测试失败" }}</div>
      </el-card>
    </div>
  </el-dialog>
</template>

<script>
import SmCodeEditor from "@/components/CodeEditor/index.vue";
import { generateLogicFlowEL, nodeSelfTest } from "@/api/ruleEngine";
export default {
  name: "TestModal",
  components: {
    SmCodeEditor,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectionInfo: {
      type: Object,
      default: () => ({}),
    },
    chainInstanceId: {
      type: String,
      default: "",
    },
    chainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      form: {
        params: "",
      },
      rules: {
        params: [],
      },
      testResults: [],
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    getTooltipText() {
      return this.form.params
        ? "当前为自定义代码块模式，将使用您输入的JSON数据进行测试"
        : "当前为实例上下文模式，将使用规则链实例的上下文数据进行测试";
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.resetForm();
      }
    },
  },
  methods: {
    handleCancel() {
      this.$emit("update:visible", false);
      this.resetForm();
    },
    resetForm() {
      this.form = {
        params: "",
      };
      this.testResults = [];
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    async handleTest() {
      this.testResults = [];
      let testParams = {};
      if (this.form.params) {
        testParams = this.form;
      } else {
        if (!this.chainInstanceId) {
          this.$message.error("实例上下文模式下，实例参数不能为空");
          return;
        }
        testParams = {
          chainInstanceId: this.chainInstanceId,
        };
      }

      const res = await nodeSelfTest({
        ...testParams,
        ...this.selectionInfo,
        chainId: this.chainId,
      });

      const res2 = await generateLogicFlowEL({
        ...this.selectionInfo,
      });

      // 处理测试结果
      this.testResults = [
        {
          title: "自测结果",
          success: res && res.success,
          data: res?.data,
          error: res?.message,
        },
        {
          title: "表达式预览",
          success: res2 && res2.success,
          data: res2?.data,
          error: res2?.message,
        },
      ];
    },

    copyContent(result) {
      const content = result.data || result.error || "";
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(content)
          .then(() => {
            this.$message.success("内容已复制到剪贴板");
          })
          .catch(() => {
            this.$message.error("复制失败");
          });
      } else {
        // 降级方案
        const textArea = document.createElement("textarea");
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand("copy");
          this.$message.success("内容已复制到剪贴板");
        } catch (err) {
          this.$message.error("复制失败");
        }
        document.body.removeChild(textArea);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.code-block-container {
  margin-bottom: 16px;

  .mode-indicator {
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    background: #fafafa;
    transition: all 0.3s ease;

    &.code-mode {
      border-color: #1890ff;
      background: #f0f7ff;

      .mode-icon i {
        color: #1890ff;
      }
    }

    &.api-mode {
      border-color: #52c41a;
      background: #f6ffed;

      .mode-icon i {
        color: #52c41a;
      }
    }

    .mode-content {
      display: flex;
      align-items: center;

      .mode-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      .mode-text {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}

.test-results {
  margin-top: 24px;

  .result-card {
    &.success {
      border-color: #52c41a;

      :deep(.el-card__header) {
        background: #f6ffed;
        border-bottom-color: #52c41a;
      }
    }

    &:not(.success) {
      border-color: #ff4d4f;

      :deep(.el-card__header) {
        background: #fff2f0;
        border-bottom-color: #ff4d4f;
      }
    }

    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left {
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          font-size: 16px;
        }

        span {
          font-weight: 500;
        }
      }

      .copy-btn {
        padding: 4px;

        i {
          font-size: 14px;
        }
      }
    }

    .result-content {
      background: #f8f8f8;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 12px;
      margin: 0;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 12px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 300px;
      overflow-y: auto;
    }

    .error-message {
      color: #ff4d4f;
      font-size: 14px;
      padding: 8px 0;
    }
  }
}
</style>
