<template>
  <div class="toolbar">
    <div class="toolbar-item">
      <el-button
        :icon="isSelecting ? 'el-icon-close' : 'el-icon-s-operation'"
        :type="isSelecting ? 'default' : 'primary'"
        size="small"
        @click="handleClick"
        class="mr-8"
      >
        {{ isSelecting ? "关闭框选" : "框选" }}
      </el-button>

      <el-button
        :icon="isDebug ? 'el-icon-close' : 'el-icon-cpu'"
        :type="isDebug ? 'default' : 'primary'"
        size="small"
        @click="handleDebug"
        class="mr-8"
      >
        {{ isDebug ? "退出调试" : "调试" }}
      </el-button>

      <el-dropdown @command="handleCommand" class="toolbar-group">
        <el-button type="primary" size="small">
          工具箱<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="version">
            <i class="el-icon-time"></i>
            <span>版本控制</span>
          </el-dropdown-item>
          <el-dropdown-item command="export">
            <i class="el-icon-download"></i>
            <span>导出</span>
          </el-dropdown-item>
          <el-dropdown-item command="import">
            <i class="el-icon-upload2"></i>
            <span>导入</span>
          </el-dropdown-item>
          <el-dropdown-item divided command="toggleExpression">
            <i
              :class="showExpressionPreview ? 'el-icon-view' : 'el-icon-view'"
            ></i>
            <span>{{
              showExpressionPreview ? "隐藏表达式预览" : "显示表达式预览"
            }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <input
      type="file"
      ref="fileInput"
      accept=".json"
      style="display: none"
      @change="handleFileSelected"
    />
  </div>
</template>

<script>
export default {
  name: "FlowToolbar",
  props: {
    isSelecting: {
      type: Boolean,
      default: false,
    },
    showExpressionPreview: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isDebug: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.isDebug = localStorage.getItem("isDebug") === "true";
    });
  },
  methods: {
    handleClick() {
      this.$emit("selection-change");
    },
    handleDebug() {
      if (!this.isDebug) {
        this.$confirm("确定要进入调试模式吗？", "调试确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.isDebug = true;
            localStorage.setItem("isDebug", "true");
            this.$emit("debug");
          })
          .catch(() => {
            // 用户取消
          });
      } else {
        this.$confirm("确定要退出调试模式吗？", "调试确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.isDebug = false;
            localStorage.setItem("isDebug", "false");
            this.$emit("debug-close");
          })
          .catch(() => {
            // 用户取消
          });
      }
    },
    handleCommand(command) {
      switch (command) {
        case "version":
          this.handleVersionControl();
          break;
        case "export":
          this.handleExport();
          break;
        case "import":
          this.handleImport();
          break;
        case "toggleExpression":
          this.handleToggleExpression();
          break;
      }
    },
    handleVersionControl() {
      this.$emit("version-control");
    },
    handleExport() {
      this.$emit("export");
    },
    handleImport() {
      this.$refs.fileInput.click();
    },
    handleToggleExpression() {
      this.$emit("toggle-expression");
    },
    handleFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (file.type !== "application/json" && !file.name.endsWith(".json")) {
        this.$message.error("请选择JSON格式的文件");
        event.target.value = "";
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target.result);
          if (!jsonData.nodes || !jsonData.edges) {
            throw new Error("无效的画布数据格式");
          }
          this.$emit("import", jsonData);
        } catch (error) {
          this.$message.error("文件格式错误：" + error.message);
        }
        event.target.value = "";
      };
      reader.onerror = () => {
        this.$message.error("文件读取失败");
        event.target.value = "";
      };
      reader.readAsText(file);
    },
  },
};
</script>

<style lang="less" scoped>
.toolbar {
  position: absolute;
  top: 24px;
  left: 24px;
  z-index: 19;

  .toolbar-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .mr-8 {
      margin-right: 8px;
    }

    .toolbar-group {
      margin-left: 8px;
    }
  }
}
</style>
