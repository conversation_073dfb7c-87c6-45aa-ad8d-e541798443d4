<template>
  <div class="debug-table">
    <el-table :data="dataSource" size="small" border stripe style="width: 100%">
      <el-table-column prop="dataCode" label="数据编码" width="120" />
      <el-table-column prop="dataType" label="数据类型" width="100" />
      <el-table-column prop="startTime" label="开始时间" width="160" />
      <el-table-column prop="endTime" label="结束时间" width="160" />
      <el-table-column prop="successFlag" label="执行结果" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.successFlag ? 'success' : 'danger'">
            {{ scope.row.successFlag ? "成功" : "失败" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="errorMsg" label="错误信息" />
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            size="mini"
            @click="showModal(scope.row)"
          >
            参数
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="dataSource.length > 10"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="dataSource.length"
      layout="total, prev, pager, next"
      @current-change="handlePageChange"
      style="margin-top: 20px; text-align: right;"
    />

    <el-dialog
      title="数据值"
      :visible.sync="modalVisible"
      width="60%"
      @close="closeModal"
    >
      <div class="data-detail">
        <json-viewer
          :value="
            currentRecord.dataValue ? JSON.parse(currentRecord.dataValue) : {}
          "
          :expand-depth="3"
          copyable
          boxed
          sort
          theme="jv-light"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import JsonViewer from "vue-json-viewer";
import "vue-json-viewer/style.css";

export default {
  name: "DebugTable",
  components: {
    JsonViewer,
  },
  props: {
    debugList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      modalVisible: false,
      currentRecord: null,
      currentPage: 1,
      pageSize: 10,
    };
  },
  computed: {
    dataSource() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.debugList.slice(start, end).map((item, index) => ({
        ...item,
        key: start + index,
      }));
    },
  },
  methods: {
    showModal(record) {
      try {
        // 尝试解析JSON字符串
        if (record.dataValue) {
          JSON.parse(record.dataValue);
        }
        this.currentRecord = record;
        this.modalVisible = true;
      } catch (e) {
        this.$message.error("数据格式不是有效的JSON");
      }
    },
    closeModal() {
      this.modalVisible = false;
      this.currentRecord = null;
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
  },
};
</script>

<style lang="less" scoped></style>
