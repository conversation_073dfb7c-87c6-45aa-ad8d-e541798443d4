<template>
  <div>
    <el-form-item
      label="异步循环模式"
      :rules="[{ required: true, message: '请选择是否支持异步循环模式' }]"
      prop="parallel"
    >
      <el-tooltip content="开启后循环子项将并行执行">
        <el-checkbox v-model="formData.parallel"></el-checkbox>
      </el-tooltip>
    </el-form-item>

    <el-form-item
      label="循环类型"
      prop="loopType"
      :rules="[{ required: true, message: '请选择循环类型' }]"
    >
      <el-radio-group v-model="formData.loopType">
        <el-radio-button label="FIXED">固定次数</el-radio-button>
        <el-radio-button label="DYNAMIC">动态次数</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item
      v-if="formData.loopType === 'FIXED'"
      label="固定次数"
      prop="loopCount"
      :rules="[{ required: true, message: '请输入循环次数' }]"
    >
      <el-input-number
        v-model="formData.loopCount"
        :min="1"
        :max="999"
        placeholder="请输入循环次数"
        style="width: 100%"
      />
    </el-form-item>

    <common-code-form
      v-if="formData.loopType === 'DYNAMIC'"
      :formData="formData"
      codeTips="代码需要返回一个整数值作为循环次数。例如: list.size() 或 count + 1"
    />
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";

export default {
  name: "FORForm",
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        loopType: "FIXED",
        loopCount: 1,
        loopCode: "",
        parallel: false,
      }),
    },
  },
};
</script>

<style lang="less" scoped></style>
