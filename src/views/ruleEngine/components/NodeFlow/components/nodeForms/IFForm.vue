<template>
  <div>
    <common-code-form
      :formData="formData"
      codeTips="条件代码需要返回布尔值(true/false)。例如: a > b 或 str.equals('test')"
    />
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";

export default {
  name: "IFForm",
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        condition: "",
      }),
    },
  },
};
</script>
