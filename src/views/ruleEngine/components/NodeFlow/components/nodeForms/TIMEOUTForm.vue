<template>
  <div>
    <el-form-item
      label="超时时间"
      prop="timeout"
      :rules="[{ required: true, message: '请输入超时时间' }]"
    >
      <el-input-number
        v-model="formData.timeout"
        :min="1"
        :max="999999"
        style="width: 100%"
        placeholder="请输入超时时间(单位:秒)"
      />
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: "TIMEOUTForm",
  components: {},
  props: {
    formData: {
      type: Object,
      default: () => ({
        timeout: 1000,
        timeoutHandler: "",
      }),
    },
  },
};
</script>
