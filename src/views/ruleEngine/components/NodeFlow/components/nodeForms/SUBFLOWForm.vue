<template>
  <div>
    <el-form-item
      label="子规则链"
      prop="subChainId"
      :rules="[{ required: true, message: '请选择子规则链' }]"
    >
      <el-select
        v-model="formData.subChainId"
        placeholder="请选择子规则链"
        style="width: 100%"
        filterable
        @change="handleChange"
      >
        <el-option
          v-for="item in chainList"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import { queryChainList } from "@/api/ruleEngine";
export default {
  name: "SUBFLOWForm",
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
    chainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      chainList: [],
    };
  },
  async mounted() {
    await this.getChainList();
  },
  methods: {
    async getChainList() {
      try {
        // 使用getDicts方法获取规则链列表
        const response = await queryChainList({ chainName: "" });
        this.chainList =
          response.data
            ?.map((item) => ({
              label: item.chainName,
              value: item.id,
            }))
            ?.filter((x) => {
              return x.value !== this.chainId;
            }) || [];
      } catch (error) {
        console.error("获取规则链列表失败:", error);
        // 提供默认的规则链选项
        this.chainList = [];
      }
    },
    handleChange(value) {
      const chainName = this.chainList.find((item) => item.value === value)
        ?.label;
      this.formData.scriptName = chainName;
    },
  },
};
</script>
