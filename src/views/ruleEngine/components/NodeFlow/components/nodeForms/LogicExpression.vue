<template>
  <div class="logic-expression">
    <!-- 逻辑类型选择 -->
    <div class="form-section">
      <el-form-item v-if="needLogicType" label="逻辑类型" required>
        <div class="logic-header">
          <el-select v-model="currentValue.type" style="width: 200px">
            <el-option v-if="!isRoot" label="节点" value="NODE"></el-option>
            <el-option label="与运算(AND)" value="AND"></el-option>
            <el-option label="或运算(OR)" value="OR"></el-option>
            <el-option label="非运算(NOT)" value="NOT"></el-option>
          </el-select>
          <el-button
            type="text"
            class="add-btn"
            @click="addChild"
            :disabled="currentValue.type === 'NODE'"
          >
            <i class="el-icon-circle-plus"></i>添加子表达式
          </el-button>
        </div>
      </el-form-item>
    </div>

    <!-- 子表达式区域 -->
    <div v-if="currentValue.children.length > 0" class="logic-children">
      <div
        v-for="(child, index) in currentValue.children"
        :key="index"
        class="logic-child"
      >
        <div class="logic-child-header">
          <div class="header-left">
            <span class="child-tag">子表达式</span>
            <span class="child-index">{{ index + 1 }}</span>
          </div>
          <el-button type="text" class="delete-btn" @click="removeChild(index)">
            <i class="el-icon-delete"></i>删除
          </el-button>
        </div>
        <logic-expression
          v-model="currentValue.children[index]"
          :is-root="false"
          :need-logic-type="true"
        />
      </div>
    </div>

    <!-- 节点输入区域 -->
    <div
      v-if="currentValue.type === 'NODE' || !currentValue.children.length"
      class="form-section"
    >
      <el-form-item label="名称" required>
        <el-input v-model="currentValue.name" />
      </el-form-item>

      <common-code-form
        :formData="currentValue"
        codeTips="代码块需要返回一个布尔值(true/false)。例如: true 或 false"
      />
    </div>

    <!-- 实时预览区域 -->
    <div v-if="isRoot" class="expression-preview">
      <div class="preview-header">
        <div class="header-left">
          <i class="el-icon-s-operation"></i>
          <span>表达式预览</span>
        </div>
        <el-tooltip content="复制表达式">
          <el-button type="text" class="copy-btn" @click="copyExpression">
            <i class="el-icon-document-copy"></i>复制
          </el-button>
        </el-tooltip>
      </div>
      <pre class="preview-content">{{ formattedExpression }}</pre>
    </div>
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";
export default {
  name: "LogicExpression",
  components: {
    CommonCodeForm,
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        type: "AND",
        children: [],
        code: "",
        name: "",
      }),
    },
    isRoot: {
      type: Boolean,
      default: true,
    },
    needLogicType: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    currentValue: {
      get() {
        return this.value;
      },
      set(val) {
        if (this.isRoot && val.type === "NODE") {
          val.type = "AND";
        }
        this.$emit("input", val);
      },
    },
    formattedExpression() {
      return this.generateExpression(this.currentValue);
    },
  },
  methods: {
    addChild() {
      if (this.currentValue.type !== "NODE") {
        this.currentValue.children.push({
          type: "NODE",
          children: [],
          code: "",
          name: "",
        });
      }
    },
    removeChild(index) {
      this.$confirm("确定要删除该表达式吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.currentValue.children.splice(index, 1);
        })
        .catch(() => {});
    },
    generateExpression(node) {
      // 处理空节点
      if (!node) return "";

      // 处理叶子节点
      if (!node.children || node.children.length === 0) {
        return node.name || node.code || "";
      }

      // 递归处理子节点
      const childExpressions = node.children
        .map((child) => {
          // 如果子节点有自己的子节点，递归生成表达式
          if (child.children && child.children.length > 0) {
            return this.generateExpression(child);
          }
          // 如果子节点是逻辑节点但没有子节点，生成简单表达式
          else if (child.type && child.type !== "NODE") {
            return `${child.type}(${child.name || child.code || ""})`;
          }
          // 普通节点直接返回值
          return child.name || child.code || "";
        })
        .filter((exp) => exp); // 过滤空值

      // 如果没有有效的子表达式，返回当前节点的值
      if (childExpressions.length === 0) {
        return node.name || node.code || "";
      }

      // 生成当前层级的表达式
      return `${node.type}(${childExpressions.join(", ")})`;
    },
    copyExpression() {
      try {
        navigator.clipboard.writeText(this.formattedExpression);
        this.$message.success("复制成功");
      } catch (err) {
        this.$message.error("复制失败，请手动复制");
      }
    },
  },
  watch: {
    "currentValue.type": {
      handler(newType) {
        if (this.isRoot && newType === "NODE") {
          this.currentValue.type = "AND";
          this.$message.warning("根节点不能设置为普通节点类型");
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.logic-expression {
  .form-section {
    margin-bottom: 20px;
  }

  .logic-header {
    display: flex;
    align-items: center;
    gap: 10px;

    .add-btn {
      color: #409eff;
      padding: 0;
    }
  }

  .logic-children {
    margin-left: 20px;
    border-left: 2px solid #e4e7ed;
    padding-left: 20px;

    .logic-child {
      margin-bottom: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 15px;
      background-color: #fafafa;

      .logic-child-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e4e7ed;

        .header-left {
          display: flex;
          align-items: center;
          gap: 8px;

          .child-tag {
            color: #606266;
            font-size: 14px;
            font-weight: 500;
          }

          .child-index {
            background-color: #409eff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }

        .delete-btn {
          color: #f56c6c;
          padding: 0;
        }
      }
    }
  }

  .expression-preview {
    margin-top: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #606266;
        font-weight: 500;
      }

      .copy-btn {
        color: #409eff;
        padding: 0;
      }
    }

    .preview-content {
      padding: 15px;
      margin: 0;
      background-color: #fafafa;
      border: none;
      font-family: "Courier New", monospace;
      font-size: 13px;
      line-height: 1.5;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>
