<template>
  <div>
    <el-form-item
      label="节点类型"
      prop="idType"
      :rules="[{ required: true, message: '请选择节点类型' }]"
    >
      <el-radio-group v-model="formData.idType">
        <el-radio-button label="NORMAL">普通节点</el-radio-button>
        <el-radio-button label="PRE">前置节点</el-radio-button>
        <el-radio-button label="FINALLY">后置节点</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item
      label="参数类型"
      prop="paramType"
      :rules="[{ required: true, message: '请选择参数类型' }]"
    >
      <el-radio-group v-model="formData.paramType">
        <el-radio-button label="normal">无</el-radio-button>
        <el-radio-button label="data">data</el-radio-button>
        <el-radio-button label="bind">bind</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <template v-if="!formData.isBuiltIn">
      <!-- data参数类型 - 允许JSON输入 -->
      <el-form-item
        v-if="formData.paramType === 'data'"
        label="参数值"
        prop="paramsValue"
        :rules="[{ required: true, message: '请输入参数值' }]"
      >
        <sm-code-editor
          v-model="formData.paramsValue"
          mode="json"
          :validate="true"
          :show-error="true"
          height="200px"
        />
        <div class="tip-text">请输入有效的JSON格式数据</div>
      </el-form-item>

      <!-- bind参数类型 - 输入标签 -->
      <el-form-item
        v-if="formData.paramType === 'bind'"
        label="关键词"
        prop="paramsValue"
        :rules="[{ required: true, message: '请输入关键词' }]"
      >
        <el-select
          v-model="formData.paramsValue"
          placeholder="请选择或输入标签"
          style="width: 100%"
          multiple
          filterable
          allow-create
          default-first-option
        >
          <el-option v-for="tag in []" :key="tag" :value="tag" :label="tag" />
        </el-select>
      </el-form-item>
    </template>

    <common-code-form
      :formData="formData"
      @update:formData="handleFormDataUpdate"
    />
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";
import SmCodeEditor from "@/components/CodeEditor/index.vue";

export default {
  name: "THENForm",
  components: {
    CommonCodeForm,
    SmCodeEditor,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        idType: "NORMAL",
        paramType: "normal",
        paramsValue: undefined,
        params: {},
        serviceCode: "",
        dataCode: "",
        objectClass: "",
        dbType: "mysql",
        customSql: "",
        expression: "",
        isBuiltIn: false,
        scriptName: "",
        scriptCode: "",
      }),
    },
  },
  data() {
    return {
      bindTags: [], // 可选的绑定标签
    };
  },
  async created() {
    await this.loadBindTags();
  },
  methods: {
    async loadBindTags() {
      try {
        // 获取绑定标签字典
        const response = await this.getDicts("rule_chain_bind_tags");
        this.bindTags = response.data?.map((item) => item.dictValue) || [];
      } catch (error) {
        console.error("获取绑定标签失败:", error);
        this.bindTags = ["input", "output", "context", "metadata"];
      }
    },
    handleFormDataUpdate(newFormData) {
      this.$emit("update:formData", {
        ...this.formData,
        ...newFormData,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.tip-text {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.logic-card {
  background: #fafafa;
  margin-top: 16px;

  :deep(.el-card__body) {
    padding: 12px;
  }
}
</style>
