<template>
  <div>
    <el-form-item
      label="异步循环模式"
      :rules="[{ required: true, message: '请选择是否支持异步循环模式' }]"
      prop="parallel"
    >
      <el-tooltip content="开启后循环子项将并行执行">
        <el-checkbox v-model="formData.parallel"></el-checkbox>
      </el-tooltip>
    </el-form-item>

    <common-code-form
      :formData="formData"
      codeTips="条件代码需要返回布尔值(true/false)。例如: count &lt; 10 或 !list.isEmpty()"
    />
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";

export default {
  name: "WHILEForm",
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        condition: "",
        parallel: false,
      }),
    },
  },

  methods: {},
  watch: {},
};
</script>

<style lang="less" scoped></style>
