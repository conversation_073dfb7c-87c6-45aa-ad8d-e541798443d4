<template>
  <div>
    <el-form-item
      label="异步循环模式"
      :rules="[{ required: true, message: '请选择是否支持异步循环模式' }]"
      prop="parallel"
    >
      <el-tooltip content="开启后循环子项将并行执行">
        <el-checkbox v-model="formData.parallel"></el-checkbox>
      </el-tooltip>
    </el-form-item>
    <common-code-form
      :formData="formData"
      codeTips="代码需要返回一个迭代器对象。例如: list.iterator() 或 map.values().iterator()"
    />
  </div>
</template>

<script>
import CommonCodeForm from "./CommonCodeForm.vue";

export default {
  name: "ITERATORForm",
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        iteratorCode: "",
        parallel: false,
      }),
    },
  },
};
</script>

<style lang="less" scoped></style>
