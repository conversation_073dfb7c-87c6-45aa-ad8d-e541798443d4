<template>
  <el-dialog
    :title="isEdit ? '编辑规则链' : '新增规则链'"
    :visible.sync="modalVisible"
    width="600px"
    @close="handleCancel"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="规则链名称" prop="chainName">
        <el-input
          v-model="formData.chainName"
          placeholder="请输入规则链名称"
          :disabled="isEdit"
        />
      </el-form-item>

      <el-form-item label="描述" prop="chainDesc">
        <el-input
          v-model="formData.chainDesc"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "RuleChainModal",
  props: {
    modalVisible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({
        chainName: "",
        chainDesc: "",
      }),
    },
    isEdit: {
      type: Boolean,
      default: true, // 默认是编辑模式
    },
  },
  data() {
    return {
      rules: {
        chainName: [
          { required: true, message: "请输入规则链名称", trigger: "blur" },
          { max: 32, message: "规则链名称最多32个字符", trigger: "blur" },
        ],
        chainDesc: [
          { max: 64, message: "规则链描述最多64个字符", trigger: "blur" },
        ],
      },
    };
  },

  methods: {
    async handleSubmit() {
      const valid = await this.$refs.formRef.validate();
      if (valid) {
        this.handleCancel(true);
      }
    },
    handleCancel(isSubmit = false) {
      this.$emit("cancel", isSubmit);
      this.resetForm();
    },
    resetForm() {
      this.$refs.formRef?.resetFields();
      this.$refs.formRef?.clearValidate();
    },
  },
};
</script>

<style lang="less" scoped></style>
