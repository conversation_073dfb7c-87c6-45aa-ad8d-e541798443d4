<!-- 标签维护页面 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['system:tag:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['system:tag:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh-left" @click.stop="resetQuery"
            >重置</el-button
          >
        </div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <!-- 批量新增抽屉 -->
    <BatchAddDrawer ref="batchAddDrawer" @refresh="loadData" />
  </div>
</template>

<script>
import {
  getTagList,
  saveTag,
  removeTag,
  exportTag,
  getTagTypeList,
} from "@/api/system/tag";
import { initParams } from "@/utils/buse";
import checkPermission from "@/utils/permission.js";
import exportMixin from "@/mixin/export.js";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";
import BatchAddDrawer from "./components/BatchAddDrawer.vue";

export default {
  name: "TagManage",
  components: { Timeline, BatchAddDrawer },
  mixins: [exportMixin],
  data() {
    return {
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "tagId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      // 标签属性选项
      tagAttributeOptions: [],
      // 标签分类选项
      tagTypeOptions: [],
      recordList: [],
    };
  },
  created() {
    // 获取标签属性字典
    this.getDicts("tag_attribute").then((response) => {
      this.tagAttributeOptions = response.data;
    });

    // 获取标签分类
    this.getTagTypeOptions();

    // 初始化参数
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },
  computed: {
    // 表格列配置
    tableColumn() {
      return [
        { field: "tagAttributeName", title: "标签属性" },
        { field: "tagType", title: "标签分类" },
        { field: "tagName", title: "标签名称" },
        { field: "createTime", title: "创建时间" },
        { field: "createByName", title: "创建人" },
      ];
    },
    // 筛选项配置
    filterOptions() {
      return {
        showCount: 3,
        layout: "right",
        inline: true,
        labelWidth: "100px",
        config: [
          {
            field: "tagAttributeValue",
            title: "标签属性",
            element: "el-select",
            props: {
              options: this.tagAttributeOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
              placeholder: "请选择标签属性",
              clearable: true,
              filterable: true,
            },
          },
          {
            field: "tagType",
            title: "标签分类",
            element: "el-select",
            props: {
              options: this.tagTypeOptions,
              placeholder: "请选择标签分类",
              clearable: true,
              filterable: true,
            },
          },
          {
            field: "tagName",
            title: "标签名称",
            element: "el-input",
            props: {
              placeholder: "请输入标签名称",
              clearable: true,
            },
          },
        ],
        params: this.params,
      };
    },
    // 弹窗配置
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: checkPermission(["system:tag:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["system:tag:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "tagAttributeValue",
            title: "标签属性",
            element: "el-select",
            props: {
              options: this.tagAttributeOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
              placeholder: "请选择标签属性",
              clearable: true,
              filterable: true,
            },
          },
          {
            field: "tagType",
            title: "标签分类",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb);
              },
            },
          },
          {
            field: "tagName",
            title: "标签名称",
            rules: [
              { required: true, message: "请输入标签名称", trigger: "blur" },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["system:tag:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  methods: {
    querySearch(queryString, cb) {
      getTagTypeList({
        name: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    handleLog(row) {
      queryLog({ businessId: row.tagId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    // 获取标签分类选项
    getTagTypeOptions() {
      getTagTypeList({}).then((response) => {
        if (response.success && response.data) {
          this.tagTypeOptions = response.data.map((item) => {
            return {
              value: item,
              label: item,
            };
          });
        }
      });
    },
    // 查询标签列表
    loadData() {
      this.loading = true;
      const queryParams = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      getTagList(queryParams)
        .then((response) => {
          this.tableData = response.data || [];
          this.tablePage.total = response.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 查询按钮操作
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    // 重置按钮操作
    resetQuery() {
      this.params = initParams(this.filterOptions.config);
      this.handleQuery();
    },

    // 批量新增按钮操作
    rowAdd() {
      this.$refs.batchAddDrawer.open();
    },
    // 编辑按钮操作
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    // 提交表单
    modalConfirmHandler({ crudOperationType, ...formParams }) {
      const params = {
        ...formParams,
      };
      saveTag(params).then((response) => {
        if (response.success) {
          this.$message.success("保存成功");
          this.loadData();
        }
      });
    },
    // 删除按钮操作
    deleteRowHandler(row) {
      this.$confirm(
        '是否确认删除标签名称为"' + row.tagName + '"的数据项?',
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          return removeTag({ tagId: row.tagId });
        })
        .then((response) => {
          if (response.success) {
            this.$message.success("删除成功");
            this.loadData();
          }
        })
        .catch(() => {});
    },
    // 导出按钮操作
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
      };

      this.handleCommonExport(exportTag, params);
    },
  },
};
</script>

<style scoped>
.btn-wrap {
  display: flex;
  justify-content: flex-end;
}
</style>
