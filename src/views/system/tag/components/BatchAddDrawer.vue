<template>
  <el-drawer
    title="批量新增"
    :visible.sync="visible"
    :before-close="handleClose"
    size="60%"
    :destroy-on-close="true"
    class="drawer-container"
  >
    <div class="drawer-body">
      <div class="drawer-header">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAddRow"
          >新增</el-button
        >
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          @click="handleBatchDelete"
          >删除</el-button
        >
      </div>
      <div class="drawer-body">
        <el-table
          ref="multipleTable"
          :data="tableData"
          border
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="标签属性" width="180">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.tagAttribute"
                placeholder="请输入或选择"
                filterable
                clearable
                style="width: 100%"
                @change="handleAttributeChange(scope.$index)"
              >
                <el-option
                  v-for="item in tagAttributeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="标签分类" width="180">
            <template slot-scope="scope">
              <el-autocomplete
                v-model="scope.row.tagCategory"
                placeholder="请输入或选择"
                style="width: 100%"
                :fetch-suggestions="querySearch"
                @select="handleCategoryChange(scope.$index)"
                @change="handleCategoryChange(scope.$index)"
                clearable
              ></el-autocomplete>
            </template>
          </el-table-column>
          <el-table-column label="标签名称">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.tagName"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose" size="medium">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="loading"
          size="medium"
          >保 存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getTagTypeList, saveTag } from "@/api/system/tag";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "BatchAddDrawer",
  data() {
    return {
      visible: false,
      loading: false,
      tableData: [
        {
          id: Date.now(),
          tagAttribute: "",
          tagCategory: "",
          tagName: "",
        },
      ],
      multipleSelection: [],
      tagAttributeOptions: [],
      tagCategoryOptions: [],
    };
  },
  created() {
    // 获取标签属性字典
    this.getDicts("tag_attribute").then((response) => {
      this.tagAttributeOptions = response.data;
    });
  },
  methods: {
    // 打开抽屉
    open() {
      this.visible = true;
      this.tableData = [
        {
          id: Date.now(),
          tagAttribute: "",
          tagCategory: "",
          tagName: "",
        },
      ];
    },
    // 关闭抽屉
    handleClose() {
      this.visible = false;
    },

    // 添加行
    handleAddRow() {
      const lastRow = this.tableData[this.tableData.length - 1];
      // 自动带入上一行的标签属性和标签分类
      this.tableData.push({
        id: Date.now() + Math.random(),
        tagAttribute: lastRow.tagAttribute || "",
        tagCategory: lastRow.tagCategory || "",
        tagName: "",
      });
    },
    // 批量删除
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一行");
        return;
      }

      const selectedIds = this.multipleSelection.map((row) => row.id);
      this.tableData = this.tableData.filter(
        (row) => !selectedIds.includes(row.id)
      );

      // 如果删除所有行，添加一个空行
      if (this.tableData.length === 0) {
        this.tableData.push({
          id: Date.now(),
          tagAttribute: "",
          tagCategory: "",
          tagName: "",
        });
      }
    },
    // 选择变更
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 标签属性变更
    handleAttributeChange(index) {
      // 如果标签属性和标签分类都有值，检查标签名称是否重复
      this.checkDuplicateName(index);
    },
    // 查询标签分类建议
    querySearch(queryString, cb) {
      getTagTypeList({
        name: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    // 标签分类变更
    handleCategoryChange(index) {
      // 如果标签属性和标签分类都有值，检查标签名称是否重复
      this.checkDuplicateName(index);
    },
    // 检查标签名称是否重复
    checkDuplicateName(index) {
      const currentRow = this.tableData[index];
      if (
        !currentRow.tagAttribute ||
        !currentRow.tagCategory ||
        !currentRow.tagName
      ) {
        return;
      }

      const duplicates = this.tableData.filter(
        (row, idx) =>
          idx !== index &&
          row.tagAttribute === currentRow.tagAttribute &&
          row.tagCategory === currentRow.tagCategory &&
          row.tagName === currentRow.tagName
      );

      if (duplicates.length > 0) {
        this.$message.warning("相同标签属性和分类下，标签名称不能重复");
        this.tableData[index].tagName = "";
      }
    },
    // 保存
    async handleSave() {
      // 验证数据
      if (!this.validateData()) {
        return;
      }

      // 自动填充标签属性和标签分类
      this.autoFillAttributes();

      this.loading = true;
      try {
        // 构建保存数据
        const saveData = this.tableData.map((row) => ({
          tagAttributeValue: row.tagAttribute,
          tagType: row.tagCategory,
          tagName: row.tagName,
        }));

        // 调用保存接口
        const res = await saveTag({ tagList: saveData });
        if (res.code === "10000") {
          this.$message.success("保存成功");
          this.handleClose();
          this.$emit("refresh");
        } else {
          this.$message.error(res.msg || "保存失败");
        }
      } catch (error) {
        console.error("保存失败", error);
        this.$message.error("保存失败");
      } finally {
        this.loading = false;
      }
    },
    // 验证数据
    validateData() {
      // 过滤掉空行
      const validData = this.tableData.filter(
        (row) => row.tagName || row.tagAttribute || row.tagCategory
      );

      if (validData.length === 0) {
        this.$message.warning("请至少填写一行数据");
        return false;
      }

      // 检查必填项
      for (let i = 0; i < validData.length; i++) {
        const row = validData[i];
        if (row.tagAttribute && row.tagCategory && !row.tagName) {
          this.$message.warning(`第 ${i + 1} 行标签名称不能为空`);
          return false;
        }
      }

      return true;
    },
    // 自动填充标签属性和标签分类
    autoFillAttributes() {
      const firstRow = this.tableData[0];
      // 如果第一行没有填写标签属性和标签分类，不进行自动填充
      if (!firstRow.tagAttribute || !firstRow.tagCategory) {
        return;
      }

      // 检查是否只有第一行填写了标签属性和标签分类
      const otherRowsFilled = this.tableData
        .slice(1)
        .some((row) => row.tagAttribute || row.tagCategory);

      // 如果其他行都没有填写，则自动填充
      if (!otherRowsFilled) {
        this.tableData.forEach((row, index) => {
          if (index > 0) {
            row.tagAttribute = firstRow.tagAttribute;
            row.tagCategory = firstRow.tagCategory;
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    padding: 0 0 20px 0;
    display: flex;
    justify-content: flex-start;
    gap: 10px;
  }

  .drawer-body {
    flex: 1;
    // padding: 0 20px;
    overflow-y: auto;
  }

  .drawer-tips {
    padding: 10px 20px;
    background-color: #fffbe6;
    margin: 10px 20px;

    .tips-content {
      font-size: 12px;
      color: #666;

      p {
        margin: 5px 0;
      }
    }
  }
}
</style>
