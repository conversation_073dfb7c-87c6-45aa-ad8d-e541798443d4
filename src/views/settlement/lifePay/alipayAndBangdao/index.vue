<!-- 支付宝与邦道账单 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
      @change="handleJump({ tab: tabActiveTab })"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <DetailPage
      v-show="tabActiveTab === 'detail'"
      ref="detail"
      :searchParams="searchParams"
    ></DetailPage>
    <BankPage
      v-show="tabActiveTab === 'bank'"
      ref="bank"
      @jump="handleJump"
    ></BankPage>
    <AlipayTotalPage
      v-show="tabActiveTab === 'alipayTotal'"
      ref="alipayTotal"
      @jump="handleJump"
    ></AlipayTotalPage>
    <BankTotalPage
      v-show="tabActiveTab === 'bankTotal'"
      ref="bankTotal"
      @jump="handleJump"
    ></BankTotalPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import DetailPage from "./detail.vue";
import BankPage from "./bank.vue";
import AlipayTotalPage from "./alipayTotal.vue";
import BankTotalPage from "./bankTotal.vue";
export default {
  name: "alipayAndBangdao",
  components: { DetailPage, BankPage, AlipayTotalPage, BankTotalPage },
  data() {
    return {
      tabActiveTab: "detail",
      topTabDict: [
        {
          value: "detail",
          label: "支付宝明细账单",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:detail:list"]);
          },
        },
        {
          value: "alipayTotal",
          label: "支付宝汇总账单",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:alipayTotal:list"]);
          },
        },
        {
          value: "bank",
          label: "银行流水明细",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:bank:list"]);
          },
        },
        {
          value: "bankTotal",
          label: "银行流水汇总",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:bankTotal:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    // tabActiveTab: {
    //   handler(val) {
    //     this.$nextTick(() => {
    //       this.$refs[val]?.loadData(this.searchParams[val]);
    //     });
    //   },
    // },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
    this.$refs[this.tabActiveTab]?.loadData();
  },
  activated() {
    // if (Object.keys(this.$route.params)?.length > 0) {
    //   this.params = { ...this.params, ...this.$route.params };
    // }
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump({ tab = "detail", params }) {
      console.log("handleJump", tab, params);
      this.searchParams[tab] = params;
      this.$nextTick(() => {
        this.tabActiveTab = tab;
        this.$refs[this.tabActiveTab]?.loadData(
          this.searchParams[this.tabActiveTab]
        );
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
