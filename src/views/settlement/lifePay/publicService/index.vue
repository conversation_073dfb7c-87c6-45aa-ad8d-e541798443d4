<!-- 公服相关数据 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <SettlementInfoPage
      v-show="tabActiveTab === 'settlementInfo'"
      ref="settlementInfo"
      :searchParams="searchParams"
    ></SettlementInfoPage>
    <SettlementPlanPage
      v-show="tabActiveTab === 'settlementPlan'"
      ref="settlementPlan"
      @jump="handleJump"
    ></SettlementPlanPage>
    <InvoiceInfoPage
      v-show="tabActiveTab === 'invoiceInfo'"
      ref="invoiceInfo"
    ></InvoiceInfoPage>
    <InvoiceDetailPage
      v-show="tabActiveTab === 'invoiceDetail'"
      ref="invoiceDetail"
    ></InvoiceDetailPage>
    <OrgAdjustmentPage
      v-show="tabActiveTab === 'orgAdjustment'"
      ref="orgAdjustment"
    ></OrgAdjustmentPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import SettlementInfoPage from "./settlementInfo.vue";
import SettlementPlanPage from "./settlementPlan.vue";
import InvoiceInfoPage from "./invoiceInfo.vue";
import InvoiceDetailPage from "./invoiceDetail.vue";
import OrgAdjustmentPage from "./orgAdjustment.vue";

export default {
  name: "publicService",
  components: {
    SettlementInfoPage,
    SettlementPlanPage,
    InvoiceInfoPage,
    InvoiceDetailPage,
    OrgAdjustmentPage,
  },
  data() {
    return {
      tabActiveTab: "settlementInfo",
      topTabDict: [
        {
          value: "settlementInfo",
          label: "结算信息管理",
          show: () => {
            return this.checkPermission(["publicService:settlementInfo:list"]);
          },
        },
        {
          value: "settlementPlan",
          label: "结算计划管理",
          show: () => {
            return this.checkPermission(["publicService:settlementPlan:list"]);
          },
        },
        {
          value: "invoiceInfo",
          label: "开票信息管理",
          show: () => {
            return this.checkPermission(["publicService:invoiceInfo:list"]);
          },
        },
        {
          value: "invoiceDetail",
          label: "发票信息管理",
          show: () => {
            return this.checkPermission(["publicService:invoiceDetail:list"]);
          },
        },
        {
          value: "orgAdjustment",
          label: "机构调账",
          show: () => {
            return this.checkPermission(["publicService:orgAdjustment:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // 如果需要跳转到其他标签页，可以在这里设置
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
