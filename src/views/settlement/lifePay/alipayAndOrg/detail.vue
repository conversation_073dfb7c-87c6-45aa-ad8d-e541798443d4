<!-- 明细数据 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['alipayAndOrg:detail:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['alipayAndOrg:detail:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入支付宝与机构账单"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/alipayAndOrg/detail.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";

export default {
  name: "ledgerList",
  components: { BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/st/lifePay/aliPayOrg/importExcel",
        url: "/charging-maintenance-ui/static/支付宝与机构账单导入模板.xlsx",
        extraData: {},
      },
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      settleStatusOptions: [],
      billStatusOptions: [],
      pidOptions: [],
      businessOptions: [],
      settleTypeOptions: [],
      accountOptions: [],
      orgOptions: [],
      orgNameOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.getDicts("st_org_bill_status").then((response) => {
      this.billStatusOptions = response.data;
    });
    this.getDicts("st_org_settle_status").then((response) => {
      this.settleStatusOptions = response.data;
    });
    this.getDicts("st_time_point_type").then((response) => {
      this.settleTypeOptions = response.data;
    });
    // 获取下拉列表数据
    this.getDropLists();

    // this.loadData();
  },
  methods: {
    checkPermission,
    getDropLists() {
      // 获取下拉列表数据 - 使用新的API数据源
      api.getDropLists().then((res) => {
        if (res.success) {
          // 处理商户名称下拉数据
          if (res.data.merchantName) {
            this.businessOptions = res.data.merchantName.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理出账机构简称下拉数据
          if (res.data.billingOrgCode) {
            this.orgOptions = res.data.billingOrgCode.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理出账机构名称下拉数据（如果API返回此字段）
          if (res.data.billingOrgName) {
            this.orgNameOptions = res.data.billingOrgName.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理PID下拉数据（如果API返回此字段）
          if (res.data.pid) {
            this.pidOptions = res.data.pid.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理支付宝账号下拉数据（如果API返回此字段）
          if (res.data.alipayAccount) {
            this.accountOptions = res.data.alipayAccount.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // if (res.data.alipayAccount) {
          //   this.billStatusOptions = res.data.billStatus.map((item) => ({
          //     dictLabel: item,
          //     dictValue: item,
          //   }));
          // }
          // if (res.data.settleStatus) {
          //   this.settleStatusOptions = res.data.settleStatus.map((item) => ({
          //     dictLabel: item,
          //     dictValue: item,
          //   }));
          // }
          // if (res.data.pointType) {
          //   this.settleTypeOptions = res.data.pointType.map((item) => ({
          //     dictLabel: item,
          //     dictValue: item,
          //   }));
          // }
        }
      });
    },
    handleResetAll() {
      this.getDropLists();
      this.handleQuery();
    },

    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billRangeTime",
          title: "账单周期",
          startFieldName: "billCycleStart",
          endFieldName: "billCycleEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    handleRangeTypeChange() {
      this.params.workRangeTime = undefined;
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "billNo",
          title: "账单编码",
          width: 180,
        },
        {
          field: "ou",
          title: "OU",
          width: 120,
        },
        {
          field: "mid",
          title: "MID",
          width: 120,
        },
        {
          field: "pid",
          title: "PID",
          width: 120,
        },
        {
          field: "merchantName",
          title: "商户名称",
          width: 120,
        },
        {
          field: "billCycle",
          title: "账单周期",
          width: 120,
        },
        {
          field: "productCode",
          title: "产品码",
          width: 120,
        },
        {
          field: "productName",
          title: "产品名称",
          width: 120,
        },
        {
          field: "billingItem",
          title: "计费项",
          width: 120,
        },
        {
          field: "contractNo",
          title: "合约编号",
          width: 120,
        },
        {
          field: "billingAmount",
          title: "计费量（元）",
          width: 120,
        },
        {
          field: "originAmount",
          title: "原账单金额（元）",
          width: 120,
        },
        {
          field: "unpaidAmount",
          title: "未出账金额（元）",
          width: 120,
        },
        {
          field: "unbilledAmount",
          title: "未开票金额（元）",
          width: 120,
        },
        {
          field: "unsureAmount",
          title: "未确认金额（元）",
          width: 120,
        },
        {
          field: "unsettledAmount",
          title: "未结算金额（元）",
          width: 120,
        },
        {
          field: "billStatus",
          title: "账单状态",
          width: 120,
        },
        {
          field: "alipayAccount",
          title: "支付宝账号",
          width: 120,
        },
        {
          field: "pointType",
          title: "结算时点类型",
          width: 120,
        },
        {
          field: "adjustAmount",
          title: "调账金额（元）",
          width: 120,
        },
        {
          field: "shouldPayAmount",
          title: "应结算（元）",
          width: 120,
        },
        {
          field: "settleStatus",
          title: "结算状态",
          width: 120,
        },
        {
          field: "billingCount",
          title: "计费笔数",
          width: 120,
        },
        {
          field: "billingOrgCode",
          title: "出账机构简称",
          width: 120,
        },
        {
          field: "billingOrgName",
          title: "出账机构名称",
          width: 120,
        },
        {
          field: "contractPid",
          title: "签约PID",
          width: 120,
        },
        {
          field: "contractName",
          title: "签约商户名称",
          width: 120,
        },
        {
          field: "contractEmail",
          title: "签约PID邮箱",
          width: 120,
        },
        {
          field: "createBy",
          title: "创建人",
          width: 120,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "billNo",
            element: "el-input",
            title: "账单编码",
          },
          {
            field: "billStatus",
            title: "账单状态",
            element: "el-select",
            props: {
              options: this.billStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "settleStatus",
            title: "结算状态",
            element: "el-select",
            props: {
              options: this.settleStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "billRangeTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "pid",
            title: "PID",
            element: "el-select",
            props: {
              options: this.pidOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "merchantName",
            title: "商户名称",
            element: "el-select",
            props: {
              options: this.businessOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "pointType",
            title: "结算时点类型",
            element: "el-select",
            props: {
              options: this.settleTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "alipayAccount",
            title: "支付宝账号",
            element: "el-select",
            props: {
              options: this.accountOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "billingOrgCode",
            title: "出账机构简称",
            element: "el-select",
            props: {
              options: this.orgOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "billingOrgName",
            title: "出账机构名称",
            element: "el-select",
            props: {
              options: this.orgNameOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
