<!-- 结算单 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['alipayAndOrg:settle:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['alipayAndOrg:settle:batch']"
          >批量生成收据单</el-button
        >
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/alipayAndOrg/settle.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";

export default {
  name: "ledgerList",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      selectPage: "1",
      uploadObj: {
        api: "/export/report/importStaffInfo",
        url: "/charging-maintenance-ui/static/人员档案批量导入模板.xlsx",
        extraData: {},
      },
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      //buse参数-e

      pidOptions: [],
      businessOptions: [],
      accountOptions: [],
      orgOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.getDropLists();
    // this.loadData();
  },
  methods: {
    checkPermission,
    getDropLists() {
      // 获取下拉列表数据 - 使用新的API数据源
      api.getDropLists().then((res) => {
        if (res.success) {
          // 处理商户名称下拉数据
          if (res.data.merchantName) {
            this.businessOptions = res.data.merchantName.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理出账机构简称下拉数据
          if (res.data.billingOrgCode) {
            this.orgOptions = res.data.billingOrgCode.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理PID下拉数据（如果API返回此字段）
          if (res.data.pid) {
            this.pidOptions = res.data.pid.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          // 处理商户登录账号下拉数据（如果API返回此字段）
          if (res.data.alipayAccount) {
            this.accountOptions = res.data.alipayAccount.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
        }
      });
    },
    handleResetAll() {
      this.getDropLists();
      this.handleQuery();
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    handleBatchAdd() {
      // this.$refs.batchUpload.open();
    },
    rowEdit(row) {
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    handleDownload(row) {},
    handlePreview(row) {},
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billRangeTime",
          title: "账单周期",
          startFieldName: "activityStartDate",
          endFieldName: "activityEndDate",
        },
        {
          field: "createTime",
          title: "生成日期",
          startFieldName: "activityStartDate",
          endFieldName: "activityEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    tableColumn() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          field: "createdDate",
          title: "生成日期",
          width: 180,
        },
        {
          field: "merchantName",
          title: "商户名称",
          width: 120,
        },
        {
          field: "billingOrgCode",
          title: "出账机构简称",
          width: 120,
        },
        {
          field: "merchantLoginAccount",
          title: "商户登录账号",
          width: 120,
        },
        {
          field: "pid",
          title: "PID",
          width: 120,
        },
        {
          field: "startMonth",
          title: "起始年月",
          width: 120,
        },
        {
          field: "endMonth",
          title: "截止年月",
          width: 120,
        },
        {
          field: "invoiceAmount",
          title: "开票金额",
          width: 120,
        },
        {
          field: "settlementRequirements",
          title: "结算单要求",
          width: 120,
        },
        {
          field: "shippingAddress",
          title: "寄送地址",
          width: 120,
        },
        {
          field: "recipient",
          title: "收件人",
          width: 120,
        },
        {
          field: "phone",
          title: "电话",
          width: 120,
        },
        {
          field: "province",
          title: "省份",
          width: 120,
        },
        {
          field: "city",
          title: "市",
          width: 120,
        },
        {
          field: "district",
          title: "区县",
          width: 120,
        },
        {
          field: "detailAddress",
          title: "详细地址",
          width: 120,
        },
        {
          field: "settlementResponsible",
          title: "结算负责人",
          width: 120,
        },
        {
          field: "alipayBd",
          title: "支付宝BD",
          width: 120,
        },
        {
          field: "createBy",
          title: "创建人",
          width: 120,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "merchantName",
            title: "商户名称",
            element: "el-select",
            props: {
              options: this.businessOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "billingOrgCode",
            title: "出账机构简称",
            element: "el-select",
            props: {
              options: this.orgOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "merchantLoginAccount",
            title: "商户登录账号",
            element: "el-select",
            props: {
              options: this.accountOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "billRangeTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "pid",
            title: "PID",
            element: "el-select",
            props: {
              options: this.pidOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "recipient",
            element: "el-input",
            title: "收件人",
          },
          {
            field: "settlementResponsible",
            element: "el-input",
            title: "结算负责人",
          },
          {
            field: "createTime",
            title: "生成日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "alipayBd",
            element: "el-input",
            title: "支付宝BD",
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        //编辑
        update: [
          {
            field: "merchantName",
            title: "商户名称",
          },
          {
            field: "billingOrgCode",
            title: "出账机构简称",
          },
          {
            field: "invoiceAmount",
            title: "开票金额",
            slots: {
              append: "元",
            },
          },
          {
            field: "region",
            title: "寄送地址",
            element: "custom-cascader",
            attrs: {
              options: regionData,
              filterable: true,
              collapseTags: true,
              clearable: true,
              props: {
                checkStrictly: true,
                multiple: false,
              },
            },
          },
          {
            field: "detailAddress",
            title: "详细地址",
          },
          {
            field: "recipient",
            title: "收件人",
          },
          {
            field: "phone",
            title: "电话",
          },
          {
            field: "settlementResponsible",
            title: "结算负责人",
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["alipayAndOrg:settle:edit"]),
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "查看",
            typeName: "preview",
            slotName: "preview",
            showForm: false,
            event: (row) => {
              return this.handlePreview(row);
            },
            condition: (row) => {
              return checkPermission(["alipayAndOrg:settle:preview"]);
            },
          },
          {
            title: "下载",
            typeName: "download",
            slotName: "download",
            showForm: false,
            event: (row) => {
              return this.handleDownload(row);
            },
            condition: (row) => {
              return checkPermission(["alipayAndOrg:settle:download"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
