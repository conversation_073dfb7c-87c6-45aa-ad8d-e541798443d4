<!-- 支付宝与机构账单 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <DetailPage
      v-show="tabActiveTab === 'detail'"
      ref="detail"
      :searchParams="searchParams"
    ></DetailPage>
    <SettlePage
      v-show="tabActiveTab === 'settle'"
      ref="settle"
      @jump="handleJump"
    ></SettlePage>
    <ReceiptPage
      v-show="tabActiveTab === 'receipt'"
      ref="receipt"
    ></ReceiptPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import DetailPage from "./detail.vue";
import SettlePage from "./settle.vue";
import ReceiptPage from "./receipt.vue";

export default {
  name: "alipayAndOrg",
  components: { DetailPage, SettlePage, ReceiptPage },
  data() {
    return {
      tabActiveTab: "detail",
      topTabDict: [
        {
          value: "detail",
          label: "明细数据",
          show: () => {
            return this.checkPermission(["alipayAndOrg:detail:list"]);
          },
        },
        {
          value: "settle",
          label: "结算单",
          show: () => {
            return this.checkPermission(["alipayAndOrg:settle:list"]);
          },
        },
        {
          value: "receipt",
          label: "收据单",
          show: () => {
            return this.checkPermission(["alipayAndOrg:receipt:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    // if (Object.keys(this.$route.params)?.length > 0) {
    //   this.params = { ...this.params, ...this.$route.params };
    // }
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // this.tabActiveTab = "originalData";
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
