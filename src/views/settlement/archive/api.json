{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "维保通", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 56705819, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "default", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 4254550, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "结算材料归档控制层", "id": 57372252, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 4254550, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "列表", "api": {"id": "299600547", "method": "post", "path": "/st/material/list", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"success\": false,\n  \"code\": \"\",\n  \"message\": \"\",\n  \"data\": [\n    {\n      \"id\": 0,\n      \"companyName\": \"\",\n      \"companyCode\": \"\",\n      \"bizTypeName\": \"\",\n      \"bizType\": \"\",\n      \"customerName\": \"\",\n      \"customerCode\": \"\",\n      \"startDate\": \"\",\n      \"endDate\": \"\",\n      \"type\": \"\",\n      \"docName\": \"\",\n      \"createBy\": \"\",\n      \"createTime\": \"\",\n      \"updateBy\": \"\",\n      \"updateTime\": \"\",\n      \"delFlag\": \"\",\n      \"tenantId\": 0,\n      \"materialDetailList\": [\n        {\n          \"id\": 0,\n          \"materialId\": 0,\n          \"type\": \"\",\n          \"fileName\": \"\",\n          \"filePath\": \"\",\n          \"delFlag\": \"\",\n          \"createTime\": \"\",\n          \"createBy\": \"\",\n          \"updateTime\": \"\",\n          \"updateBy\": \"\"\n        }\n      ],\n      \"settlementCycle\": \"\"\n    }\n  ],\n  \"traceId\": \"\",\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"total\": 0\n}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/170396394", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 4254550, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "导出", "api": {"id": "299600548", "method": "post", "path": "/st/material/exportExcel", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "681908178", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/169747732"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"success\": false,\n  \"code\": \"\",\n  \"message\": \"\",\n  \"data\": \"\",\n  \"traceId\": \"\"\n}", "responseId": 681908178, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/170396394", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 4254550, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增", "api": {"id": "299600549", "method": "post", "path": "/st/material/add", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "681908179", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/169747732"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"success\": false,\n  \"code\": \"\",\n  \"message\": \"\",\n  \"data\": \"\",\n  \"traceId\": \"\"\n}", "responseId": 681908179, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 4254550, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "编辑", "api": {"id": "299600550", "method": "post", "path": "/st/material/edit", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "681908180", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/169747732"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"success\": false,\n  \"code\": \"\",\n  \"message\": \"\",\n  \"data\": \"\",\n  \"traceId\": \"\"\n}", "responseId": 681908180, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 4254550, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除", "api": {"id": "299600551", "method": "post", "path": "/st/material/delete", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "681908181", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/169747732"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"success\": false,\n  \"code\": \"\",\n  \"message\": \"\",\n  \"data\": \"\",\n  \"traceId\": \"\"\n}", "responseId": 681908181, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 4254550, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [], "schemaCollection": [{"id": ********, "name": "根目录", "visibility": "SHARED", "moduleId": 4254550, "items": [{"name": "BankFlowVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "industry": {"type": "string", "description": "行业"}, "isNucleated": {"type": "string", "description": "是否核销"}, "nucleationSubmitTime": {"type": "string", "description": "提交核销时间", "x-apifox-mock": "@datetime"}, "reasonClassification": {"type": "string", "description": "原因分类"}, "subReasonClassification": {"type": "string", "description": "子原因分类"}, "cleanupClassification": {"type": "string", "description": "清理分类"}, "remark": {"type": "string", "description": "备注"}, "matchedBillMonth": {"type": "string", "description": "匹配的账单月份"}, "unNucleatedAmount": {"type": "number", "description": "未核销金额"}, "ourAccountNumber": {"type": "string", "description": "本方账号"}, "bankTransactionDate": {"type": "string", "description": "银行交易日期(sec)", "x-apifox-mock": "@datetime"}, "currency": {"type": "string", "description": "币种"}, "debitAmount": {"type": "number", "description": "借方金额(单位:元)"}, "creditAmount": {"type": "number", "description": "贷方金额(单位:元)"}, "treasuryFlowType": {"type": "string", "description": "财资流水类型"}, "bankRemark": {"type": "string", "description": "银行备注"}, "counterpartyBankName": {"type": "string", "description": "对方银行户名"}, "openingBank": {"type": "string", "description": "开户行"}, "counterpartyAccountNumber": {"type": "string", "description": "对方账号"}, "supplementaryNotes": {"type": "string", "description": "补充说明"}, "flowStatus": {"type": "string", "description": "流水状态"}, "flowDestination": {"type": "string", "description": "流水去向"}, "flowDestinationAccount": {"type": "string", "description": "流水去向账号"}, "bankFlowNumber": {"type": "string", "description": "银行流水号"}, "createBy": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "industry", "isNucleated", "nucleationSubmitTime", "reasonClassification", "subReasonClassification", "cleanupClassification", "remark", "matchedBillMonth", "unNucleatedAmount", "ourAccountNumber", "bankTransactionDate", "currency", "debitAmount", "creditAmount", "treasuryFlowType", "bankRemark", "counterpartyBankName", "openingBank", "counterpartyAccountNumber", "supplementaryNotes", "flowStatus", "flowDestination", "flowDestinationAccount", "bankFlowNumber", "createBy", "createTime", "tenantId", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "StMaterialDetail", "displayName": "", "id": "#/definitions/*********", "description": "结算材料详情表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "materialId": {"type": "integer", "description": "结算材料id", "format": "int64"}, "type": {"type": "string", "description": "类型 字典:st_material_type"}, "fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件地址"}, "delFlag": {"type": "string", "description": "删除标志（0正常 1删除）"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}}, "x-apifox-orders": ["id", "materialId", "type", "fileName", "filePath", "delFlag", "createTime", "createBy", "updateTime", "updateBy"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeProfitVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeProfitVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "costType": {"type": "string", "description": "费用类型"}, "sharedIncome": {"type": "number", "description": "分摊收入(元)"}, "returnAmount": {"type": "number", "description": "回票金额(元)"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "billReview": {"type": "string", "description": "账单复核"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}, "mode": {"type": "string", "description": "模式"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "costType", "sharedIncome", "returnAmount", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counterpartySeal", "ourSeal", "returnComplete", "remarks", "mode", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeMaintenanceVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeMaintenanceVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "operator": {"type": "string", "description": "运营商名称"}, "mode": {"type": "string", "description": "模式"}, "responsiblePerson": {"type": "string", "description": "负责人"}, "remarks": {"type": "string", "description": "备注信息"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "operator", "mode", "<PERSON><PERSON><PERSON>", "remarks", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultBankFlowVO", "displayName": "", "id": "#/definitions/*********", "description": "[BankFlowVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "SettlementMaterialVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.material.vo.SettlementMaterialVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "companyName": {"type": "string", "description": "所属公司名称"}, "companyCode": {"type": "string", "description": "所属公司编码 字典:st_material_company"}, "bizTypeName": {"type": "string", "description": "所属业务名称"}, "bizType": {"type": "string", "description": "所属业务 字典:st_material_biz"}, "customerName": {"type": "string", "description": "客户名称"}, "customerCode": {"type": "string", "description": "客户编码 字典:st_material_customer"}, "startDate": {"type": "string", "description": "结算周期开始", "x-apifox-mock": "@datetime"}, "endDate": {"type": "string", "description": "结算周期结束", "x-apifox-mock": "@datetime"}, "type": {"type": "string", "description": "类型 字典:st_material_type"}, "docName": {"type": "string", "description": "文件名称"}, "createBy": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "delFlag": {"type": "string", "description": "删除标志（0正常 1删除）"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "materialDetailList": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "结算材料详情表"}, "description": "材料明细"}, "settlementCycle": {"type": "string", "description": "结算周期"}}, "x-apifox-orders": ["id", "companyName", "companyCode", "bizTypeName", "bizType", "customerName", "customerCode", "startDate", "endDate", "type", "doc<PERSON>ame", "createBy", "createTime", "updateBy", "updateTime", "delFlag", "tenantId", "materialDetailList", "settlementCycle"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeProfitVO", "displayName": "", "id": "#/definitions/*********", "description": "[NewChargeProfitVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeProfitVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeMaintenanceVO", "displayName": "", "id": "#/definitions/170897287", "description": "[NewChargeMaintenanceVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeMaintenanceVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "BankFlowQueryDTO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "remark": {"type": "string", "description": "备注pid"}, "counterpartyBankName": {"type": "string", "description": "银行户名"}, "matchedBillMonthStart": {"type": "string", "description": "账单月份开始"}, "matchedBillMonthEnd": {"type": "string", "description": "账单月份结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "remark", "counterpartyBankName", "matchedBillMonthStart", "matchedBillMonthEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultSettlementMaterialVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.material.vo.SettlementMaterialVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeProfitQueryDTO", "displayName": "", "id": "#/definitions/170897250", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeProfitQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "operator": {"type": "string", "description": "运营商"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "billReview": {"type": "string", "description": "账单复核"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "billYearMonthStart": {"type": "string", "description": "账单年月开始"}, "billYearMonthEnd": {"type": "string", "description": "账单年月结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "operator", "<PERSON><PERSON><PERSON>", "ourSeal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counterpartySeal", "returnComplete", "billYearMonthStart", "billYearMonthEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeMaintenanceQueryDTO", "displayName": "", "id": "#/definitions/170897288", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeMaintenanceQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "operator": {"type": "string", "description": "运营商"}, "mode": {"type": "string", "description": "模式"}, "responsiblePerson": {"type": "string", "description": "负责人"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "operator", "mode", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "RestResultString", "displayName": "", "id": "#/definitions/169747732", "description": "[RestResult]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "string", "description": ""}, "traceId": {"type": "string", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "SettlementMaterialQueryDTO", "displayName": "", "id": "#/definitions/170396394", "description": "com.whale.cloud.model.st.material.dto.SettlementMaterialQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "companyCode": {"type": "string", "description": "所属公司名称编码 字典:st_material_company"}, "bizType": {"type": "string", "description": "所属业务编码 字典:st_material_biz"}, "customerCode": {"type": "string", "description": "客户名称编码 字典:st_material_customer"}, "startDate": {"type": "string", "description": "结算周期开始 yyyy-MM-dd"}, "endDate": {"type": "string", "description": "结算周期结束 yyyy-MM-dd"}, "type": {"type": "string", "description": "类型编码 字典:st_material_type"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "companyCode", "bizType", "customerCode", "startDate", "endDate", "type"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "MapListString", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"key": {"type": "array", "items": {"type": "string"}}}, "x-apifox-orders": ["key"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayBdBillCheckBankVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.lifePay.vo.AlipayBdBillCheckBankVO", "schema": {"jsonSchema": {"type": "object", "properties": {"sameNum": {"type": "integer", "description": ""}, "diffNum": {"type": "integer", "description": ""}}, "x-apifox-orders": ["sameNum", "diffNum"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeProfitImportDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeProfitImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "costType": {"type": "string", "description": "费用类型"}, "sharedIncome": {"type": "number", "description": "分摊收入(元)"}, "returnAmount": {"type": "number", "description": "回票金额(元)"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "billReview": {"type": "string", "description": "账单复核"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}, "mode": {"type": "string", "description": "模式"}}, "x-apifox-orders": ["bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "costType", "sharedIncome", "returnAmount", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counterpartySeal", "ourSeal", "returnComplete", "remarks", "mode"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeMaintenanceImportDTO", "displayName": "", "id": "#/definitions/170897289", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeMaintenanceImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"operator": {"type": "string", "description": "运营商名称"}, "mode": {"type": "string", "description": "模式"}, "responsiblePerson": {"type": "string", "description": "负责人"}, "remarks": {"type": "string", "description": "备注信息"}}, "x-apifox-orders": ["operator", "mode", "<PERSON><PERSON><PERSON>", "remarks"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "RestResultMapListString", "displayName": "", "id": "#/definitions/169747734", "description": "[RestResult]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"$ref": "#/definitions/*********", "description": ""}, "traceId": {"type": "string", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "RestResultAlipayBdBillCheckBankVO", "displayName": "", "id": "#/definitions/*********", "description": "[RestResult]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"$ref": "#/definitions/*********", "description": ""}, "traceId": {"type": "string", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "SettlementMaterialEditDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.material.dto.SettlementMaterialEditDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "companyName": {"type": "string", "description": "所属公司名称"}, "companyCode": {"type": "string", "description": "所属公司编码 字典:st_material_company"}, "bizTypeName": {"type": "string", "description": "所属业务名称"}, "bizType": {"type": "string", "description": "所属业务 字典:st_material_biz"}, "customerName": {"type": "string", "description": "客户名称"}, "customerCode": {"type": "string", "description": "客户编码 字典:st_material_customer"}, "startDate": {"type": "string", "description": "结算周期开始", "x-apifox-mock": "@datetime"}, "endDate": {"type": "string", "description": "结算周期结束", "x-apifox-mock": "@datetime"}, "type": {"type": "string", "description": "类型 字典:st_material_type"}, "docName": {"type": "string", "description": "文件名称"}, "createBy": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "delFlag": {"type": "string", "description": "删除标志（0正常 1删除）"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "materialDetailList": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "结算材料详情表"}, "description": "材料明细"}}, "x-apifox-orders": ["id", "companyName", "companyCode", "bizTypeName", "bizType", "customerName", "customerCode", "startDate", "endDate", "type", "doc<PERSON>ame", "createBy", "createTime", "updateBy", "updateTime", "delFlag", "tenantId", "materialDetailList"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeProfitOperationDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeProfitOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "costType": {"type": "string", "description": "费用类型"}, "sharedIncome": {"type": "number", "description": "分摊收入(元)"}, "returnAmount": {"type": "number", "description": "回票金额(元)"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "billReview": {"type": "string", "description": "账单复核"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}, "mode": {"type": "string", "description": "模式"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}, "idList": {"type": "array", "items": {"type": "integer"}, "description": "id集合"}}, "x-apifox-orders": ["id", "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "costType", "sharedIncome", "returnAmount", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counterpartySeal", "ourSeal", "returnComplete", "remarks", "mode", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource", "idList"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeMaintenanceOperationDTO", "displayName": "", "id": "#/definitions/170897290", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeMaintenanceOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"idList": {"type": "array", "items": {"type": "integer"}, "description": "id列表"}, "responsiblePerson": {"type": "string", "description": "负责人"}}, "x-apifox-orders": ["idList", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayBdBillVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "policyNo": {"type": "string", "description": "政策号"}, "policyName": {"type": "string", "description": "政策名称"}, "bizCycle": {"type": "string", "description": "业务周期"}, "incentiveObjectPid": {"type": "string", "description": "激励对象pid"}, "incentiveObjectName": {"type": "string", "description": "激励对象名称"}, "contractAccountPid": {"type": "string", "description": "签约商户pid"}, "contractAccountName": {"type": "string", "description": "签约账号名称"}, "contractMerchantMid": {"type": "string", "description": "签约商户mid"}, "contractMerchantName": {"type": "string", "description": "签约商户名称"}, "settlementAccountPid": {"type": "string", "description": "结算商户pid"}, "settlementAccountName": {"type": "string", "description": "结算账号名称"}, "settlementMerchantMid": {"type": "string", "description": "结算商户mid"}, "settlementMerchantName": {"type": "string", "description": "结算商户名称"}, "payMerchantId": {"type": "string", "description": "付款商户"}, "payMerchantName": {"type": "string", "description": "付款商户名称"}, "productCode": {"type": "string", "description": "产品码"}, "billMonth": {"type": "string", "description": "账单月份"}, "providerPid": {"type": "string", "description": "服务商pid"}, "writeOffMonth": {"type": "string", "description": "核销月份"}, "writeOffAmount": {"type": "number", "description": "核销金额"}, "settlementAmount": {"type": "number", "description": "结算金额"}, "settlement": {"type": "string", "description": "是否结算"}, "settlementRemark": {"type": "string", "description": "不结算原因"}, "bankRemark": {"type": "string", "description": "银行流水备注"}, "bankMonth": {"type": "string", "description": "银行匹配账单月份"}, "bankAmount": {"type": "number", "description": "银行贷方金额"}, "diffResult": {"type": "string", "description": "比对结果 01:一致 02::不一致 03:未匹配到数据"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "policyNo", "policyName", "bizCycle", "incentiveObjectPid", "incentiveObjectName", "contractAccountPid", "contractAccountName", "contractMerchantMid", "contractMerchantName", "settlementAccountPid", "settlementAccountName", "settlementMerchantMid", "settlementMerchantName", "payMerchantId", "payMerchantName", "productCode", "<PERSON><PERSON><PERSON><PERSON>", "providerPid", "writeOffMonth", "writeOffAmount", "settlementAmount", "settlement", "settlementRemark", "bankRemark", "bankMonth", "bankAmount", "diffResult", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultAlipayBdBillVO", "displayName": "", "id": "#/definitions/*********", "description": "[AlipayBdBillVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayBdBillQueryDTO", "displayName": "", "id": "#/definitions/169747737", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "idList": {"type": "array", "items": {"type": "integer"}, "description": "主键id"}, "settlementAccountPid": {"type": "string", "description": "结算商户pid"}, "diffResult": {"type": "string", "description": "比对结果 00:不一致 01:一致"}, "settlementAccountName": {"type": "string", "description": "结算账号名称"}, "billMonthStart": {"type": "string", "description": "账单月份"}, "billMonthEnd": {"type": "string", "description": ""}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "idList", "settlementAccountPid", "diffResult", "settlementAccountName", "billMonthStart", "billMonthEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargePowerVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargePowerVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "returnAmount": {"type": "number", "description": "回票金额(元)"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "billCheck": {"type": "string", "description": "账单核对"}, "counterpartyConfirmation": {"type": "string", "description": "对方确认"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}, "mode": {"type": "string", "description": "模式"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "returnAmount", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "counterpartyConfirmation", "counterpartySeal", "ourSeal", "returnComplete", "remarks", "mode", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeOnlineVO", "displayName": "", "id": "#/definitions/171177315", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeOnlineVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "operatorName": {"type": "string", "description": "运营商名称"}, "billDate": {"type": "string", "description": "账单日", "x-apifox-mock": "@datetime"}, "billMonth": {"type": "string", "description": "账单月"}, "billMonthStart": {"type": "string", "description": "账单月开始"}, "billMonthEnd": {"type": "string", "description": "账单月结束"}, "settlementMethod": {"type": "string", "description": "结算方式"}, "chargingDegree": {"type": "number", "description": "充电量度数"}, "chargingAmount": {"type": "number", "description": "充电量金额"}, "chargingSettlementRatio": {"type": "string", "description": "充电结算比例"}, "dischargingDegree": {"type": "number", "description": "放电量度数"}, "dischargingAmount": {"type": "number", "description": "放电量金额"}, "dischargingStationRatio": {"type": "string", "description": "放电结算比例"}, "adjustedChargingFee": {"type": "number", "description": "调整充电费"}, "adjustedDischargingFee": {"type": "number", "description": "调整放电费"}, "difference": {"type": "number", "description": "税差"}, "platformPayable": {"type": "number", "description": "平台应付"}, "platformReceivable": {"type": "number", "description": "平台应收"}, "platformPendingPay": {"type": "number", "description": "平台待付"}, "platformPendingReceive": {"type": "number", "description": "平台待收"}, "paymentStatus": {"type": "string", "description": "付款状态"}, "platformActualReceive": {"type": "number", "description": "轧差后平台实收"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "operatorName", "billDate", "<PERSON><PERSON><PERSON><PERSON>", "billMonthStart", "billMonthEnd", "settlementMethod", "chargingDegree", "chargingAmount", "chargingSettlementRatio", "dischargingDegree", "dischargingAmount", "dischargingStationRatio", "adjustedChargingFee", "adjustedDischargingFee", "difference", "platformPayable", "platformReceivable", "platformPendingPay", "platformPendingReceive", "paymentStatus", "platformActualReceive", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgBillVO", "displayName": "", "id": "#/definitions/169747738", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billNo": {"type": "string", "description": "账单编码"}, "ou": {"type": "string", "description": "OU"}, "mid": {"type": "string", "description": "MID"}, "pid": {"type": "string", "description": "PID"}, "merchantName": {"type": "string", "description": "商户名称"}, "billCycle": {"type": "string", "description": "账单周期"}, "productCode": {"type": "string", "description": "产品码"}, "productName": {"type": "string", "description": "产品名称"}, "billingItem": {"type": "string", "description": "计费项"}, "contractNo": {"type": "string", "description": "合约编号"}, "billingAmount": {"type": "number", "description": "计费量"}, "originAmount": {"type": "number", "description": "原账单金额（元）"}, "unpaidAmount": {"type": "number", "description": "未出账金额（元）"}, "unbilledAmount": {"type": "number", "description": "未开票金额（元）"}, "unsureAmount": {"type": "number", "description": "未确认金额（元）"}, "unsettledAmount": {"type": "number", "description": "未结算金额（元）"}, "billStatus": {"type": "string", "description": "账单状态"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "pointType": {"type": "string", "description": "结算时点类型"}, "adjustAmount": {"type": "number", "description": "调账金额（元）"}, "shouldPayAmount": {"type": "number", "description": "应结算（元）"}, "settleStatus": {"type": "string", "description": "结算状态"}, "billingCount": {"type": "integer", "description": "计费笔数"}, "billingOrgCode": {"type": "string", "description": "出账机构简称"}, "billingOrgName": {"type": "string", "description": "出账机构名称"}, "contractPid": {"type": "string", "description": "签约PID"}, "contractName": {"type": "string", "description": "签约商户名称"}, "contractEmail": {"type": "string", "description": "签约PID邮箱"}, "createBy": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "billNo", "ou", "mid", "pid", "merchantName", "billCycle", "productCode", "productName", "billingItem", "contractNo", "billingAmount", "originAmount", "unpaidAmount", "unbilledAmount", "unsure<PERSON><PERSON>", "unsettledAmount", "billStatus", "alip<PERSON><PERSON><PERSON>unt", "pointType", "adjustAmount", "shouldPayAmount", "settleStatus", "billingCount", "billingOrgCode", "billingOrgName", "contractPid", "contractName", "contractEmail", "createBy", "createTime", "tenantId", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargePowerVO", "displayName": "", "id": "#/definitions/170897292", "description": "[NewChargePowerVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargePowerVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeOnlineVO", "displayName": "", "id": "#/definitions/171177316", "description": "[NewChargeOnlineVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/171177315", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeOnlineVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultAlipayOrgBillVO", "displayName": "", "id": "#/definitions/169747739", "description": "[AlipayOrgBillVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747738", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargePowerQueryDTO", "displayName": "", "id": "#/definitions/170897293", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargePowerQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "operator": {"type": "string", "description": "运营商"}, "counterpartyConfirmation": {"type": "string", "description": "对方确认状态"}, "ourSeal": {"type": "string", "description": "我方盖章状态"}, "billYearMonthStart": {"type": "string", "description": "账单年月开始"}, "billYearMonthEnd": {"type": "string", "description": "账单年月结束"}, "billCheck": {"type": "string", "description": "账单核对状态"}, "returnComplete": {"type": "string", "description": "回票完成状态"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "operator", "counterpartyConfirmation", "ourSeal", "billYearMonthStart", "billYearMonthEnd", "<PERSON><PERSON><PERSON><PERSON>", "returnComplete"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeOnlineQueryDTO", "displayName": "", "id": "#/definitions/171177317", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeOnlineQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": "运营商"}, "settlementMethod": {"type": "string", "description": "结算方式"}, "paymentStatus": {"type": "string", "description": "付款状态"}, "billMonthStart": {"type": "string", "description": "账单月开始"}, "billMonthEnd": {"type": "string", "description": "账单月结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementMethod", "paymentStatus", "billMonthStart", "billMonthEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgBillQueryDTO", "displayName": "", "id": "#/definitions/169747740", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "billNo": {"type": "string", "description": "账单编码"}, "billStatus": {"type": "string", "description": "账单状态"}, "settleStatus": {"type": "string", "description": "结算状态"}, "billCycleStart": {"type": "string", "description": "账单周期开始"}, "billCycleEnd": {"type": "string", "description": "账单周期结束"}, "pid": {"type": "string", "description": "PID"}, "merchantName": {"type": "string", "description": "商户名称"}, "pointType": {"type": "string", "description": "结算时点类型"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "billingOrgCode": {"type": "string", "description": "出账机构简称"}, "billingOrgName": {"type": "string", "description": "出账机构名称"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "billNo", "billStatus", "settleStatus", "billCycleStart", "billCycleEnd", "pid", "merchantName", "pointType", "alip<PERSON><PERSON><PERSON>unt", "billingOrgCode", "billingOrgName"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargePowerImportDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargePowerImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "returnAmount": {"type": "number", "description": "回票金额(元)"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "billCheck": {"type": "string", "description": "账单核对"}, "counterpartyConfirmation": {"type": "string", "description": "对方确认"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}, "mode": {"type": "string", "description": "模式"}}, "x-apifox-orders": ["bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "returnAmount", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "counterpartyConfirmation", "counterpartySeal", "ourSeal", "returnComplete", "remarks", "mode"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "BdProviderBillVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "partnerName": {"type": "string", "description": "合作伙伴名"}, "instituteName": {"type": "string", "description": "机构名称"}, "mid": {"type": "string", "description": "MID-Merchant_no"}, "accountDay": {"type": "string", "description": "账户日期account_day"}, "settlementDay": {"type": "string", "description": "结算期(month)"}, "onlineDay": {"type": "string", "description": "上线日期online_date(day)"}, "aliShouldPayAmount": {"type": "number", "description": "支付宝应收"}, "aliActualPayAmount": {"type": "number", "description": "支付宝实收"}, "spShouldPayAmount": {"type": "number", "description": "服务商应收"}, "spActualPayAmount": {"type": "number", "description": "服务商实收"}, "rebate": {"type": "number", "description": "返佣比例"}, "status": {"type": "string", "description": "结算状态"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "partner<PERSON>ame", "instituteName", "mid", "accountDay", "settlementDay", "onlineDay", "aliShouldPayAmount", "aliActualPayAmount", "spShouldPayAmount", "spActualPayAmount", "rebate", "status", "updateBy", "updateTime", "tenantId", "createTime", "createBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargePowerOperationDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargePowerOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"idList": {"type": "array", "items": {"type": "integer"}, "description": "id列表"}, "billCheck": {"type": "string", "description": "账单核对"}, "counterpartyConfirmation": {"type": "string", "description": "对方确认"}, "counterpartySeal": {"type": "string", "description": "对方盖章"}, "ourSeal": {"type": "string", "description": "我方盖章"}, "returnComplete": {"type": "string", "description": "回票完成"}, "remarks": {"type": "string", "description": "备注"}}, "x-apifox-orders": ["idList", "<PERSON><PERSON><PERSON><PERSON>", "counterpartyConfirmation", "counterpartySeal", "ourSeal", "returnComplete", "remarks"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultBdProviderBillVO", "displayName": "", "id": "#/definitions/169747742", "description": "[BdProviderBillVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "BdProviderBillQueryDTO", "displayName": "", "id": "#/definitions/169747743", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "idList": {"type": "array", "items": {"type": "integer"}, "description": "id列表"}, "partnerName": {"type": "string", "description": "合作伙伴名"}, "status": {"type": "string", "description": "结算状态"}, "settlementDayStart": {"type": "string", "description": "结算周期(month)开始"}, "settlementDayEnd": {"type": "string", "description": "结算周期(month)结束"}, "instituteName": {"type": "string", "description": "机构名称"}, "mid": {"type": "string", "description": "MID-Merchant_no"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "idList", "partner<PERSON>ame", "status", "settlementDayStart", "settlementDayEnd", "instituteName", "mid"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "InstitutionInfoVO", "displayName": "", "id": "#/definitions/169747744", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "institutionName": {"type": "string", "description": "机构名称"}, "institutionCode": {"type": "string", "description": "机构编码"}, "billingInstitutionCode": {"type": "string", "description": "出账机构编码"}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "businessType": {"type": "string", "description": "业务类型"}, "collectionInstitution": {"type": "string", "description": "销账机构"}, "primaryServiceProvider": {"type": "string", "description": "一级服务商"}, "secondaryServiceProvider": {"type": "string", "description": "二级服务商"}, "onlineStatus": {"type": "string", "description": "上线状态"}, "auditStatus": {"type": "string", "description": "审核状态"}, "goLiveTime": {"type": "string", "description": "上线时间", "x-apifox-mock": "@datetime"}, "settlementRequirements": {"type": "string", "description": "结算单要求"}, "collectionRequirements": {"type": "string", "description": "收据要求"}, "handlingFee": {"type": "string", "description": "手续函"}, "handlingFeeUrl": {"type": "string", "description": "手续函url"}, "collectionReceipt": {"type": "string", "description": "收据函"}, "collectionReceiptUrl": {"type": "string", "description": "收据函url"}, "alipayBd": {"type": "string", "description": "支付宝BD"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "institutionName", "institutionCode", "billingInstitutionCode", "settlementInstitutionName", "settlementPid", "businessType", "collectionInstitution", "primaryServiceProvider", "secondaryServiceProvider", "onlineStatus", "auditStatus", "goLiveTime", "settlementRequirements", "collectionRequirements", "handlingFee", "handlingFeeUrl", "collectionReceipt", "collectionReceiptUrl", "alipayBd", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultInstitutionInfoVO", "displayName": "", "id": "#/definitions/169747745", "description": "[InstitutionInfoVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747744", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "InstitutionInfoQueryDTO", "displayName": "", "id": "#/definitions/169747746", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "institutionName": {"type": "string", "description": "机构名称"}, "businessType": {"type": "string", "description": "业务类型"}, "onlineStatus": {"type": "string", "description": "上线状态"}, "serviceProvider": {"type": "string", "description": "服务商"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "institutionName", "businessType", "onlineStatus", "serviceProvider"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "InstitutionInfoEditDTO", "displayName": "", "id": "#/definitions/169747747", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "settlementRequirements": {"type": "string", "description": "结算单要求"}, "collectionRequirements": {"type": "string", "description": "收据要求"}, "handlingFee": {"type": "string", "description": "手续函名称"}, "handlingFeeUrl": {"type": "string", "description": "手续函url"}, "collectionReceipt": {"type": "string", "description": "收据函名称"}, "collectionReceiptUrl": {"type": "string", "description": "收据函url"}}, "x-apifox-orders": ["id", "settlementRequirements", "collectionRequirements", "handlingFee", "handlingFeeUrl", "collectionReceipt", "collectionReceiptUrl"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "ServiceProviderVO", "displayName": "", "id": "#/definitions/169747748", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "serviceProvider": {"type": "string", "description": "服务商"}, "agreementType": {"type": "string", "description": "协议类型"}, "billingInstitution": {"type": "string", "description": "出账机构"}, "mid": {"type": "string", "description": "mid"}, "pid": {"type": "string", "description": "pid"}, "goLiveTime": {"type": "string", "description": "上线时间", "x-apifox-mock": "@datetime"}, "commissionStartTime": {"type": "string", "description": "返佣起始时间", "x-apifox-mock": "@datetime"}, "commissionEndTime": {"type": "string", "description": "返佣截止时间", "x-apifox-mock": "@datetime"}, "lastCommissionRatio": {"type": "number", "description": "上年协议返佣比例"}, "rate": {"type": "number", "description": "费率"}, "isDiscounted": {"type": "string", "description": "是否打折"}, "currCommissionRatio": {"type": "number", "description": "今年协议返佣比例"}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "状态"}, "archiveTime": {"type": "string", "description": "归档时间", "x-apifox-mock": "@datetime"}, "originProvider": {"type": "string", "description": "原服务商"}, "billingOrg": {"type": "string", "description": "出账机构"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "serviceProvider", "agreementType", "billingInstitution", "mid", "pid", "goLiveTime", "commissionStartTime", "commissionEndTime", "lastCommissionRatio", "rate", "isDiscounted", "currCommissionRatio", "remark", "status", "archiveTime", "originProvider", "billingOrg", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultServiceProviderVO", "displayName": "", "id": "#/definitions/169747749", "description": "[ServiceProviderVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747748", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "ServiceProviderQueryDTO", "displayName": "", "id": "#/definitions/169747750", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "serviceProvider": {"type": "string", "description": "服务商"}, "mid": {"type": "string", "description": "mid"}, "pid": {"type": "string", "description": "pid"}, "commissionStartTime": {"type": "string", "description": "返佣起始时间"}, "commissionEndTime": {"type": "string", "description": "返佣截止时间"}, "billingOrg": {"type": "string", "description": "出账机构"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "serviceProvider", "mid", "pid", "commissionStartTime", "commissionEndTime", "billingOrg"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationVO", "displayName": "", "id": "#/definitions/171177318", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeStationVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商，字典：st_new_charge_station"}, "siteName": {"type": "string", "description": "站点名称"}, "investmentMode": {"type": "string", "description": "投资模式，字典：st_new_charge_station_invest"}, "settlementMethod": {"type": "string", "description": "结算方式，字典：st_new_charge_station_pay_method"}, "discount": {"type": "string", "description": "折扣"}, "chargingAmount": {"type": "number", "description": "充电量"}, "actualChargingFee": {"type": "number", "description": "实际充电费"}, "dischargingAmount": {"type": "number", "description": "放电量"}, "actualDischargingFee": {"type": "number", "description": "实际放电费"}, "powerLossRate": {"type": "string", "description": "电损率"}, "income": {"type": "number", "description": "收益(元)"}, "payableAmount": {"type": "number", "description": "应付金额(元)"}, "receivableAmount": {"type": "number", "description": "应回金额(元)"}, "billingDate": {"type": "string", "description": "出账日期"}, "paymentDate": {"type": "string", "description": "回款日期"}, "settlementStatus": {"type": "string", "description": "结算进度，字典：st_new_charge_station_pay_status"}, "remarks": {"type": "string", "description": "备注"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "siteName", "investmentMode", "settlementMethod", "discount", "chargingAmount", "actualChargingFee", "dischargingAmount", "actualDischargingFee", "powerLossRate", "income", "payableAmount", "receivableAmount", "billingDate", "paymentDate", "settlementStatus", "remarks", "<PERSON><PERSON><PERSON>", "updateBy", "updateTime", "tenantId", "createTime", "createBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "ServiceProviderEditDTO", "displayName": "", "id": "#/definitions/169747751", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "serviceProvider": {"type": "string", "description": "服务商"}, "agreementType": {"type": "string", "description": "协议类型"}, "billingInstitution": {"type": "string", "description": "出账机构"}, "mid": {"type": "string", "description": "mid"}, "pid": {"type": "string", "description": "pid"}, "goLiveTime": {"type": "string", "description": "上线时间", "x-apifox-mock": "@datetime"}, "commissionStartTime": {"type": "string", "description": "返佣起始时间", "x-apifox-mock": "@datetime"}, "commissionEndTime": {"type": "string", "description": "返佣截止时间", "x-apifox-mock": "@datetime"}, "lastCommissionRatio": {"type": "number", "description": "上年协议返佣比例"}, "rate": {"type": "number", "description": "费率"}, "isDiscounted": {"type": "string", "description": "是否打折"}, "currCommissionRatio": {"type": "number", "description": "今年协议返佣比例"}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "状态"}, "archiveTime": {"type": "string", "description": "归档时间", "x-apifox-mock": "@datetime"}, "originProvider": {"type": "string", "description": "原服务商"}, "billingOrg": {"type": "string", "description": "出账机构"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "serviceProvider", "agreementType", "billingInstitution", "mid", "pid", "goLiveTime", "commissionStartTime", "commissionEndTime", "lastCommissionRatio", "rate", "isDiscounted", "currCommissionRatio", "remark", "status", "archiveTime", "originProvider", "billingOrg", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeStationVO", "displayName": "", "id": "#/definitions/171177319", "description": "[NewChargeStationVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/171177318", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeStationVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgReceiptVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "merchantName": {"type": "string", "description": "商户名称"}, "merchantLoginAccount": {"type": "string", "description": "商户登录账号"}, "pid": {"type": "string", "description": "PID"}, "startMonth": {"type": "string", "description": "起始年月"}, "endMonth": {"type": "string", "description": "截止年月"}, "generatedDate": {"type": "string", "description": "生成日期", "x-apifox-mock": "@datetime"}, "invoiceAmount": {"type": "number", "description": "开票金额"}, "collectionRequirements": {"type": "string", "description": "收据要求"}, "shippingAddress": {"type": "string", "description": "寄送地址"}, "recipient": {"type": "string", "description": "收件人"}, "phone": {"type": "string", "description": "电话"}, "remark": {"type": "string", "description": "备注"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "merchantName", "merchantLoginAccount", "pid", "startMonth", "endMonth", "generatedDate", "invoiceAmount", "collectionRequirements", "shippingAddress", "recipient", "phone", "remark", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationQueryDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "billYearMonthStart": {"type": "string", "description": "账单年月开始"}, "billYearMonthEnd": {"type": "string", "description": "账单年月结束"}, "operator": {"type": "string", "description": "运营商"}, "siteName": {"type": "string", "description": "站点名称"}, "settlementMethod": {"type": "string", "description": "结算方式"}, "settlementStatus": {"type": "string", "description": "结算进度"}, "investmentMode": {"type": "string", "description": "投资模式"}, "reconciliationPerson": {"type": "string", "description": "对账人"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "billYearMonthStart", "billYearMonthEnd", "operator", "siteName", "settlementMethod", "settlementStatus", "investmentMode", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultAlipayOrgReceiptVO", "displayName": "", "id": "#/definitions/169747753", "description": "[AlipayOrgReceiptVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationImportDTO", "displayName": "", "id": "#/definitions/171177321", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"billYearMonth": {"type": "string", "description": "账单年月"}, "operator": {"type": "string", "description": "运营商"}, "siteName": {"type": "string", "description": "站点名称"}, "investmentMode": {"type": "string", "description": "投资模式"}, "settlementMethod": {"type": "string", "description": "结算方式"}, "discount": {"type": "string", "description": "折扣"}, "chargingAmount": {"type": "number", "description": "充电量"}, "actualChargingFee": {"type": "number", "description": "实际充电费"}, "dischargingAmount": {"type": "number", "description": "放电量"}, "actualDischargingFee": {"type": "number", "description": "实际放电费"}, "powerLossRate": {"type": "string", "description": "电损率"}, "income": {"type": "number", "description": "收益(元)"}, "payableAmount": {"type": "number", "description": "应付金额(元)"}, "receivableAmount": {"type": "number", "description": "应回金额(元)"}, "billingDate": {"type": "string", "description": "出账日期"}, "paymentDate": {"type": "string", "description": "回款日期"}, "settlementStatus": {"type": "string", "description": "结算进度"}, "remarks": {"type": "string", "description": "备注"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}}, "x-apifox-orders": ["bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "siteName", "investmentMode", "settlementMethod", "discount", "chargingAmount", "actualChargingFee", "dischargingAmount", "actualDischargingFee", "powerLossRate", "income", "payableAmount", "receivableAmount", "billingDate", "paymentDate", "settlementStatus", "remarks", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgReceiptQueryDTO", "displayName": "", "id": "#/definitions/169747754", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "merchantName": {"type": "string", "description": "商户名称"}, "merchantLoginAccount": {"type": "string", "description": "商户登录账号"}, "pid": {"type": "string", "description": "PID"}, "startMonth": {"type": "string", "description": "起始和截止年月开始"}, "endMonth": {"type": "string", "description": "起始和截止年月结束"}, "createdDateStart": {"type": "string", "description": "生成日期开始"}, "createdDateEnd": {"type": "string", "description": "生成日期结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "merchantName", "merchantLoginAccount", "pid", "startMonth", "endMonth", "createdDateStart", "createdDateEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationOperationDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"idList": {"type": "array", "items": {"type": "integer"}, "description": "id集合"}, "settlementMethod": {"type": "string", "description": "结算方式"}, "discount": {"type": "string", "description": "折扣"}, "billingDate": {"type": "string", "description": "出账日期"}, "paymentDate": {"type": "string", "description": "回款日期"}, "settlementStatus": {"type": "string", "description": "结算进度"}, "reconciliationPerson": {"type": "string", "description": "对账负责人"}}, "x-apifox-orders": ["idList", "settlementMethod", "discount", "billingDate", "paymentDate", "settlementStatus", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgReceiptEditDTO", "displayName": "", "id": "#/definitions/169747755", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"subject": {"type": "string", "description": "主题"}, "text": {"type": "string", "description": "正文内容"}, "from": {"type": "string", "description": "发件人"}, "to": {"type": "string", "description": "要发送的邮箱 多个人用逗号分割"}, "cc": {"type": "string", "description": "要抄送的邮箱 多个人用逗号分割"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/169758308"}, "description": "附件url"}, "id": {"type": "integer", "description": "主键", "format": "int64"}, "totalInvoiceAmount": {"type": "number", "description": "开票金额合计"}, "province": {"type": "string", "description": "省份"}, "provinceCode": {"type": "string", "description": "省份区域编码"}, "city": {"type": "string", "description": "城市"}, "cityCode": {"type": "string", "description": "城市区域编码"}, "shippingAddress": {"type": "string", "description": "详细地址"}, "recipient": {"type": "string", "description": "收件人"}, "phone": {"type": "string", "description": "电话"}, "remark": {"type": "string", "description": "备注"}}, "x-apifox-orders": ["subject", "text", "from", "to", "cc", "attachments", "id", "totalInvoiceAmount", "province", "provinceCode", "city", "cityCode", "shippingAddress", "recipient", "phone", "remark"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "MapString", "displayName": "", "id": "#/definitions/169758308", "description": "附件url", "schema": {"jsonSchema": {"type": "object", "properties": {"key": {"type": "string"}}, "x-apifox-orders": ["key"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationPaymentVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeStationPaymentVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "operatorName": {"type": "string", "description": "运营商名称"}, "fundPath": {"type": "string", "description": "资金路径 字典: st_new_charge_station_fund_path"}, "totalReceivable": {"type": "number", "description": "应收款总金额"}, "custodyRecoveryAmount": {"type": "number", "description": "管存回收金额"}, "offlinePaymentAmount": {"type": "number", "description": "线下回款金额"}, "electricityPurchaseDeduction": {"type": "number", "description": "购电款抵扣金额"}, "totalPaymentAmount": {"type": "number", "description": "合计回款金额"}, "pendingPaymentAmount": {"type": "number", "description": "待回款金额"}, "remarks": {"type": "string", "description": "备注"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "operatorName", "fundPath", "totalReceivable", "custodyRecoveryAmount", "offlinePaymentAmount", "electricityPurchaseDeduction", "totalPaymentAmount", "pendingPaymentAmount", "remarks", "updateBy", "updateTime", "tenantId", "createTime", "createBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgSettlementVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "generatedDate": {"type": "string", "description": "生成日期", "x-apifox-mock": "@datetime"}, "merchantName": {"type": "string", "description": "商户名称"}, "billingOrgCode": {"type": "string", "description": "出账机构简称"}, "merchantLoginAccount": {"type": "string", "description": "商户登录账号"}, "pid": {"type": "string", "description": "PID"}, "startMonth": {"type": "string", "description": "起始年月"}, "endMonth": {"type": "string", "description": "截止年月"}, "invoiceAmount": {"type": "number", "description": "开票金额"}, "settlementRequirements": {"type": "string", "description": "结算单要求"}, "shippingAddress": {"type": "string", "description": "寄送地址"}, "recipient": {"type": "string", "description": "收件人"}, "phone": {"type": "string", "description": "电话"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "district": {"type": "string", "description": "区县"}, "detailedAddress": {"type": "string", "description": "详细地址"}, "settlementLeader": {"type": "string", "description": "结算负责人"}, "alipayBd": {"type": "string", "description": "支付宝BD"}, "checkUrl": {"type": "string", "description": "查看地址"}, "downloadUrl": {"type": "string", "description": "下载地址"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "generatedDate", "merchantName", "billingOrgCode", "merchantLoginAccount", "pid", "startMonth", "endMonth", "invoiceAmount", "settlementRequirements", "shippingAddress", "recipient", "phone", "province", "city", "district", "detailed<PERSON>ddress", "settlementLeader", "alipayBd", "checkUrl", "downloadUrl", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeStationPaymentVO", "displayName": "", "id": "#/definitions/*********", "description": "[NewChargeStationPaymentVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeStationPaymentVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultAlipayOrgSettlementVO", "displayName": "", "id": "#/definitions/169747757", "description": "[AlipayOrgSettlementVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationPaymentQueryDTO", "displayName": "", "id": "#/definitions/171177325", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationPaymentQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": "运营商名称"}, "fundPath": {"type": "string", "description": "资金路径"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "fundPath"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "AlipayOrgSettlementQueryDTO", "displayName": "", "id": "#/definitions/169747758", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "idList": {"type": "array", "items": {"type": "integer"}, "description": "id列表"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "idList"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationPaymentImportDTO", "displayName": "", "id": "#/definitions/171177326", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationPaymentImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"operatorName": {"type": "string", "description": "运营商名称"}, "fundPath": {"type": "string", "description": "资金路径 字典:"}, "totalReceivable": {"type": "number", "description": "应收款总金额"}, "custodyRecoveryAmount": {"type": "number", "description": "管存回收金额"}, "offlinePaymentAmount": {"type": "number", "description": "线下回款金额"}, "electricityPurchaseDeduction": {"type": "number", "description": "购电款抵扣金额"}, "totalPaymentAmount": {"type": "number", "description": "合计回款金额"}, "pendingPaymentAmount": {"type": "number", "description": "待回款金额"}, "remarks": {"type": "string", "description": "备注"}}, "x-apifox-orders": ["operatorName", "fundPath", "totalReceivable", "custodyRecoveryAmount", "offlinePaymentAmount", "electricityPurchaseDeduction", "totalPaymentAmount", "pendingPaymentAmount", "remarks"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceAdjustmentVO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.lifePay.vo.PublicServiceAdjustmentVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "merchantMid": {"type": "string", "description": "商户号MID"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "product": {"type": "string", "description": "产品"}, "startYearMonth": {"type": "string", "description": "起始年月"}, "endYearMonth": {"type": "string", "description": "截至年月"}, "invoiceAmount": {"type": "number", "description": "账单金额（元）"}, "actualBillingAmount": {"type": "number", "description": "实际开票金额（元）"}, "adjustmentAmount": {"type": "number", "description": "调账金额"}, "adjustmentRatio": {"type": "number", "description": "调账比例"}, "adjustmentProgress": {"type": "string", "description": "调账进度"}, "status": {"type": "string", "description": "状态"}, "responsiblePerson": {"type": "string", "description": "负责人"}, "adjustmentReason": {"type": "string", "description": "调账原因"}, "remark": {"type": "string", "description": "备注"}, "submissionTime": {"type": "string", "description": "提交时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "settlementInstitutionName", "settlementPid", "merchantMid", "alip<PERSON><PERSON><PERSON>unt", "product", "startYearMonth", "endYearMonth", "invoiceAmount", "actualBillingAmount", "adjustmentAmount", "adjustmentRatio", "adjustmentProgress", "status", "<PERSON><PERSON><PERSON>", "adjustmentReason", "remark", "submissionTime", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeStationPaymentOperationDTO", "displayName": "", "id": "#/definitions/171177327", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeStationPaymentOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"idList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "fundPath": {"type": "string", "description": ""}}, "x-apifox-orders": ["idList", "fundPath"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultPublicServiceAdjustmentVO", "displayName": "", "id": "#/definitions/169747760", "description": "[PublicServiceAdjustmentVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": "com.whale.cloud.model.st.lifePay.vo.PublicServiceAdjustmentVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeThirdVO", "displayName": "", "id": "#/definitions/171177328", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeThirdVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "billYearMonth": {"type": "string", "description": "账单年月"}, "investorName": {"type": "string", "description": "资方名称"}, "operator": {"type": "string", "description": "运营商"}, "siteName": {"type": "string", "description": "站点名称"}, "stationRatio": {"type": "string", "description": "场站比例"}, "investorRatio": {"type": "string", "description": "资方比例"}, "platformRatio": {"type": "string", "description": "平台比例"}, "chargingAmount": {"type": "number", "description": "充电量"}, "chargingFee": {"type": "number", "description": "充电费"}, "dischargingAmount": {"type": "number", "description": "放电量"}, "dischargingFee": {"type": "number", "description": "放电费"}, "electricityRate": {"type": "string", "description": "电损率"}, "energyManagementIncome": {"type": "number", "description": "能源管理收益金额"}, "energyManagementFee": {"type": "number", "description": "能源管理服务费"}, "assetIncome": {"type": "number", "description": "资产收益"}, "platformIncome": {"type": "number", "description": "平台收入"}, "paymentProgress": {"type": "string", "description": "付款进度 字典：st_new_charge_station_third_status"}, "remarks": {"type": "string", "description": "备注"}, "reconciliationPerson": {"type": "string", "description": "负责人"}, "updateBy": {"type": "string", "description": "更新人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "investorName", "operator", "siteName", "stationRatio", "investorRatio", "platformRatio", "chargingAmount", "chargingFee", "dischargingAmount", "dischargingFee", "electricityRate", "energyManagementIncome", "energyManagementFee", "assetIncome", "platformIncome", "paymentProgress", "remarks", "<PERSON><PERSON><PERSON>", "updateBy", "updateTime", "tenantId", "createTime", "createBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceAdjustmentQueryDTO", "displayName": "", "id": "#/definitions/169747761", "description": "com.whale.cloud.model.st.lifePay.dto.PublicServiceAdjustmentQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "startYearMonth": {"type": "string", "description": "账单周期开始"}, "endYearMonth": {"type": "string", "description": "账单周期结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementInstitutionName", "settlementPid", "startYearMonth", "endYearMonth"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultNewChargeThirdVO", "displayName": "", "id": "#/definitions/171177329", "description": "[NewChargeThirdVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/171177328", "description": "com.whale.cloud.model.st.newCharge.vo.NewChargeThirdVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceSettlementVO", "displayName": "", "id": "#/definitions/169747762", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "institutionCode": {"type": "string", "description": "机构编码"}, "billingInstitutionCode": {"type": "string", "description": "出账机构编码"}, "billingInstitutionName": {"type": "string", "description": "出账机构名称"}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "status": {"type": "string", "description": "状态"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "actualSettlementMethod": {"type": "string", "description": "实际结算方式"}, "actualSettlementCycle": {"type": "string", "description": "实际结算周期"}, "actualSettlementMonth": {"type": "string", "description": "实际结算月份"}, "settlementSpecialist": {"type": "string", "description": "结算专员"}, "handlingFeeSettlementMethod": {"type": "string", "description": "手续费结算方式"}, "handlingFeeCalculationStandard": {"type": "number", "description": "手续费计算标准"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "institutionCode", "billingInstitutionCode", "billingInstitutionName", "settlementInstitutionName", "settlementPid", "status", "alip<PERSON><PERSON><PERSON>unt", "actualSettlementMethod", "actualSettlementCycle", "actualSettlementMonth", "settlementSpecialist", "handlingFeeSettlementMethod", "handlingFeeCalculationStandard", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeThirdQueryDTO", "displayName": "", "id": "#/definitions/171177330", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeThirdQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "operator": {"type": "string", "description": "运营商"}, "investorName": {"type": "string", "description": "资方名称"}, "paymentProgress": {"type": "string", "description": "付款进度"}, "siteName": {"type": "string", "description": "站点名称"}, "billYearMonthStart": {"type": "string", "description": "账单年月开始"}, "billYearMonthEnd": {"type": "string", "description": "账单年月结束"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "operator", "investorName", "paymentProgress", "siteName", "billYearMonthStart", "billYearMonthEnd"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultPublicServiceSettlementVO", "displayName": "", "id": "#/definitions/169747763", "description": "[PublicServiceSettlementVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747762", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeThirdImportDTO", "displayName": "", "id": "#/definitions/171177331", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeThirdImportDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"billYearMonth": {"type": "string", "description": "账单年月"}, "investorName": {"type": "string", "description": "资方名称"}, "operator": {"type": "string", "description": "运营商"}, "siteName": {"type": "string", "description": "站点名称"}, "stationRatio": {"type": "string", "description": "场站比例"}, "investorRatio": {"type": "string", "description": "资方比例"}, "platformRatio": {"type": "string", "description": "平台比例"}, "chargingAmount": {"type": "number", "description": "充电量"}, "chargingFee": {"type": "number", "description": "充电费"}, "dischargingAmount": {"type": "number", "description": "放电量"}, "dischargingFee": {"type": "number", "description": "放电费"}, "electricityRate": {"type": "string", "description": "电损率"}, "energyManagementIncome": {"type": "number", "description": "能源管理收益金额"}, "energyManagementFee": {"type": "number", "description": "能源管理服务费"}, "assetIncome": {"type": "number", "description": "资产收益"}, "platformIncome": {"type": "number", "description": "平台收入"}, "paymentProgress": {"type": "string", "description": "付款进度"}, "remarks": {"type": "string", "description": "备注"}, "reconciliationPerson": {"type": "string", "description": "负责人"}}, "x-apifox-orders": ["bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "investorName", "operator", "siteName", "stationRatio", "investorRatio", "platformRatio", "chargingAmount", "chargingFee", "dischargingAmount", "dischargingFee", "electricityRate", "energyManagementIncome", "energyManagementFee", "assetIncome", "platformIncome", "paymentProgress", "remarks", "<PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceSettlementQueryDTO", "displayName": "", "id": "#/definitions/169747764", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "status": {"type": "string", "description": "状态"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementInstitutionName", "settlementPid", "alip<PERSON><PERSON><PERSON>unt", "status"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "NewChargeThirdOperationDTO", "displayName": "", "id": "#/definitions/*********", "description": "com.whale.cloud.model.st.newCharge.dto.NewChargeThirdOperationDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"idList": {"type": "array", "items": {"type": "integer"}, "description": "id集合"}, "paymentProgress": {"type": "string", "description": "付款进度"}, "reconciliationPerson": {"type": "string", "description": "负责人"}, "remarks": {"type": "string", "description": "备注"}}, "x-apifox-orders": ["idList", "paymentProgress", "<PERSON><PERSON><PERSON>", "remarks"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceBillingInfoVO", "displayName": "", "id": "#/definitions/169747765", "description": "com.whale.cloud.model.st.lifePay.vo.PublicServiceBillingInfoVO", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "settlementPid": {"type": "string", "description": "结算PID"}, "provinceCityDistrict": {"type": "string", "description": "省市区"}, "shippingAddress": {"type": "string", "description": "寄送地址"}, "recipient": {"type": "string", "description": "收件人"}, "contactPhone": {"type": "string", "description": "联系电话"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "settlementPid", "provinceCityDistrict", "shippingAddress", "recipient", "contactPhone", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultPublicServiceBillingInfoVO", "displayName": "", "id": "#/definitions/169747766", "description": "[PublicServiceBillingInfoVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747765", "description": "com.whale.cloud.model.st.lifePay.vo.PublicServiceBillingInfoVO"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceBillingInfoQueryDTO", "displayName": "", "id": "#/definitions/169747767", "description": "com.whale.cloud.model.st.lifePay.dto.PublicServiceBillingInfoQueryDTO", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "settlementPid": {"type": "string", "description": "PID"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementPid"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceInvoiceInfoVO", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "submissionDate": {"type": "string", "description": "提交日期", "x-apifox-mock": "@datetime"}, "alipayMid": {"type": "string", "description": "支付宝mid"}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "alipayAccount": {"type": "string", "description": "支付宝账号"}, "settlementPid": {"type": "string", "description": "结算pid"}, "startYearMonth": {"type": "string", "description": "起始年月"}, "endYearMonth": {"type": "string", "description": "截至年月"}, "billingAmount": {"type": "number", "description": "开票金额"}, "responsiblePerson": {"type": "string", "description": "负责人"}, "invoiceOrTaxServiceName": {"type": "string", "description": "发票货物或应税劳务服务名称"}, "invoiceType": {"type": "string", "description": "发票类型"}, "invoiceRequirements": {"type": "string", "description": "开票要求"}, "invoiceCode": {"type": "string", "description": "发票代码"}, "invoiceNumber": {"type": "string", "description": "发票号"}, "billingTime": {"type": "string", "description": "开票时间", "x-apifox-mock": "@datetime"}, "expressOrderNumber": {"type": "string", "description": "快递单号"}, "logisticsStatus": {"type": "string", "description": "物流状态"}, "paymentStatus": {"type": "string", "description": "付款状态"}, "returnTicketStatus": {"type": "string", "description": "退票状态"}, "statusDescription": {"type": "string", "description": "情况说明"}, "status": {"type": "string", "description": "状态"}, "province": {"type": "string", "description": "省"}, "city": {"type": "string", "description": "市"}, "district": {"type": "string", "description": "区"}, "recipientAddress": {"type": "string", "description": "收件地址"}, "recipient": {"type": "string", "description": "收件人"}, "recipientPhone": {"type": "string", "description": "收件电话"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "submissionDate", "<PERSON><PERSON>ay<PERSON><PERSON>", "settlementInstitutionName", "alip<PERSON><PERSON><PERSON>unt", "settlementPid", "startYearMonth", "endYearMonth", "billingAmount", "<PERSON><PERSON><PERSON>", "invoiceOrTaxServiceName", "invoiceType", "invoiceRequirements", "invoiceCode", "invoiceNumber", "billingTime", "expressOrderNumber", "logisticsStatus", "paymentStatus", "returnTicketStatus", "statusDescription", "status", "province", "city", "district", "recipient<PERSON><PERSON><PERSON>", "recipient", "recipient<PERSON>hone", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultPublicServiceInvoiceInfoVO", "displayName": "", "id": "#/definitions/*********", "description": "[PublicServiceInvoiceInfoVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/*********", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceInvoiceInfoQueryDTO", "displayName": "", "id": "#/definitions/169747770", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementInstitutionName", "settlementPid"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceSettlementPlanVO", "displayName": "", "id": "#/definitions/169747771", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}, "settlementPeriod": {"type": "string", "description": "结算周期"}, "handlingFeeAmount": {"type": "number", "description": "手续费金额"}, "billingStatus": {"type": "string", "description": "开票状态"}, "collectionStatus": {"type": "string", "description": "收款状态"}, "adjustmentStatus": {"type": "string", "description": "调账状态"}, "tenantId": {"type": "integer", "description": "租户号", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "createBy": {"type": "string", "description": "创建人"}, "updateTime": {"type": "string", "description": "更新时间", "x-apifox-mock": "@datetime"}, "updateBy": {"type": "string", "description": "更新人"}, "dataSource": {"type": "string", "description": "数据来源"}}, "x-apifox-orders": ["id", "settlementInstitutionName", "settlementPid", "settlementPeriod", "handlingFeeAmount", "billingStatus", "collectionStatus", "adjustmentStatus", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataSource"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PaginationResultPublicServiceSettlementPlanVO", "displayName": "", "id": "#/definitions/169747772", "description": "[PublicServiceSettlementPlanVO]", "schema": {"jsonSchema": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/definitions/169747771", "description": ""}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}, "x-apifox-orders": ["success", "code", "message", "data", "traceId", "pageNum", "pageSize", "total"]}}, "visibility": "INHERITED", "moduleId": 4254550}, {"name": "PublicServiceSettlementPlanQueryDTO", "displayName": "", "id": "#/definitions/169747773", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "settlementInstitutionName": {"type": "string", "description": "结算机构名称"}, "settlementPid": {"type": "string", "description": "结算PID"}}, "x-apifox-orders": ["pageNum", "pageSize", "tenantId", "orgNo", "orgNoList", "operatorId", "operatorName", "settlementInstitutionName", "settlementPid"]}}, "visibility": "INHERITED", "moduleId": 4254550}]}], "securitySchemeCollection": [{"id": 1087558, "name": "根目录", "items": [], "ordering": []}], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.6759491"], "items": []}], "environments": [], "globalVariables": [{"id": "6388935", "variables": [{"name": "token", "value": "", "description": "", "isBindInitial": false, "initialValue": "", "isSync": true}]}], "commonParameters": {"id": 775841, "createdAt": "2025-05-21T07:21:53.000Z", "updatedAt": "2025-05-21T07:21:53.000Z", "deletedAt": null, "parameters": {"header": [{"name": "Authorization", "defaultEnable": true, "type": "string", "id": "3I5N4NGkvr", "defaultValue": "{{token}}", "schema": {"type": "string", "default": "{{token}}"}}]}, "projectId": 6388935, "creatorId": 2955252, "editorId": 2955252}, "customFunctions": [], "projectAssociations": []}