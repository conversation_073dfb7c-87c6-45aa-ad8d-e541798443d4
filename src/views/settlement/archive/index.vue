<!-- 结算材料归档 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      @rowDel="deleteRowHandler"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['settlement:archive:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <!-- <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['lifePay:organization:import']"
          >导入</el-button
        > -->
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['settlement:archive:add']"
          >新增</el-button
        >
      </template>
      <template #download="{row, operationType}">
        <FileTable
          :fileList="row.fileList"
          :fileOptions="{ url: 'filePath', name: 'fileName' }"
          showCheckbox
          ref="fileTable"
        ></FileTable>
      </template>
      <template #materialDetailsTable="{ params }">
        <MaterialDetailsTable
          v-model="params.materialDetailList"
          :file-type-options="typeOptions"
        />
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'download'">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="submitDownloadList" type="primary">下载</el-button>
        </div>
        <div v-else-if="crudOperationType === 'preview'"></div>
      </template>
      <template #preview="{ row, operationType }">
        <div
          v-for="(item, index) in row.fileList"
          :key="index"
          class="file-link"
        >
          <el-link @click="handlePreviewFile(index, row.fileList)">{{
            item.fileName
          }}</el-link>
        </div>
      </template>
    </BuseCrud>
    <!-- <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入机构信息"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload> -->
    <!-- 文件预览组件 -->
    <PreviewFiles
      v-if="showFilePreview"
      :url-list="previewFileList"
      :initial-index="fileIndex"
      :file-options="{ url: 'filePath', name: 'fileName' }"
      :on-close="closeFilePreview"
    />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/archive/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
// import BatchUpload from "@/components/BatchUpload/index.vue";
import FileTable from "@/components/PreviewFiles/fileTable.vue";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { downLoadUrl2Blob } from "@/api/common.js";
import { fileDownLoad } from "@/utils/downLoad.js";
import MaterialDetailsTable from "./components/MaterialDetailsTable.vue";
export default {
  name: "settlementArchive",
  components: { FileTable, PreviewFiles, MaterialDetailsTable },
  mixins: [exportMixin],
  data() {
    return {
      // uploadObj: {
      //   api: "/st/material/importExcel",
      //   url: "/charging-maintenance-ui/static/结算材料归档导入模板.xlsx",
      //   extraData: {},
      // },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      //buse参数-e

      businessTypeOptions: [],
      onlineStatusOptions: [],
      serviceProviderOptions: [],
      institutionNameOptions: [],
      companyNameOptions: [],
      customerNameOptions: [],
      fileTypeOptions: [
        { dictLabel: "收入", dictValue: "income" },
        { dictLabel: "支出", dictValue: "expense" },
      ],

      // 文件预览相关状态
      showFilePreview: false,
      previewFileList: [],
      typeOptions: [],
      fileIndex: 0,
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取所属业务字典
    this.getDicts("st_material_biz").then((response) => {
      this.businessTypeOptions = response.data;
    });

    // 获取所属公司字典
    this.getDicts("st_material_company").then((response) => {
      this.companyNameOptions = response.data;
    });

    // 获取客户字典
    this.getDicts("st_material_customer").then((response) => {
      this.customerNameOptions = response.data;
    });

    // 获取材料类型字典
    this.getDicts("st_material_type").then((response) => {
      this.typeOptions = response.data;
    });

    // 获取下拉列表数据
    // api.getDropLists().then((res) => {
    //   if (res.success) {
    //     // 处理下拉列表数据
    //     if (res.data.institutionName) {
    //       this.institutionNameOptions = res.data.institutionName.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //     if (res.data.serviceProvider) {
    //       this.serviceProviderOptions = res.data.serviceProvider.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //   }
    // });

    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },
    async submitDownloadList() {
      const list = this.$refs.fileTable.getCheckboxRecords();
      if (list?.length === 0) {
        this.$message.warning("请勾选要下载的文件");
        return;
      }
      const downloadPromises = list.map(async (x) => {
        try {
          const res = await downLoadUrl2Blob({ fileUrl: x.filePath });
          if (res) {
            await fileDownLoad(res, x.fileName);
          }
        } catch (error) {
          // 处理错误，例如显示提示信息
          console.error(`Error downloading file: ${x.fileName}`, error);
        }
      });
      this.downloadLoading = true;
      try {
        // 等待所有文件下载完成
        await Promise.all(downloadPromises);
      } finally {
        // 无论成功还是失败，都将 downloadLoading 设为 false
        this.downloadLoading = false;
      }
    },

    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        settleRangeTime:
          row.startDate && row.endDate ? [row.startDate, row.endDate] : [],
      });
    },
    handlePreview(row) {
      console.log(row, "预览");
      if (row.materialDetailList.length == 0) {
        this.$message.warning("没有可预览的文件");
      } else if (row.materialDetailList.length == 1) {
        this.handlePreviewFile(0, row.materialDetailList);
      } else {
        this.$refs.crud.switchModalView(true, "preview", {
          fileList: row.materialDetailList,
        });
      }
    },
    handleDownload(row) {
      this.$refs.crud.switchModalView(true, "download", {
        fileList: row.materialDetailList,
      });
    },

    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "settleRangeTime",
          title: "结算周期",
          startFieldName: "startDate",
          endFieldName: "endDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        this.handleTimeRange(params);
        // crudOperationType:update
        const apiMethod = params.id ? api.edit : api.add;
        const res = await apiMethod(params).catch(() => {
          resolve(false);
        });
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },

    // 处理文件预览
    handlePreviewFile(index, fileList) {
      if (fileList.length > 0) {
        this.previewFileList = fileList;
        this.showFilePreview = true;
        this.fileIndex = index;
      } else {
        this.$message.warning("暂无文件可预览");
      }
    },

    // 关闭文件预览
    closeFilePreview() {
      this.showFilePreview = false;
      this.previewFileList = [];
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "companyName",
          title: "所属公司名称",
          width: 180,
        },
        {
          field: "bizTypeName",
          title: "所属业务",
          width: 120,
        },
        {
          field: "customerName",
          title: "客户名称",
          width: 120,
        },
        {
          field: "settlementCycle",
          title: "结算周期",
          width: 120,
        },
        {
          field: "typeName",
          title: "类型",
          width: 120,
        },
        {
          field: "docName",
          title: "文件名称",
          width: 120,
        },
        {
          field: "createBy",
          title: "提交人",
          width: 120,
        },
        {
          field: "createTime",
          title: "提交时间",
          width: 120,
        },
        {
          field: "updateBy",
          title: "修改人",
          width: 120,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 120,
        },
        {
          field: "materialDetailList",
          title: "附件",
          width: 100,
          fixed: "right",
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.handlePreview(row),
                  }}
                >
                  查看
                </el-link>,
              ];
            },
          },
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "companyCode",
            // element: "el-select",
            title: "所属公司名称",
            // props: {
            //   options: this.companyNameOptions,
            //   optionLabel: "dictLabel",
            //   optionValue: "dictValue",
            //   filterable: true,
            //   placeholder: "请选择所属公司",
            // },
          },
          {
            field: "bizType",
            title: "所属业务",
            // element: "el-select",
            // props: {
            //   options: this.businessTypeOptions,
            //   optionLabel: "dictLabel",
            //   optionValue: "dictValue",
            //   filterable: true,
            //   placeholder: "请选择所属业务",
            // },
          },
          {
            field: "customerCode",
            title: "客户名称",
            // element: "el-select",
            // props: {
            //   options: this.customerNameOptions,
            //   optionLabel: "dictLabel",
            //   optionValue: "dictValue",
            //   filterable: true,
            //   placeholder: "请选择客户",
            // },
          },
          {
            field: "type",
            element: "el-select",
            title: "类型",
            props: {
              options: this.typeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择类型",
            },
          },
          {
            field: "settleRangeTime",
            title: "结算周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "companyCode",
            title: "所属公司名称",
            element: "el-select",
            rules: [{ required: true, message: "请选择所属公司名称" }],
            props: {
              options: this.companyNameOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择所属公司名称",
            },
          },
          {
            field: "bizType",
            title: "所属业务",
            element: "el-select",
            rules: [{ required: true, message: "请选择所属业务" }],
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择所属业务",
            },
          },
          {
            field: "customerCode",
            title: "客户名称",
            element: "el-select",
            rules: [{ required: true, message: "请选择客户名称" }],
            props: {
              options: this.customerNameOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择客户名称",
            },
          },
          {
            field: "docName",
            title: "文件名称",
            element: "el-input",
            rules: [
              { required: true, message: "请输入文件名称" },
              { max: 100, message: "文件名称不能超过100个字符" },
            ],
            attrs: {
              maxlength: 100,
              placeholder: "请输入文件名称，100个字符以内",
            },
          },
          {
            field: "settleRangeTime",
            title: "结算周期",
            element: "el-date-picker",
            rules: [{ required: true, message: "请选择结算周期" }],
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
              format: "yyyy-MM-dd",
            },
          },
          {
            field: "materialDetailList",
            title: "材料明细",
            element: "slot",
            slotName: "materialDetailsTable",
            itemProps: {
              labelWidth: "110px",
            },
            rules: [{ required: true, message: "请上传" }],
            defaultValue: [
              {
                type: "",
                fileName: "",
                filePath: "",
              },
            ],
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["settlement:archive:edit"]),
        delBtn: checkPermission(["settlement:archive:delete"]),
        menu: true,
        menuWidth: 200,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "下载",
            typeName: "download",
            slotName: "download",
            showForm: false,
            event: (row) => {
              return this.handleDownload(row);
            },
            condition: (row) => {
              return checkPermission(["settlement:archive:download"]);
            },
          },
          //非操作列
          {
            title: "附件",
            typeName: "preview",
            slotName: "preview",
            showForm: false,
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style scoped lang="less">
.file-link {
  padding: 20px 20px 0;
}
</style>
