<template>
  <el-drawer :title="title" :visible.sync="drawerVisible" size="80%">
    <div v-if="drawerVisible" v-loading="loading">
      <StationConfigTable
        :columns="columns"
        v-model="formData"
        ref="stationTable"
        showAddBtn
      ></StationConfigTable>

      <div class="drawer-btn">
        <el-button @click.stop="handleClose" size="medium">取 消</el-button>
        <el-button @click="handleSubmit" type="primary" size="medium"
          >确 定</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script>
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import { initParams } from "@/utils/buse.js";

export default {
  name: "BatchAddDrawer",
  components: { StationConfigTable },
  props: {
    // 表格列配置
    columns: {
      type: Array,
      required: true,
    },
    // 抽屉标题
    title: {
      type: String,
      default: "批量新增",
    },
  },
  data() {
    return {
      drawerVisible: false,
      loading: false,
      formData: [],
    };
  },

  methods: {
    // 打开抽屉
    open() {
      this.drawerVisible = true;
      // 初始化一行空数据
      this.formData = [{ ...initParams(this.columns) }];
    },

    // 关闭抽屉
    handleClose() {
      this.drawerVisible = false;
      this.formData = [];
    },

    // 提交数据
    async handleSubmit() {
      // 验证表格数据
      const valid = await this.$refs.stationTable.validate();
      console.log("valid", valid);
      if (!valid) {
        this.$message.error("请检查表格数据填写是否正确");
        return;
      }

      if (!this.formData.length) {
        this.$message.warning("请至少添加一条数据");
        return;
      }

      // 触发父组件的提交事件
      this.$emit("submit", this.formData);
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
