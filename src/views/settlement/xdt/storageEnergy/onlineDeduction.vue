<!-- 线上扣款记录 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtStorageEnergy:onlineDeduction:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtStorageEnergy:onlineDeduction:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入线上扣款记录"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/storageEnergy/onlineDeduction.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
export default {
  name: "onlineDeduction",
  mixins: [exportMixin],
  components: { BatchUpload },
  data() {
    return {
      selectPage: "1",
      workLoading: false,
      uploadObj: {
        api: "/export/report/newcharge/online/importExcel",
        url: "/charging-maintenance-ui/static/线上扣款记录导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
      reconciliationPersonOptions: [],
      recordList: [],
      costTypeOptions: [],
      billReviewOptions: [],
      selectedData: [],
      settlementMethodOptions: [],
      paymentStatusOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取下拉列表数据
    this.loadDropListData();
  },
  methods: {
    checkPermission,
    handleResetAll() {
      this.loadDropListData();
      this.handleQuery();
    },
    handleBatchImport() {
      this.$refs.batchUpload.open();
    },

    // 加载下拉列表数据
    async loadDropListData() {
      try {
        api
          .getDropLists({
            fieldName: "settlement_method",
            pageSize: 9999,
            pageNum: 1,
          })
          .then((res) => {
            this.settlementMethodOptions = res.data?.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          });
        api
          .getDropLists({
            fieldName: "payment_status",
            pageSize: 9999,
            pageNum: 1,
          })
          .then((res) => {
            this.paymentStatusOptions = res.data?.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          });
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },

    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billMonthRange",
          title: "账单月份",
          startFieldName: "billMonthStart",
          endFieldName: "billMonthEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
      this.loadDropListData();
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType: add/update/batchOperation
      try {
        await api[crudOperationType](params);
        this.$message.success("提交成功");
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "operatorName",
          title: "运营商名称",
          width: 150,
        },
        {
          field: "billDate",
          title: "账单日",
          width: 120,
        },
        {
          field: "billMonth",
          title: "账单月",
          width: 120,
          formatter: ({ row }) => {
            return row.billMonthStart + "~" + row.billMonthEnd;
          },
        },
        {
          field: "settlementMethod",
          title: "结算方式",
          width: 120,
        },
        {
          field: "chargingDegree",
          title: "充电量度数",
          width: 120,
        },
        {
          field: "chargingAmount",
          title: "充电量金额",
          width: 120,
        },
        {
          field: "chargingSettlementRatio",
          title: "充电结算比例",
          width: 120,
        },
        {
          field: "dischargingDegree",
          title: "放电量度数",
          width: 120,
        },
        {
          field: "dischargingAmount",
          title: "放电量金额",
          width: 120,
        },
        {
          field: "dischargingStationRatio",
          title: "放电结算比例",
          width: 120,
        },
        {
          field: "adjustedChargingFee",
          title: "调整充电费",
          width: 120,
        },
        {
          field: "adjustedDischargingFee",
          title: "调整放电费",
          width: 120,
        },
        {
          field: "difference",
          title: "税差",
          width: 120,
        },
        {
          field: "platformPayable",
          title: "平台应付",
          width: 120,
        },
        {
          field: "platformReceivable",
          title: "平台应收",
          width: 120,
        },
        {
          field: "platformPendingPay",
          title: "平台待付",
          width: 120,
        },
        {
          field: "platformPendingReceive",
          title: "平台待收",
          width: 120,
        },
        {
          field: "paymentStatus",
          title: "付款状态",
          width: 120,
        },
        {
          field: "platformActualReceive",
          title: "轧差后平台实收",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "operatorName",
            element: "el-input",
            title: "运营商",
          },
          {
            field: "settlementMethod",
            element: "el-select",
            title: "结算方式",
            props: {
              options: this.settlementMethodOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "paymentStatus",
            element: "el-select",
            title: "付款状态",
            props: {
              options: this.paymentStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "billMonthRange",
            title: "账单月份",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
