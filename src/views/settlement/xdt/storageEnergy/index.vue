<!-- 新电途储能管理 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <StationProgressPage
      v-show="tabActiveTab === 'stationProgress'"
      ref="stationProgress"
      :searchParams="searchParams"
    ></StationProgressPage>
    <OtherProgressPage
      v-show="tabActiveTab === 'otherProgress'"
      ref="otherProgress"
      @jump="handleJump"
    ></OtherProgressPage>
    <OnlineDeductionPage
      v-show="tabActiveTab === 'onlineDeduction'"
      ref="onlineDeduction"
    ></OnlineDeductionPage>
    <VenueStatisticsPage
      v-show="tabActiveTab === 'venueStatistics'"
      ref="venueStatistics"
    ></VenueStatisticsPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import StationProgressPage from "./stationProgress.vue";
import OtherProgressPage from "./otherProgress.vue";
import OnlineDeductionPage from "./onlineDeduction.vue";
import VenueStatisticsPage from "./venueStatistics.vue";

export default {
  name: "xdtStorageEnergy",
  components: {
    StationProgressPage,
    OtherProgressPage,
    OnlineDeductionPage,
    VenueStatisticsPage,
  },
  data() {
    return {
      tabActiveTab: "stationProgress",
      topTabDict: [
        {
          value: "stationProgress",
          label: "场站结算进度",
          show: () => {
            return this.checkPermission([
              "xdtStorageEnergy:stationProgress:list",
            ]);
          },
        },
        {
          value: "otherProgress",
          label: "他投结算进度",
          show: () => {
            return this.checkPermission([
              "xdtStorageEnergy:otherProgress:list",
            ]);
          },
        },
        {
          value: "onlineDeduction",
          label: "线上扣款记录",
          show: () => {
            return this.checkPermission([
              "xdtStorageEnergy:onlineDeduction:list",
            ]);
          },
        },
        {
          value: "venueStatistics",
          label: "场地回款统计",
          show: () => {
            return this.checkPermission([
              "xdtStorageEnergy:venueStatistics:list",
            ]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // 可以在这里处理标签页跳转逻辑
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
