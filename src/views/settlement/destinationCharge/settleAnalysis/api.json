[{"index": 0, "name": "场站结算情况分析 控制层", "desc": "场站结算情况分析 控制层", "add_time": 1749030146, "up_time": 1749030146, "list": [{"query_path": {"path": "/st/stationSettlementAnalysis/queryList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147307, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationNo\":{\"type\":\"string\",\"description\":\"\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"operationStatus\":{\"type\":\"string\",\"description\":\"运营状态，01-建设中，02-运营，03-停运，04-检修, 05-退运，06-已删除\"},\"manager\":{\"type\":\"string\",\"description\":\"负责人\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"城市\"},\"region\":{\"type\":\"string\",\"description\":\"所属区域\"},\"onlineStartDate\":{\"type\":\"string\",\"description\":\"上线日期\",\"mock\":{\"mock\":\"@datetime\"}},\"onlineEndDate\":{\"type\":\"string\",\"description\":\"\",\"mock\":{\"mock\":\"@datetime\"}}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/st/stationSettlementAnalysis/queryList", "catid": 20005, "markdown": "", "req_headers": [{"required": "1", "_id": "68402b7a4ed726d489acad42", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"主键\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"stationType\":{\"type\":\"string\",\"description\":\"站点类型\"},\"construction\":{\"type\":\"string\",\"description\":\"建设场所\"},\"stationChargeType\":{\"type\":\"string\",\"description\":\"站点充电类型：字典cm_station_charge_type\"},\"operationStatus\":{\"type\":\"string\",\"description\":\"运营状态，字典cm_station_status\"},\"openDate\":{\"type\":\"string\",\"description\":\"投运日期\",\"mock\":{\"mock\":\"@datetime\"}},\"onlineDate\":{\"type\":\"string\",\"description\":\"上线日期\",\"mock\":{\"mock\":\"@datetime\"}},\"pileNum\":{\"type\":\"integer\",\"description\":\"充电桩数量\"},\"settlementCycle\":{\"type\":\"string\",\"description\":\"实际结算周期，字典settlement_cycle\"},\"region\":{\"type\":\"string\",\"description\":\"所属区域\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"城市\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算对象类型，字典settlement_type\"},\"lastMeterTime\":{\"type\":\"string\",\"description\":\"最近抄表时间\"},\"siteElectricity\":{\"type\":\"number\",\"description\":\"场地方电量（kWh）\"},\"siteFee\":{\"type\":\"number\",\"description\":\"场地方电费（元）\"},\"platformCharge\":{\"type\":\"number\",\"description\":\"平台充电电量（kWh）\"},\"platformFee\":{\"type\":\"number\",\"description\":\"平台充电电费（元）\"},\"electricalLoss\":{\"type\":\"number\",\"description\":\"电损=(场地方电量-充电电量)/场地方电量\"},\"feeSettle\":{\"type\":\"number\",\"description\":\"电费结算金额（元）\"},\"feeDeviation\":{\"type\":\"number\",\"description\":\"电费偏差（%）\"},\"servicePerson\":{\"type\":\"string\",\"description\":\"服务费结算人员\"},\"serviceRule\":{\"type\":\"string\",\"description\":\"服务费结算规则\"},\"serviceStartDate\":{\"type\":\"string\",\"description\":\"服务费初始结算时间\"},\"serviceLastDate\":{\"type\":\"string\",\"description\":\"服务费最近结算时间\"},\"serviceSettleAmount\":{\"type\":\"number\",\"description\":\"服务费结算金额\"},\"remark\":{\"type\":\"string\",\"description\":\"结算特殊情况备注\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030146, "up_time": 1749035898, "__v": 0}, {"query_path": {"path": "/st/stationSettlementAnalysis/queryStationList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147352, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationNo\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询 充电平台的站点信息", "path": "/st/stationSettlementAnalysis/queryStationList", "catid": 20005, "markdown": "", "req_headers": [{"required": "1", "_id": "68402b9f4ed726b5b1acad48", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"stationId\":{\"type\":\"string\",\"description\":\"\"},\"stationNo\":{\"type\":\"string\",\"description\":\"站点编码，运营商自编的编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"stationType\":{\"type\":\"string\",\"description\":\"站点类型\"},\"pileNum\":{\"type\":\"integer\",\"description\":\"站点充电桩个数\"},\"province\":{\"type\":\"string\",\"description\":\"省\"},\"city\":{\"type\":\"string\",\"description\":\"城市\"},\"county\":{\"type\":\"string\",\"description\":\"区/县\"},\"stationAddress\":{\"type\":\"string\",\"description\":\"地址：区下面的详细地址\"},\"operationStatus\":{\"type\":\"string\",\"description\":\"运营状态，01-建设中，02-运营，03-停运，04-检修, 05-退运，06-已删除\"},\"buildDate\":{\"type\":\"string\",\"description\":\"建设时间\",\"mock\":{\"mock\":\"@datetime\"}},\"openDate\":{\"type\":\"string\",\"description\":\"投运时间\",\"mock\":{\"mock\":\"@datetime\"}},\"onlineDate\":{\"type\":\"string\",\"description\":\"上线日期\",\"mock\":{\"mock\":\"@datetime\"}},\"openFlag\":{\"type\":\"integer\",\"description\":\"是否对外开放1是0否\"},\"stationChargeType\":{\"type\":\"string\",\"description\":\"站点充电分类 标准代码stationchargetype 01交流充电站 02直流充电桩 03交直流混合充电站\"},\"businessTime\":{\"type\":\"string\",\"description\":\"运营时间\"},\"regionTag\":{\"type\":\"string\",\"description\":\"\"},\"stationTag\":{\"type\":\"string\",\"description\":\"站点标签\"},\"serviceTag\":{\"type\":\"string\",\"description\":\"服务标签\"},\"tpStationNo\":{\"type\":\"string\",\"description\":\"第三方站点编码,一般用于互联互通同步时，存储对方的站点编码\"},\"lon\":{\"type\":\"number\",\"description\":\"坐标经度\"},\"lat\":{\"type\":\"number\",\"description\":\"坐标纬度\"},\"stationObjectType\":{\"type\":\"string\",\"description\":\"站点实体类型，01-实体，02-虚拟。也可用以充电站点，如：01充电站 02充电点。\"},\"construction\":{\"type\":\"string\",\"description\":\"建设场所 字典维护 code：construction\"},\"subConstruction\":{\"type\":\"string\",\"description\":\"\"},\"serviceTel\":{\"type\":\"string\",\"description\":\"服务电话\"},\"stationUrl\":{\"type\":\"string\",\"description\":\"站点图片\"},\"stationIntroduce\":{\"type\":\"string\",\"description\":\"站点简介\"},\"siteGuide\":{\"type\":\"string\",\"description\":\"站点引导\"},\"operatorCode\":{\"type\":\"string\",\"description\":\"站点运营商code(资产单位)\"},\"parkNum\":{\"type\":\"integer\",\"description\":\"车位数量\"},\"parkFeeDescription\":{\"type\":\"string\",\"description\":\"停车费说明\"},\"deleteFlag\":{\"type\":\"boolean\",\"description\":\"是否删除\"},\"createUser\":{\"type\":\"string\",\"description\":\"创建人员\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateUser\":{\"type\":\"string\",\"description\":\"更新人员\"},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"returnedReason\":{\"type\":\"string\",\"description\":\"退运原因\"},\"merchantId\":{\"type\":\"string\",\"description\":\"租户 id\"},\"assetUnit\":{\"type\":\"string\",\"description\":\"资产单位\"},\"parkingChargeRemark\":{\"type\":\"string\",\"description\":\"停车收费说明\"},\"parkingChargeType\":{\"type\":\"string\",\"description\":\"停车收费类型\"},\"locationType\":{\"type\":\"string\",\"description\":\"位置类型\"},\"stationTags\":{\"type\":\"string\",\"description\":\"站点标签(旧)\"},\"runTime\":{\"type\":\"string\",\"description\":\"站点运营时间\"},\"carType\":{\"type\":\"string\",\"description\":\"支持车型\"},\"parkingLocation\":{\"type\":\"string\",\"description\":\"停车位置\"},\"parkingLot\":{\"type\":\"string\",\"description\":\"停车场\"},\"operatorId\":{\"type\":\"string\",\"description\":\"运营商a_operator表主键\"},\"pileVOList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"pileId\":{\"type\":\"string\",\"description\":\"充电桩id\"},\"pileNo\":{\"type\":\"string\",\"description\":\"桩编号\"},\"pileName\":{\"type\":\"string\",\"description\":\"桩名称\"},\"assetCode\":{\"type\":\"string\",\"description\":\"资产编码\"},\"deviceId\":{\"type\":\"string\",\"description\":\"ioe设备id\"},\"protocolCode\":{\"type\":\"string\",\"description\":\"ioe产品code\"},\"protocolName\":{\"type\":\"string\",\"description\":\"ioe产品名称\"},\"stationId\":{\"type\":\"string\",\"description\":\"站点id\"},\"stationNo\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"pileBrandId\":{\"type\":\"string\",\"description\":\"桩品牌id\"},\"pileModelId\":{\"type\":\"string\",\"description\":\"桩型号id\"},\"stackPower\":{\"type\":\"number\",\"description\":\"充电堆功率\"},\"stackNo\":{\"type\":\"string\",\"description\":\"充电堆编号\"},\"operStatus\":{\"type\":\"string\",\"description\":\"运营状态，引用标准代码在【设备分类】中定义。\"},\"operDate\":{\"type\":\"string\",\"description\":\"投运时间\",\"mock\":{\"mock\":\"@datetime\"}},\"ratePower\":{\"type\":\"number\",\"description\":\"额定功率，单位kw\"},\"subType\":{\"type\":\"string\",\"description\":\"子分类编码，设备子类，引用标准代码在【类分类class_sort】中定义。如：充电桩 01交流式 02直流式 03交直流一体\"},\"pilePostalAddress\":{\"type\":\"string\",\"description\":\"桩通讯地址\"},\"openFlag\":{\"type\":\"string\",\"description\":\"是否开放\"},\"createUser\":{\"type\":\"string\",\"description\":\"创建人员\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateUser\":{\"type\":\"string\",\"description\":\"更新人员\"},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"merchantId\":{\"type\":\"string\",\"description\":\"租户 id\"},\"appointFlag\":{\"type\":\"string\",\"description\":\"是否允许预约\"},\"brandInfo\":{\"type\":\"object\",\"properties\":{\"brandId\":{\"type\":\"string\",\"description\":\"品牌id\"},\"brandName\":{\"type\":\"string\",\"description\":\"品牌名称，如：大众\"},\"brandLevel\":{\"type\":\"string\",\"description\":\"品牌等级，01品牌 02品牌小类。如：大众 > 一汽大众\"},\"parentBrandId\":{\"type\":\"string\",\"description\":\"上级品牌id\"},\"brandSn\":{\"type\":\"integer\",\"description\":\"排序号\"},\"dataSources\":{\"type\":\"string\",\"description\":\"数据来源：01-内部生成，02-外部接入（互联互通）\"},\"logoUrl\":{\"type\":\"string\",\"description\":\"品牌logo\"},\"createUser\":{\"type\":\"string\",\"description\":\"创建人\"},\"updateUser\":{\"type\":\"string\",\"description\":\"更新人\"},\"createTime\":{\"type\":\"string\",\"description\":\"数据操作时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"数据操作时间\",\"mock\":{\"mock\":\"@datetime\"}},\"merchantId\":{\"type\":\"string\",\"description\":\"租户 id\"}},\"description\":\"\"},\"modelPileInfo\":{\"type\":\"object\",\"properties\":{\"modelId\":{\"type\":\"string\",\"description\":\"型号id\"},\"brandId\":{\"type\":\"string\",\"description\":\"品牌id\"},\"subType\":{\"type\":\"string\",\"description\":\"子分类编码，设备子类，引用标准代码在【类分类class_sort】中定义。如：充电桩 01交流式 02直流式 03交直流一体\"},\"modelName\":{\"type\":\"string\",\"description\":\"型号名称\"},\"spec\":{\"type\":\"string\",\"description\":\"规格\"},\"manufacturer\":{\"type\":\"string\",\"description\":\"生产厂家\"},\"manufacturerCode\":{\"type\":\"string\",\"description\":\"生产厂商机构代码\"},\"nominalVoltage\":{\"type\":\"number\",\"description\":\"额定电压，单位v\"},\"ratePower\":{\"type\":\"number\",\"description\":\"额定功率，单位kw\"},\"rateCurrent\":{\"type\":\"number\",\"description\":\"额定电流，单位a\"},\"gunNominalVoltage\":{\"type\":\"number\",\"description\":\"额定电压，单位v(枪)\"},\"gunRateCurrent\":{\"type\":\"number\",\"description\":\"额定电流，单位a(枪)\"},\"gunAmortizedPower\":{\"type\":\"number\",\"description\":\"均摊功率，单位kw(枪)\"},\"gunMaxPower\":{\"type\":\"number\",\"description\":\"最大功率(枪)\"},\"protocolCode\":{\"type\":\"string\",\"description\":\"ioe产品id\"},\"protocolName\":{\"type\":\"string\",\"description\":\"ioe产品名称\"},\"commType\":{\"type\":\"string\",\"description\":\"通讯模块类型\"},\"gunNum\":{\"type\":\"integer\",\"description\":\"充电枪数量\"},\"couplerType\":{\"type\":\"string\",\"description\":\"接口类型 字典couplertype维护\"},\"pileTcu\":{\"type\":\"string\",\"description\":\"桩tcu\"},\"supDischarge\":{\"type\":\"integer\",\"description\":\"是否支持放电 1支持、0不支持\"},\"equipLogoUrl\":{\"type\":\"string\",\"description\":\"设备图片url\"},\"supPowerChange\":{\"type\":\"integer\",\"description\":\"是否支持功率调整：1支持、0不支持\"},\"enableFlag\":{\"type\":\"integer\",\"description\":\"是否启用：0不启用、1启用\"},\"version\":{\"type\":\"string\",\"description\":\"软件版本\"},\"createUser\":{\"type\":\"string\",\"description\":\"创建人\"},\"updateUser\":{\"type\":\"string\",\"description\":\"更新人\"},\"createTime\":{\"type\":\"string\",\"description\":\"数据操作时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"数据操作时间\",\"mock\":{\"mock\":\"@datetime\"}},\"merchantId\":{\"type\":\"string\",\"description\":\"租户 id\"}},\"description\":\"\"}}},\"description\":\"\"},\"operators\":{\"type\":\"object\",\"properties\":{\"operatorId\":{\"type\":\"string\",\"description\":\"商户id\"},\"orgCode\":{\"type\":\"string\",\"description\":\"组织编码\"},\"operatorNo\":{\"type\":\"string\",\"description\":\"商户编号\"},\"operatorName\":{\"type\":\"string\",\"description\":\"商户名称\"},\"operatorType\":{\"type\":\"string\",\"description\":\"字典维护 operatorType:运营商类型\"},\"shortname\":{\"type\":\"string\",\"description\":\"商户简称\"},\"contact\":{\"type\":\"string\",\"description\":\"联系人\"},\"contactTel\":{\"type\":\"string\",\"description\":\"联系电话\"},\"email\":{\"type\":\"string\",\"description\":\"邮箱\"},\"address\":{\"type\":\"string\",\"description\":\"地址\"},\"socialCreditCode\":{\"type\":\"string\",\"description\":\"统一社会信用代码\"},\"businessLicenceType\":{\"type\":\"string\",\"description\":\"营业执照类型 标准代码 businessLicenceType\"},\"legalRepresentative\":{\"type\":\"string\",\"description\":\"法定代表人名称\"},\"legalRepresentativeCode\":{\"type\":\"string\",\"description\":\"法人证件号\"},\"registeredCapital\":{\"type\":\"number\",\"description\":\"注册资本\"},\"foundDate\":{\"type\":\"string\",\"description\":\"成立日期\",\"mock\":{\"mock\":\"@datetime\"}},\"businessTermStart\":{\"type\":\"string\",\"description\":\"营业期限-开始时间\",\"mock\":{\"mock\":\"@datetime\"}},\"businessTermEnd\":{\"type\":\"string\",\"description\":\"营业期限-结束时间\",\"mock\":{\"mock\":\"@datetime\"}},\"linkMode\":{\"type\":\"string\",\"description\":\"接入方式 字典维护 linkMode\"},\"saasMode\":{\"type\":\"string\",\"description\":\"商户模式 字典维护 saasMode: 01共享客户及资金 02独立客户及资金\"},\"assetFlag\":{\"type\":\"string\",\"description\":\"资产标志  0否 1是\"},\"operationFlag\":{\"type\":\"string\",\"description\":\"运营标志  0否 1是\"},\"maintainFlag\":{\"type\":\"string\",\"description\":\"运维标志  0否 1是\"},\"platFlag\":{\"type\":\"string\",\"description\":\"平台标志  0否 1是\"},\"govFlag\":{\"type\":\"string\",\"description\":\"政府标志  0否 1是\"},\"superviseFlag\":{\"type\":\"string\",\"description\":\"监管标志  0否 1是\"},\"invoiceAttribute\":{\"type\":\"string\",\"description\":\"开票所属 字典维护 invoiceAttribute 00平台开票 01运营商开票\"},\"invoiceProjectNo\":{\"type\":\"string\",\"description\":\"开票项目编号\"},\"channelFlag\":{\"type\":\"string\",\"description\":\"渠道商标志  01是 02否\"},\"businessScope\":{\"type\":\"string\",\"description\":\"经营范围\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"createUser\":{\"type\":\"string\",\"description\":\"创建人\"},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateUser\":{\"type\":\"string\",\"description\":\"更新人\"},\"operatorStatus\":{\"type\":\"string\",\"description\":\"商户状态 字典维护operatorStatus 01启用 02禁用\"},\"businessLicensePicture\":{\"type\":\"string\",\"description\":\"营业执照图片\"},\"merchantId\":{\"type\":\"string\",\"description\":\"租户 id\"},\"operatorCode\":{\"type\":\"string\",\"description\":\"运营商编码\"}},\"description\":\"\"},\"operationMode\":{\"type\":\"string\",\"description\":\"运营模式 1自营 2代运营\"},\"assetNumber\":{\"type\":\"string\",\"description\":\"资产编号\"},\"invoicingMethod\":{\"type\":\"string\",\"description\":\"开票方式\"},\"invoicingParty\":{\"type\":\"string\",\"description\":\"开票方\"},\"invoicingPartyName\":{\"type\":\"string\",\"description\":\"\"},\"regionTagName\":{\"type\":\"string\",\"description\":\"\"},\"platformMerchantId\":{\"type\":\"string\",\"description\":\"\"},\"platformMerchantName\":{\"type\":\"string\",\"description\":\"\"},\"canBooking\":{\"type\":\"integer\",\"description\":\"是否支持预约，0-不支持，1-支持\"},\"settleMode\":{\"type\":\"string\",\"description\":\"站点结算模式： RECEIPT_MONEY-款项到帐 CUSHION_SETTLEMENT-垫资结算\"},\"controlSoc\":{\"type\":\"string\",\"description\":\"soc阈值\"},\"chargeType\":{\"type\":\"string\",\"description\":\"充电方式\"},\"pushRegulation\":{\"type\":\"string\",\"description\":\"是否推送监管平台 0-否 1-是\"},\"formConfigName\":{\"type\":\"string\",\"description\":\"站点表单配置名称\"}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030148, "up_time": 1749035935, "__v": 0}, {"query_path": {"path": "/st/stationSettlementAnalysis/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147325, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/st/stationSettlementAnalysis/remove", "catid": 20005, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "68402b8a4ed72658c4acad44", "name": "id", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030147, "up_time": 1749035914, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/st/stationSettlementAnalysis/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147343, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationNo\":{\"type\":\"string\",\"description\":\"\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"operationStatus\":{\"type\":\"string\",\"description\":\"运营状态，01-建设中，02-运营，03-停运，04-检修, 05-退运，06-已删除\"},\"manager\":{\"type\":\"string\",\"description\":\"负责人\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"城市\"},\"region\":{\"type\":\"string\",\"description\":\"所属区域\"},\"onlineStartDate\":{\"type\":\"string\",\"description\":\"上线日期\",\"mock\":{\"mock\":\"@datetime\"}},\"onlineEndDate\":{\"type\":\"string\",\"description\":\"\",\"mock\":{\"mock\":\"@datetime\"}}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/st/stationSettlementAnalysis/export", "catid": 20005, "markdown": "", "req_headers": [{"required": "1", "_id": "68402b984ed7262269acad47", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030147, "up_time": 1749035928, "__v": 0}, {"query_path": {"path": "/st/stationSettlementAnalysis/import", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147334, "req_body_type": "form", "res_body_type": "json", "title": "批量导入", "path": "/st/stationSettlementAnalysis/import", "catid": 20005, "markdown": "", "req_headers": [{"required": "1", "_id": "68402b914ed726435facad45", "name": "Content-Type", "value": "multipart/form-data", "example": "multipart/form-data"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [{"required": "1", "_id": "68402b914ed726647bacad46", "name": "file", "type": "file", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030147, "up_time": 1749035921, "__v": 0}, {"query_path": {"path": "/st/stationSettlementAnalysis/save", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147316, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"主键\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编码\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"stationType\":{\"type\":\"string\",\"description\":\"站点类型\"},\"construction\":{\"type\":\"string\",\"description\":\"建设场所\"},\"stationChargeType\":{\"type\":\"string\",\"description\":\"站点充电类型：字典cm_station_charge_type\"},\"operationStatus\":{\"type\":\"string\",\"description\":\"运营状态，字典cm_station_status\"},\"openDate\":{\"type\":\"string\",\"description\":\"投运日期\",\"mock\":{\"mock\":\"@datetime\"}},\"onlineDate\":{\"type\":\"string\",\"description\":\"上线日期\",\"mock\":{\"mock\":\"@datetime\"}},\"pileNum\":{\"type\":\"integer\",\"description\":\"充电桩数量\"},\"settlementCycle\":{\"type\":\"string\",\"description\":\"实际结算周期，字典settlement_cycle\"},\"region\":{\"type\":\"string\",\"description\":\"所属区域\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"城市\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算对象类型，字典settlement_type\"},\"lastMeterTime\":{\"type\":\"string\",\"description\":\"最近抄表时间\"},\"siteElectricity\":{\"type\":\"number\",\"description\":\"场地方电量（kWh）\"},\"siteFee\":{\"type\":\"number\",\"description\":\"场地方电费（元）\"},\"platformCharge\":{\"type\":\"number\",\"description\":\"平台充电电量（kWh）\"},\"platformFee\":{\"type\":\"number\",\"description\":\"平台充电电费（元）\"},\"electricalLoss\":{\"type\":\"number\",\"description\":\"电损=(场地方电量-充电电量)/场地方电量\"},\"feeSettle\":{\"type\":\"number\",\"description\":\"电费结算金额（元）\"},\"feeDeviation\":{\"type\":\"number\",\"description\":\"电费偏差（%）\"},\"servicePerson\":{\"type\":\"string\",\"description\":\"服务费结算人员\"},\"serviceRule\":{\"type\":\"string\",\"description\":\"服务费结算规则\"},\"serviceStartDate\":{\"type\":\"string\",\"description\":\"服务费初始结算时间\"},\"serviceLastDate\":{\"type\":\"string\",\"description\":\"服务费最近结算时间\"},\"serviceSettleAmount\":{\"type\":\"number\",\"description\":\"服务费结算金额\"},\"remark\":{\"type\":\"string\",\"description\":\"结算特殊情况备注\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationNo\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/st/stationSettlementAnalysis/save", "catid": 20005, "markdown": "", "req_headers": [{"required": "1", "_id": "68402b834ed7264fa0acad43", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749030147, "up_time": 1749035907, "__v": 0}]}]