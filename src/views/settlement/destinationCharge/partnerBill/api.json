[{"index": 0, "name": "合伙人账单", "desc": "合伙人账单", "add_time": 1749172506, "up_time": 1749172506, "list": [{"query_path": {"path": "/partner/bill/queryList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147901, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"salesContractCode\":{\"type\":\"string\",\"description\":\"销售合同编码\"},\"feeType\":{\"type\":\"string\",\"description\":\"费用类型(字典:fee_type)\"},\"settlementStatus\":{\"type\":\"string\",\"description\":\"结算状态(字典:settlement_status)\"},\"startBillingPeriod\":{\"type\":\"string\",\"description\":\"账单周期\"},\"endBillingPeriod\":{\"type\":\"string\",\"description\":\"\"},\"customerName\":{\"type\":\"string\",\"description\":\"客户名称\"},\"serviceInvoiceCategory\":{\"type\":\"string\",\"description\":\"平台服务费开票类目(字典:service_invoice_category)\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/partner/bill/queryList", "catid": 20056, "markdown": "", "req_headers": [{"required": "1", "_id": "684245114ed72662d9acada8", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"partnerBillId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"salesContractCode\":{\"type\":\"string\",\"description\":\"销售合同编码\"},\"customerName\":{\"type\":\"string\",\"description\":\"客户名称\"},\"billingPeriod\":{\"type\":\"string\",\"description\":\"账单周期\"},\"totalPeriods\":{\"type\":\"integer\",\"description\":\"总期数\"},\"paymentPeriods\":{\"type\":\"integer\",\"description\":\"支付期数\"},\"gunCount\":{\"type\":\"integer\",\"description\":\"枪数\"},\"unitPrice\":{\"type\":\"number\",\"description\":\"单价（元/枪）\"},\"amount\":{\"type\":\"number\",\"description\":\"金额（元）\"},\"feeType\":{\"type\":\"string\",\"description\":\"费用类型(字典:fee_type)\"},\"feeTypeName\":{\"type\":\"string\",\"description\":\"\"},\"settlementStatus\":{\"type\":\"string\",\"description\":\"结算状态(字典:settlement_status)\"},\"settlementStatusName\":{\"type\":\"string\",\"description\":\"\"},\"serviceInvoiceCategory\":{\"type\":\"string\",\"description\":\"平台服务费开票类目(字典:service_invoice_category)\"},\"serviceInvoiceCategoryName\":{\"type\":\"string\",\"description\":\"\"},\"serviceInvoiceRate\":{\"type\":\"string\",\"description\":\"平台服务费开票税率(字典:service_invoice_rate)\"},\"serviceInvoiceRateName\":{\"type\":\"string\",\"description\":\"\"},\"remarks\":{\"type\":\"string\",\"description\":\"备注\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"\"}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749172506, "up_time": 1749173521, "__v": 0}, {"query_path": {"path": "/partner/bill/queryCustomerNameList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147928, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"salesContractCode\":{\"type\":\"string\",\"description\":\"销售合同编码\"},\"feeType\":{\"type\":\"string\",\"description\":\"费用类型(字典:fee_type)\"},\"settlementStatus\":{\"type\":\"string\",\"description\":\"结算状态(字典:settlement_status)\"},\"startBillingPeriod\":{\"type\":\"string\",\"description\":\"账单周期\"},\"endBillingPeriod\":{\"type\":\"string\",\"description\":\"\"},\"customerName\":{\"type\":\"string\",\"description\":\"客户名称\"},\"serviceInvoiceCategory\":{\"type\":\"string\",\"description\":\"平台服务费开票类目(字典:service_invoice_category)\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询客户列表", "path": "/partner/bill/queryCustomerNameList", "catid": 20056, "markdown": "", "req_headers": [{"required": "1", "_id": "684245264ed7266fd0acadab", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749172506, "up_time": 1749173542, "__v": 0}, {"query_path": {"path": "/partner/bill/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147919, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/partner/bill/remove", "catid": 20056, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "6842451e4ed726d3d8acadaa", "name": "partnerBillId", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749172506, "up_time": 1749173534, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/partner/bill/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147937, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"salesContractCode\":{\"type\":\"string\",\"description\":\"销售合同编码\"},\"feeType\":{\"type\":\"string\",\"description\":\"费用类型(字典:fee_type)\"},\"settlementStatus\":{\"type\":\"string\",\"description\":\"结算状态(字典:settlement_status)\"},\"startBillingPeriod\":{\"type\":\"string\",\"description\":\"账单周期\"},\"endBillingPeriod\":{\"type\":\"string\",\"description\":\"\"},\"customerName\":{\"type\":\"string\",\"description\":\"客户名称\"},\"serviceInvoiceCategory\":{\"type\":\"string\",\"description\":\"平台服务费开票类目(字典:service_invoice_category)\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/partner/bill/export", "catid": 20056, "markdown": "", "req_headers": [{"required": "1", "_id": "6842452e4ed726a56eacadac", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749172506, "up_time": 1749173550, "__v": 0}, {"query_path": {"path": "/partner/bill/saveInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 147910, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"partnerBillId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"salesContractCode\":{\"type\":\"string\",\"description\":\"销售合同编码\"},\"customerName\":{\"type\":\"string\",\"description\":\"客户名称\"},\"billingPeriod\":{\"type\":\"string\",\"description\":\"账单周期\"},\"totalPeriods\":{\"type\":\"integer\",\"description\":\"总期数\"},\"paymentPeriods\":{\"type\":\"integer\",\"description\":\"支付期数\"},\"gunCount\":{\"type\":\"integer\",\"description\":\"枪数\"},\"unitPrice\":{\"type\":\"number\",\"description\":\"单价（元/枪）\"},\"amount\":{\"type\":\"number\",\"description\":\"金额（元）\"},\"feeType\":{\"type\":\"string\",\"description\":\"费用类型(字典:fee_type)\"},\"feeTypeName\":{\"type\":\"string\",\"description\":\"\"},\"settlementStatus\":{\"type\":\"string\",\"description\":\"结算状态(字典:settlement_status)\"},\"settlementStatusName\":{\"type\":\"string\",\"description\":\"\"},\"serviceInvoiceCategory\":{\"type\":\"string\",\"description\":\"平台服务费开票类目(字典:service_invoice_category)\"},\"serviceInvoiceCategoryName\":{\"type\":\"string\",\"description\":\"\"},\"serviceInvoiceRate\":{\"type\":\"string\",\"description\":\"平台服务费开票税率(字典:service_invoice_rate)\"},\"serviceInvoiceRateName\":{\"type\":\"string\",\"description\":\"\"},\"remarks\":{\"type\":\"string\",\"description\":\"备注\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/partner/bill/saveInfo", "catid": 20056, "markdown": "", "req_headers": [{"required": "1", "_id": "684245184ed726474cacada9", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749172506, "up_time": 1749173528, "__v": 0}]}]