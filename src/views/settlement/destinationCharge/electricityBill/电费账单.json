{"item": [{"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"manager\": 0,\n  \"managerName\": \"\",\n  \"province\": \"\",\n  \"city\": \"\",\n  \"settlementType\": \"\",\n  \"billStartDate\": \"\",\n  \"billEndDate\": \"\"\n}"}, "url": {"path": ["st", "chargeFeeBill", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/queryList"}}, "response": [{"name": "分页查询-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"manager\": 0, //负责人\n    \"managerName\": \"\",\n    \"province\": \"\", //省份\n    \"city\": \"\", //城市\n    \"settlementType\": \"\", //结算类型，字典settlement_type\n    \"billStartDate\": \"\", //账单开始日期\n    \"billEndDate\": \"\" //账单截止日期\n}"}, "url": {"path": ["st", "chargeFeeBill", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/queryList"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周二, 10 6月 202514:52:03 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"id\": 0, //主键\n            \"stationCode\": \"\", //站点编号\n            \"stationName\": \"\", //站点名称\n            \"province\": \"\", //省份\n            \"provinceName\": \"\",\n            \"city\": \"\", //市\n            \"cityName\": \"\",\n            \"manager\": 0, //负责人\n            \"managerName\": \"\", //负责人名称\n            \"settlementType\": \"\", //结算类型，字典settlement_type\n            \"settlementTypeName\": \"\",\n            \"billStartDate\": \"\", //账单开始日期\n            \"billEndDate\": \"\", //账单截止日期\n            \"billCycle\": \"\",\n            \"siteElectricity\": 0.0, //场地方电量（kWh）\n            \"siteFee\": 0.0, //场地方电费（元）\n            \"platformCharge\": 0.0, //平台充电电量（kWh）\n            \"platformFee\": 0.0, //平台充电电费（元）\n            \"electricalLoss\": 0.0, //电损=(场地方电量-充电电量)/场地方电量\n            \"feeSettle\": 0.0, //电费结算金额（元）\n            \"feeDeviation\": 0.0, //电费偏差（%）\n            \"remark\": \"\", //特殊情况备注\n            \"amountDifference\": 0.0, //电费结算金额-场地方电费 (元)\n            \"tenantId\": 0, //租户号\n            \"orgNo\": 0, //组织\n            \"orgNoName\": \"\",\n            \"creator\": 0, //创建人\n            \"creatorName\": \"\", //创建人名称\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\", //更新时间\n            \"belongPlace\": \"\"\n        }\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "分页查询"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"id\": 0,\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"province\": \"\",\n  \"provinceName\": \"\",\n  \"city\": \"\",\n  \"cityName\": \"\",\n  \"manager\": 0,\n  \"managerName\": \"\",\n  \"settlementType\": \"\",\n  \"settlementTypeName\": \"\",\n  \"billStartDate\": \"\",\n  \"billEndDate\": \"\",\n  \"billCycle\": \"\",\n  \"siteElectricity\": 0.0,\n  \"siteFee\": 0.0,\n  \"platformCharge\": 0.0,\n  \"platformFee\": 0.0,\n  \"electricalLoss\": 0.0,\n  \"feeSettle\": 0.0,\n  \"feeDeviation\": 0.0,\n  \"remark\": \"\",\n  \"amountDifference\": 0.0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoName\": \"\",\n  \"creator\": 0,\n  \"creatorName\": \"\",\n  \"createTime\": \"\",\n  \"updateTime\": \"\",\n  \"belongPlace\": \"\",\n  \"operatorId\": 0,\n  \"operatorName\": \"\"\n}"}, "url": {"path": ["st", "chargeFeeBill", "save"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/save"}}, "response": [{"name": "新增/编辑-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"id\": 0, //主键\n    \"stationCode\": \"\", //站点编号\n    \"stationName\": \"\", //站点名称\n    \"province\": \"\", //省份\n    \"provinceName\": \"\",\n    \"city\": \"\", //市\n    \"cityName\": \"\",\n    \"manager\": 0, //负责人\n    \"managerName\": \"\", //负责人名称\n    \"settlementType\": \"\", //结算类型，字典settlement_type\n    \"settlementTypeName\": \"\",\n    \"billStartDate\": \"\", //账单开始日期\n    \"billEndDate\": \"\", //账单截止日期\n    \"billCycle\": \"\",\n    \"siteElectricity\": 0.0, //场地方电量（kWh）\n    \"siteFee\": 0.0, //场地方电费（元）\n    \"platformCharge\": 0.0, //平台充电电量（kWh）\n    \"platformFee\": 0.0, //平台充电电费（元）\n    \"electricalLoss\": 0.0, //电损=(场地方电量-充电电量)/场地方电量\n    \"feeSettle\": 0.0, //电费结算金额（元）\n    \"feeDeviation\": 0.0, //电费偏差（%）\n    \"remark\": \"\", //特殊情况备注\n    \"amountDifference\": 0.0, //电费结算金额-场地方电费 (元)\n    \"tenantId\": 0, //租户号\n    \"orgNo\": 0, //组织\n    \"orgNoName\": \"\",\n    \"creator\": 0, //创建人\n    \"creatorName\": \"\", //创建人名称\n    \"createTime\": \"\", //创建时间\n    \"updateTime\": \"\", //更新时间\n    \"belongPlace\": \"\",\n    \"operatorId\": 0,\n    \"operatorName\": \"\"\n}"}, "url": {"path": ["st", "chargeFeeBill", "save"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/save"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周二, 10 6月 202514:52:03 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "新增/编辑"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["st", "chargeFeeBill", "remove"], "query": [{"key": "id", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/remove"}}, "response": [{"name": "删除-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["st", "chargeFeeBill", "remove"], "query": [{"key": "id", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/remove"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周二, 10 6月 202514:52:03 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "删除"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"manager\": 0,\n  \"managerName\": \"\",\n  \"province\": \"\",\n  \"city\": \"\",\n  \"settlementType\": \"\",\n  \"billStartDate\": \"\",\n  \"billEndDate\": \"\"\n}"}, "url": {"path": ["st", "chargeFeeBill", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/export"}}, "response": [{"name": "导出-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"manager\": 0, //负责人\n    \"managerName\": \"\",\n    \"province\": \"\", //省份\n    \"city\": \"\", //城市\n    \"settlementType\": \"\", //结算类型，字典settlement_type\n    \"billStartDate\": \"\", //账单开始日期\n    \"billEndDate\": \"\" //账单截止日期\n}"}, "url": {"path": ["st", "chargeFeeBill", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/export"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周二, 10 6月 202514:52:03 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": \"\",\n    \"traceId\": \"\"\n}"}], "name": "导出"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["st", "chargeFeeBill", "managerList"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/managerList"}}, "response": [{"name": "负责人 列表-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["st", "chargeFeeBill", "managerList"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/chargeFeeBill/managerList"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周二, 10 6月 202514:52:03 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        \"\"\n    ],\n    \"traceId\": \"\"\n}"}], "name": "负责人 列表"}], "info": {"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "name": "电费账单-20250610145203", "description": "电费账单"}}