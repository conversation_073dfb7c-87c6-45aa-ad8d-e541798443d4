[{"index": 0, "name": "居间商账单", "desc": "居间商账单", "add_time": 1749202473, "up_time": 1749202473, "list": [{"query_path": {"path": "/intermediary/bill/queryList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148279, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"\"},\"stationName\":{\"type\":\"string\",\"description\":\"\"},\"province\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"\"},\"billPeriodStart\":{\"type\":\"string\",\"description\":\"账单周期\"},\"billPeriodEnd\":{\"type\":\"string\",\"description\":\"\"},\"responsiblePerson\":{\"type\":\"string\",\"description\":\"负责人\"},\"intermediaryChannel\":{\"type\":\"string\",\"description\":\"居间渠道商\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/intermediary/bill/queryList", "catid": 20074, "markdown": "", "req_headers": [{"required": "1", "_id": "684641a74ed72655daacae4d", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"intermediaryId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"responsiblePerson\":{\"type\":\"string\",\"description\":\"负责人\"},\"billPeriodStart\":{\"type\":\"string\",\"description\":\"账单周期开始日期\"},\"billPeriodEnd\":{\"type\":\"string\",\"description\":\"账单周期结束日期\"},\"billPeriod\":{\"type\":\"string\",\"description\":\"\"},\"intermediaryChannel\":{\"type\":\"string\",\"description\":\"居间渠道商(字典intermediary_channel)\"},\"intermediaryChannelName\":{\"type\":\"string\",\"description\":\"\"},\"profitSharingRatio\":{\"type\":\"number\",\"description\":\"分润比例（百分比）\"},\"settlementAmount\":{\"type\":\"number\",\"description\":\"结算金额（元）\"},\"specialNotes\":{\"type\":\"string\",\"description\":\"特殊情况备注（最多500个字符）\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202473, "up_time": 1749434791, "__v": 0}, {"query_path": {"path": "/intermediary/bill/queryResponsiblePersonList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148306, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"\"},\"stationName\":{\"type\":\"string\",\"description\":\"\"},\"province\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"\"},\"billPeriodStart\":{\"type\":\"string\",\"description\":\"账单周期\"},\"billPeriodEnd\":{\"type\":\"string\",\"description\":\"\"},\"responsiblePerson\":{\"type\":\"string\",\"description\":\"负责人\"},\"intermediaryChannel\":{\"type\":\"string\",\"description\":\"居间渠道商\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询负责人列表", "path": "/intermediary/bill/queryResponsiblePersonList", "catid": 20074, "markdown": "", "req_headers": [{"required": "1", "_id": "684641bb4ed7266470acae50", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202473, "up_time": 1749434811, "__v": 0}, {"query_path": {"path": "/intermediary/bill/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148297, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/intermediary/bill/remove", "catid": 20074, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "684641b54ed7267690acae4f", "name": "intermediaryId", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202473, "up_time": 1749434805, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/intermediary/bill/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148315, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"\"},\"stationName\":{\"type\":\"string\",\"description\":\"\"},\"province\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"\"},\"billPeriodStart\":{\"type\":\"string\",\"description\":\"账单周期\"},\"billPeriodEnd\":{\"type\":\"string\",\"description\":\"\"},\"responsiblePerson\":{\"type\":\"string\",\"description\":\"负责人\"},\"intermediaryChannel\":{\"type\":\"string\",\"description\":\"居间渠道商\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/intermediary/bill/export", "catid": 20074, "markdown": "", "req_headers": [{"required": "1", "_id": "684641c34ed726145facae51", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202474, "up_time": 1749434819, "__v": 0}, {"query_path": {"path": "/intermediary/bill/import", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148324, "req_body_type": "form", "res_body_type": "json", "title": "批量导入", "path": "/intermediary/bill/import", "catid": 20074, "markdown": "", "req_headers": [{"required": "1", "_id": "684641ca4ed7260840acae52", "name": "Content-Type", "value": "multipart/form-data", "example": "multipart/form-data"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202474, "up_time": 1749434826, "__v": 0}, {"query_path": {"path": "/intermediary/bill/saveInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148288, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"intermediaryId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"responsiblePerson\":{\"type\":\"string\",\"description\":\"负责人\"},\"billPeriodStart\":{\"type\":\"string\",\"description\":\"账单周期开始日期\"},\"billPeriodEnd\":{\"type\":\"string\",\"description\":\"账单周期结束日期\"},\"billPeriod\":{\"type\":\"string\",\"description\":\"\"},\"intermediaryChannel\":{\"type\":\"string\",\"description\":\"居间渠道商(字典intermediary_channel)\"},\"intermediaryChannelName\":{\"type\":\"string\",\"description\":\"\"},\"profitSharingRatio\":{\"type\":\"number\",\"description\":\"分润比例（百分比）\"},\"settlementAmount\":{\"type\":\"number\",\"description\":\"结算金额（元）\"},\"specialNotes\":{\"type\":\"string\",\"description\":\"特殊情况备注（最多500个字符）\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/intermediary/bill/saveInfo", "catid": 20074, "markdown": "", "req_headers": [{"required": "1", "_id": "684641af4ed7266ff8acae4e", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1749202473, "up_time": 1749434799, "__v": 0}]}]