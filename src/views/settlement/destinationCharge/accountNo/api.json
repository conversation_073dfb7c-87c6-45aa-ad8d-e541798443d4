[{"index": 0, "name": "户号", "desc": "户号", "add_time": **********, "up_time": **********, "list": [{"query_path": {"path": "/account/queryList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148549, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"startOnlineDate\":{\"type\":\"string\",\"description\":\"上线时间\"},\"endOnlineDate\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"accountSubject\":{\"type\":\"string\",\"description\":\"户号主体\"},\"reportForm\":{\"type\":\"string\",\"description\":\"报电形式\"},\"powerSupplyUnit\":{\"type\":\"string\",\"description\":\"供电单位\"},\"fieldName\":{\"type\":\"string\",\"description\":\"查询字段\"},\"fieldValue\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/account/queryList", "catid": 20089, "markdown": "", "req_headers": [{"required": "1", "_id": "684789ee4ed7266086acaea8", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"accountId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"省份名称\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"onlineDate\":{\"type\":\"string\",\"description\":\"上线时间\"},\"accountNo\":{\"type\":\"string\",\"description\":\"电费户号\"},\"accountSubject\":{\"type\":\"string\",\"description\":\"户号主体\"},\"powerSupplyUnit\":{\"type\":\"string\",\"description\":\"供电单位\"},\"electricAddress\":{\"type\":\"string\",\"description\":\"用电地址\"},\"reportForm\":{\"type\":\"string\",\"description\":\"报电形式(字典：report_form)\"},\"reportFormName\":{\"type\":\"string\",\"description\":\"\"},\"remark\":{\"type\":\"string\",\"description\":\"备注：500个字符以内\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"belongPlace\":{\"type\":\"string\",\"description\":\"\"}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/account/queryDistinctValue", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148576, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"startOnlineDate\":{\"type\":\"string\",\"description\":\"上线时间\"},\"endOnlineDate\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"accountSubject\":{\"type\":\"string\",\"description\":\"户号主体\"},\"reportForm\":{\"type\":\"string\",\"description\":\"报电形式\"},\"powerSupplyUnit\":{\"type\":\"string\",\"description\":\"供电单位\"},\"fieldName\":{\"type\":\"string\",\"description\":\"查询字段\"},\"fieldValue\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询字段(account_no、account_subject、power_supply_unit)", "path": "/account/queryDistinctValue", "catid": 20089, "markdown": "", "req_headers": [{"required": "1", "_id": "68478a034ed726647facaeab", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/account/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148567, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/account/remove", "catid": 20089, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "684789fd4ed7262890acaeaa", "name": "accountId", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/account/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148585, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"startOnlineDate\":{\"type\":\"string\",\"description\":\"上线时间\"},\"endOnlineDate\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"accountSubject\":{\"type\":\"string\",\"description\":\"户号主体\"},\"reportForm\":{\"type\":\"string\",\"description\":\"报电形式\"},\"powerSupplyUnit\":{\"type\":\"string\",\"description\":\"供电单位\"},\"fieldName\":{\"type\":\"string\",\"description\":\"查询字段\"},\"fieldValue\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/account/export", "catid": 20089, "markdown": "", "req_headers": [{"required": "1", "_id": "68478a0b4ed726ff94acaeac", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/account/saveInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148558, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"accountId\":{\"type\":\"integer\",\"description\":\"主键ID\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"省份名称\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"onlineDate\":{\"type\":\"string\",\"description\":\"上线时间\"},\"accountNo\":{\"type\":\"string\",\"description\":\"电费户号\"},\"accountSubject\":{\"type\":\"string\",\"description\":\"户号主体\"},\"powerSupplyUnit\":{\"type\":\"string\",\"description\":\"供电单位\"},\"electricAddress\":{\"type\":\"string\",\"description\":\"用电地址\"},\"reportForm\":{\"type\":\"string\",\"description\":\"报电形式(字典：report_form)\"},\"reportFormName\":{\"type\":\"string\",\"description\":\"\"},\"remark\":{\"type\":\"string\",\"description\":\"备注：500个字符以内\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"belongPlace\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/account/saveInfo", "catid": 20089, "markdown": "", "req_headers": [{"required": "1", "_id": "684789f74ed726021eacaea9", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}]}]