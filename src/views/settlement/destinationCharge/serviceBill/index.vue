<!-- 服务商账单 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <ServiceFeeBillPage
      v-show="tabActiveTab === 'serviceFeeBill'"
      ref="serviceFeeBill"
      :searchParams="searchParams"
    ></ServiceFeeBillPage>
    <DailyBillPage
      v-show="tabActiveTab === 'dailyBill'"
      ref="dailyBill"
      @jump="handleJump"
    ></DailyBillPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import ServiceFeeBillPage from "./serviceFeeBill.vue";
import DailyBillPage from "./dailyBill.vue";

export default {
  name: "destinationServiceBill",
  components: {
    ServiceFeeBillPage,
    DailyBillPage,
  },
  data() {
    return {
      tabActiveTab: "serviceFeeBill",
      topTabDict: [
        {
          value: "serviceFeeBill",
          label: "服务费账单",
          show: () => {
            return this.checkPermission(["serviceBill:serviceFeeBill:list"]);
          },
        },
        {
          value: "dailyBill",
          label: "服务费日账单",
          show: () => {
            return this.checkPermission(["serviceBill:dailyBill:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // 可以在这里处理标签页跳转逻辑
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
