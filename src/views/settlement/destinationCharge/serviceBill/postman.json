{"item": [{"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"settlementDate\": \"\",\n  \"settlementStartDate\": \"\",\n  \"settlementEndDate\": \"\",\n  \"settlementParty\": \"\",\n  \"settlementStatus\": \"\"\n}"}, "url": {"path": ["st", "serviceFeeDailyBill", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/serviceFeeDailyBill/queryList"}}, "response": [{"name": "分页查询-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"settlementDate\": \"\", //结算日期\n    \"settlementStartDate\": \"\",\n    \"settlementEndDate\": \"\",\n    \"settlementParty\": \"\", //结算方\n    \"settlementStatus\": \"\" //结算状态\n}"}, "url": {"path": ["st", "serviceFeeDailyBill", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/serviceFeeDailyBill/queryList"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周一, 09 6月 202516:22:51 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"id\": 0, //主键\n            \"businessDate\": \"\", //业务日期\n            \"settlementNo\": \"\", //结算单号\n            \"settlementDate\": \"\", //结算日期\n            \"settlementParty\": \"\", //结算方\n            \"settlementFee\": 0.0, //结算费用(元)\n            \"settlementOrderCount\": 0, //结算订单数\n            \"systemClear\": \"\", //系统清分\n            \"businessOrgName\": \"\", //业务组织名称\n            \"orderAmount\": 0.0, //订单金额(元)\n            \"channelProfit\": 0.0, //渠道分润(元)\n            \"paymentFee\": 0.0, //支付手续费(元)\n            \"shouldSettlementAmount\": 0.0, //应结算金额(元)\n            \"settledAmount\": 0.0, //已结算金额(元)\n            \"settlementMethod\": \"\", //结算方式\n            \"clearRule\": \"\", //清分规则\n            \"settlementStatus\": \"\", //结算状态\n            \"generationTime\": \"\", //生成时间\n            \"surcharge\": 0.0, //附加费(元)\n            \"serviceFee\": 0.0, //服务费(元)\n            \"platformStationFee\": 0.0, //平台站点活动费(结算)(元)\n            \"channelStationFee\": 0.0, //渠道站点活动费(元)\n            \"platformSubsidyAmount\": 0.0, //平台补贴金额(元)\n            \"platformActivityAmount\": 0.0, //平台活动金额(元)\n            \"principalSettlement\": 0.0, //本金(结算)(元)\n            \"chargeQuantity\": 0.0, //充电量(kWh)\n            \"platformChannelFee\": 0.0, //平台渠道活动(结算)(元)\n            \"chargeFee\": 0.0, //充电费(元)\n            \"platformSubsidyAmountSettle\": 0.0, //平台补贴金额(结算)(元)\n            \"tenantId\": 0, //租户号\n            \"creator\": 0, //创建人\n            \"creatorName\": \"\", //创建人名称\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\" //更新时间\n        }\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "分页查询"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"settlementDate\": \"\",\n  \"settlementStartDate\": \"\",\n  \"settlementEndDate\": \"\",\n  \"settlementParty\": \"\",\n  \"settlementStatus\": \"\"\n}"}, "url": {"path": ["st", "serviceFeeDailyBill", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/serviceFeeDailyBill/export"}}, "response": [{"name": "导出-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"settlementDate\": \"\", //结算日期\n    \"settlementStartDate\": \"\",\n    \"settlementEndDate\": \"\",\n    \"settlementParty\": \"\", //结算方\n    \"settlementStatus\": \"\" //结算状态\n}"}, "url": {"path": ["st", "serviceFeeDailyBill", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/st/serviceFeeDailyBill/export"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周一, 09 6月 202516:22:51 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": \"\",\n    \"traceId\": \"\"\n}"}], "name": "导出"}], "info": {"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "name": "目的地充电-服务费日账单-20250609162251", "description": "目的地充电-服务费日账单"}}