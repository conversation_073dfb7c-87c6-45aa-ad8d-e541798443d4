[{"index": 0, "name": "目的地充电-服务商账单", "desc": "目的地充电-服务商账单", "add_time": **********, "up_time": **********, "list": [{"query_path": {"path": "/st/serviceProviderBill/queryList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148405, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"负责人名称\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算类型，字典settlement_type\"},\"billStartDate\":{\"type\":\"string\",\"description\":\"账单开始日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billEndDate\":{\"type\":\"string\",\"description\":\"账单截止日期\",\"mock\":{\"mock\":\"@datetime\"}},\"settleProgress\":{\"type\":\"string\",\"description\":\"结算进度，字典settle_progress\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/st/serviceProviderBill/queryList", "catid": 20080, "markdown": "", "req_headers": [{"required": "1", "_id": "684641d44ed7262d00acae53", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"主键\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"负责人名称\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算类型，字典settlement_type\"},\"settlementTypeName\":{\"type\":\"string\",\"description\":\"\"},\"billStartDate\":{\"type\":\"string\",\"description\":\"账单开始日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billEndDate\":{\"type\":\"string\",\"description\":\"账单截止日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billCycle\":{\"type\":\"string\",\"description\":\"账单周期\"},\"serviceFeeAmount\":{\"type\":\"number\",\"description\":\"服务费应结算金额（元）\"},\"serviceFeeStandard\":{\"type\":\"number\",\"description\":\"服务费计算标准（%）\"},\"serviceFeeRealAmount\":{\"type\":\"number\",\"description\":\"服务费实际结算金额（元）\"},\"settleProgress\":{\"type\":\"string\",\"description\":\"结算进度，字典settle_progress\"},\"settleProgressName\":{\"type\":\"string\",\"description\":\"\"},\"settleRemark\":{\"type\":\"string\",\"description\":\"结算备注\"},\"serviceFeeSettleBy\":{\"type\":\"string\",\"description\":\"服务费结算人员\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"belongPlace\":{\"type\":\"string\",\"description\":\"\"}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/st/serviceProviderBill/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148423, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/st/serviceProviderBill/remove", "catid": 20080, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "684641e34ed7263c8dacae55", "name": "id", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/st/serviceProviderBill/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148432, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"负责人名称\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算类型，字典settlement_type\"},\"billStartDate\":{\"type\":\"string\",\"description\":\"账单开始日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billEndDate\":{\"type\":\"string\",\"description\":\"账单截止日期\",\"mock\":{\"mock\":\"@datetime\"}},\"settleProgress\":{\"type\":\"string\",\"description\":\"结算进度，字典settle_progress\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/st/serviceProviderBill/export", "catid": 20080, "markdown": "", "req_headers": [{"required": "1", "_id": "684641e94ed726e9e3acae56", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/st/serviceProviderBill/save", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148414, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"主键\"},\"stationCode\":{\"type\":\"string\",\"description\":\"站点编号\"},\"stationName\":{\"type\":\"string\",\"description\":\"站点名称\"},\"province\":{\"type\":\"string\",\"description\":\"省份\"},\"provinceName\":{\"type\":\"string\",\"description\":\"\"},\"city\":{\"type\":\"string\",\"description\":\"市\"},\"cityName\":{\"type\":\"string\",\"description\":\"\"},\"manager\":{\"type\":\"integer\",\"description\":\"负责人\"},\"managerName\":{\"type\":\"string\",\"description\":\"负责人名称\"},\"settlementType\":{\"type\":\"string\",\"description\":\"结算类型，字典settlement_type\"},\"settlementTypeName\":{\"type\":\"string\",\"description\":\"\"},\"billStartDate\":{\"type\":\"string\",\"description\":\"账单开始日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billEndDate\":{\"type\":\"string\",\"description\":\"账单截止日期\",\"mock\":{\"mock\":\"@datetime\"}},\"billCycle\":{\"type\":\"string\",\"description\":\"账单周期\"},\"serviceFeeAmount\":{\"type\":\"number\",\"description\":\"服务费应结算金额（元）\"},\"serviceFeeStandard\":{\"type\":\"number\",\"description\":\"服务费计算标准（%）\"},\"serviceFeeRealAmount\":{\"type\":\"number\",\"description\":\"服务费实际结算金额（元）\"},\"settleProgress\":{\"type\":\"string\",\"description\":\"结算进度，字典settle_progress\"},\"settleProgressName\":{\"type\":\"string\",\"description\":\"\"},\"settleRemark\":{\"type\":\"string\",\"description\":\"结算备注\"},\"serviceFeeSettleBy\":{\"type\":\"string\",\"description\":\"服务费结算人员\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"组织\"},\"orgNoName\":{\"type\":\"string\",\"description\":\"\"},\"creator\":{\"type\":\"integer\",\"description\":\"创建人\"},\"creatorName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"belongPlace\":{\"type\":\"string\",\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/st/serviceProviderBill/save", "catid": 20080, "markdown": "", "req_headers": [{"required": "1", "_id": "684641dd4ed72681eeacae54", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/st/serviceProviderBill/settlePersonList", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 148441, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "服务费结算人员 列表", "path": "/st/serviceProviderBill/settlePersonList", "catid": 20080, "markdown": "", "req_headers": [], "req_query": [{"required": "0", "_id": "684641f04ed7265eeaacae57", "name": "name", "desc": ""}], "desc": "", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": **********, "up_time": **********, "req_body_form": [], "__v": 0}]}]