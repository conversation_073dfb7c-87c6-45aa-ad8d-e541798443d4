<!-- 服务费日账单 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceBill:dailyBill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <!-- <el-select
            v-model="selectPage"
            size="mini"
            style="margin-right: 10px;width: 86px;"
          >
            <el-option label="当前页" value="1"></el-option>
            <el-option label="全部页" value="2"></el-option>
          </el-select> -->
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['serviceBill:dailyBill:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/destinationCharge/serviceBill/dailyBill.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
export default {
  name: "dailyBill",
  mixins: [exportMixin],
  components: {
    BatchUpload,
  },
  data() {
    return {
      workLoading: false,
      uploadObj: {
        api: "/export/report/importServiceFeeDailyBill",
        url: "/charging-maintenance-ui/static/服务费日账单导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      settlementStatusOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取结算状态字典
    this.getDicts("st_bd_bill_status").then((response) => {
      this.settlementStatusOptions = response.data;
    });
  },
  methods: {
    checkPermission,

    handleBatchAdd() {},

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.exportData, params);
    },

    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.remove(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "onlineRangeTime",
          title: "结算周期",
          startFieldName: "settlementStartDate",
          endFieldName: "settlementEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.queryList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "businessDate",
          title: "业务日期",
          width: 120,
        },
        {
          field: "settlementNo",
          title: "结算单号",
          width: 150,
        },
        {
          field: "settlementDate",
          title: "结算日期",
          width: 120,
        },
        {
          field: "settlementParty",
          title: "结算方",
          width: 120,
        },
        {
          field: "settlementFee",
          title: "结算费用（元）",
          width: 120,
        },
        {
          field: "settlementOrderCount",
          title: "结算订单数",
          width: 100,
        },
        {
          field: "systemClear",
          title: "系统清分",
          width: 100,
        },
        {
          field: "businessOrgName",
          title: "业务组织名称",
          width: 100,
        },
        {
          field: "orderAmount",
          title: "订单金额（元）",
          width: 100,
        },
        {
          field: "channelProfit",
          title: "渠道分润（元）",
          width: 100,
        },
        {
          field: "paymentFee",
          title: "支付手续费（元）",
          width: 150,
        },
        {
          field: "shouldSettlementAmount",
          title: "应结算金额（元）",
          width: 100,
        },
        {
          field: "settledAmount",
          title: "已结算金额（元）",
          width: 100,
        },
        {
          field: "settlementMethod",
          title: "结算方式",
          width: 100,
        },
        {
          field: "clearRule",
          title: "清分规则",
          width: 100,
        },
        {
          field: "settlementStatusName",
          title: "结算状态",
          width: 100,
        },
        {
          field: "generationTime",
          title: "生成时间",
          width: 100,
        },
        {
          field: "surcharge",
          title: "附加费（元）",
          width: 100,
        },
        {
          field: "serviceFee",
          title: "服务费（元）",
          width: 100,
        },
        {
          field: "platformStationFee",
          title: "平台站点活动费（结算）（元）",
          width: 100,
        },
        {
          field: "channelStationFee",
          title: "渠道站点活动费（元）",
          width: 100,
        },
        {
          field: "platformSubsidyAmount",
          title: "平台补贴金额（元）",
          width: 100,
        },
        {
          field: "platformActivityAmount",
          title: "平台活动金额（元）",
          width: 100,
        },
        {
          field: "principalSettlement",
          title: "本金（结算）（元）",
          width: 100,
        },
        {
          field: "chargeQuantity",
          title: "充电量（kWh）",
          width: 100,
        },
        {
          field: "platformChannelFee",
          title: "平台渠道活动（结算）（元）",
          width: 100,
        },
        {
          field: "chargeFee",
          title: "充电费（元）",
          width: 100,
        },
        {
          field: "platformSubsidyAmountSettle",
          title: "平台补贴金额（结算）（元）",
          width: 100,
        },
        {
          field: "creatorName",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "settlementParty",
            element: "el-input",
            title: "结算方",
          },
          {
            field: "settlementStatus",
            element: "el-select",
            title: "结算状态",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "onlineRangeTime",
            title: "结算周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [],
      };
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        // editBtn: checkPermission(["serviceBill:serviceFeeBill:edit"]),
        // delBtn: checkPermission(["serviceBill:serviceFeeBill:delete"]),
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
