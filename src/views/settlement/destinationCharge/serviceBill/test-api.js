// 测试服务费日账单API接口
import api from '@/api/settlement/destinationCharge/serviceBill/dailyBill.js';

// 测试查询接口
async function testQueryList() {
  console.log('=== 测试查询接口 ===');
  try {
    const params = {
      pageNum: 1,
      pageSize: 10,
      operatorName: '测试组织',
      settlementStatus: '1',
      settlementStartDate: '2024-01-01',
      settlementEndDate: '2024-01-31'
    };
    
    const result = await api.queryList(params);
    console.log('查询结果:', result);
    
    // 验证返回数据结构
    if (result.data && Array.isArray(result.data)) {
      console.log('✓ 返回数据格式正确');
      if (result.data.length > 0) {
        const firstItem = result.data[0];
        console.log('第一条数据字段:', Object.keys(firstItem));
        
        // 验证关键字段是否存在
        const requiredFields = [
          'businessDate', 'settlementNo', 'settlementDate', 
          'settlementParty', 'settlementFee', 'settlementOrderCount'
        ];
        
        const missingFields = requiredFields.filter(field => !(field in firstItem));
        if (missingFields.length === 0) {
          console.log('✓ 所有必需字段都存在');
        } else {
          console.log('✗ 缺少字段:', missingFields);
        }
      }
    } else {
      console.log('✗ 返回数据格式错误');
    }
  } catch (error) {
    console.error('查询接口测试失败:', error);
  }
}

// 测试导出接口
async function testExport() {
  console.log('=== 测试导出接口 ===');
  try {
    const params = {
      operatorName: '测试组织',
      settlementStatus: '1'
    };
    
    const result = await api.exportData(params);
    console.log('导出结果:', result);
    
    if (result.success) {
      console.log('✓ 导出接口调用成功');
    } else {
      console.log('✗ 导出接口调用失败:', result.message);
    }
  } catch (error) {
    console.error('导出接口测试失败:', error);
  }
}

// 测试更新接口
async function testUpdate() {
  console.log('=== 测试更新接口 ===');
  try {
    const params = {
      id: 1,
      businessDate: '2024-01-01',
      settlementParty: '测试结算方',
      settlementFee: 1000.00,
      settlementStatus: '1'
    };
    
    const result = await api.update(params);
    console.log('更新结果:', result);
    
    if (result.success) {
      console.log('✓ 更新接口调用成功');
    } else {
      console.log('✗ 更新接口调用失败:', result.message);
    }
  } catch (error) {
    console.error('更新接口测试失败:', error);
  }
}

// 测试删除接口
async function testRemove() {
  console.log('=== 测试删除接口 ===');
  try {
    const params = { id: 999 }; // 使用不存在的ID进行测试
    
    const result = await api.remove(params);
    console.log('删除结果:', result);
    
    if (result.success) {
      console.log('✓ 删除接口调用成功');
    } else {
      console.log('✗ 删除接口调用失败:', result.message);
    }
  } catch (error) {
    console.error('删除接口测试失败:', error);
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log('开始API接口测试...');
  
  await testQueryList();
  await testExport();
  await testUpdate();
  await testRemove();
  
  console.log('API接口测试完成');
}

// 字段映射验证
export function validateFieldMapping() {
  console.log('=== 验证字段映射 ===');
  
  // 接口返回字段
  const apiFields = [
    'id', 'businessDate', 'settlementNo', 'settlementDate', 'settlementParty',
    'settlementFee', 'settlementOrderCount', 'systemClear', 'businessOrgName',
    'orderAmount', 'channelProfit', 'paymentFee', 'shouldSettlementAmount',
    'settledAmount', 'settlementMethod', 'clearRule', 'settlementStatus',
    'generationTime', 'surcharge', 'serviceFee', 'platformStationFee',
    'channelStationFee', 'platformSubsidyAmount', 'platformActivityAmount',
    'principalSettlement', 'chargeQuantity', 'platformChannelFee',
    'chargeFee', 'platformSubsidyAmountSettle', 'tenantId', 'creator',
    'creatorName', 'createTime', 'updateTime'
  ];
  
  // 页面表格列字段
  const tableFields = [
    'businessDate', 'settlementNo', 'settlementDate', 'settlementParty',
    'settlementFee', 'settlementOrderCount', 'systemClear', 'businessOrgName',
    'orderAmount', 'channelProfit', 'paymentFee', 'shouldSettlementAmount',
    'settledAmount', 'settlementMethod', 'clearRule', 'settlementStatus',
    'generationTime', 'surcharge', 'serviceFee', 'platformStationFee',
    'channelStationFee', 'platformSubsidyAmount', 'platformActivityAmount',
    'principalSettlement', 'chargeQuantity', 'platformChannelFee',
    'chargeFee', 'platformSubsidyAmountSettle', 'creatorName', 'createTime'
  ];
  
  // 检查字段映射
  const unmappedFields = tableFields.filter(field => !apiFields.includes(field));
  const extraApiFields = apiFields.filter(field => !tableFields.includes(field));
  
  console.log('页面表格字段数量:', tableFields.length);
  console.log('API返回字段数量:', apiFields.length);
  
  if (unmappedFields.length === 0) {
    console.log('✓ 所有表格字段都有对应的API字段');
  } else {
    console.log('✗ 未映射的表格字段:', unmappedFields);
  }
  
  if (extraApiFields.length > 0) {
    console.log('ℹ 额外的API字段（未在表格中显示）:', extraApiFields);
  }
  
  // 检查筛选条件字段映射
  const filterFields = ['operatorName', 'settlementStatus', 'settlementStartDate', 'settlementEndDate'];
  const apiQueryFields = [
    'pageNum', 'pageSize', 'tenantId', 'orgNo', 'orgNoList', 'operatorId',
    'operatorName', 'settlementDate', 'settlementStartDate', 'settlementEndDate',
    'settlementParty', 'settlementStatus'
  ];
  
  const unmappedFilterFields = filterFields.filter(field => !apiQueryFields.includes(field));
  
  if (unmappedFilterFields.length === 0) {
    console.log('✓ 所有筛选条件字段都有对应的API参数');
  } else {
    console.log('✗ 未映射的筛选条件字段:', unmappedFilterFields);
  }
  
  console.log('字段映射验证完成');
}

export default {
  runAllTests,
  validateFieldMapping,
  testQueryList,
  testExport,
  testUpdate,
  testRemove
};
