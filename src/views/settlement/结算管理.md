---
title: 维保通
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 维保通

Base URLs:

# Authentication

# 银行流水控制层

## POST 分页查询

POST /st/lifePay/bankFlow/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "remark": "string",
  "counterpartyBankName": "string",
  "matchedBillMonthStart": "string",
  "matchedBillMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BankFlowQueryDTO](#schemabankflowquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "industry": "",
      "isNucleated": "",
      "nucleationSubmitTime": "",
      "reasonClassification": "",
      "subReasonClassification": "",
      "cleanupClassification": "",
      "remark": "",
      "matchedBillMonth": "",
      "unNucleatedAmount": 0,
      "ourAccountNumber": "",
      "bankTransactionDate": "",
      "currency": "",
      "debitAmount": 0,
      "creditAmount": 0,
      "treasuryFlowType": "",
      "bankRemark": "",
      "counterpartyBankName": "",
      "openingBank": "",
      "counterpartyAccountNumber": "",
      "supplementaryNotes": "",
      "flowStatus": "",
      "flowDestination": "",
      "flowDestinationAccount": "",
      "bankFlowNumber": "",
      "createBy": "",
      "createTime": "",
      "tenantId": 0,
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultBankFlowVO](#schemapaginationresultbankflowvo)|

## POST 导出Excel

POST /st/lifePay/bankFlow/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "remark": "string",
  "counterpartyBankName": "string",
  "matchedBillMonthStart": "string",
  "matchedBillMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BankFlowQueryDTO](#schemabankflowquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/bankFlow/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/bankFlow/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 支付宝与邦道账单控制层

## POST 分页查询

POST /st/lifePay/alipayBd/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "settlementAccountPid": "string",
  "diffResult": "string",
  "settlementAccountName": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» pageNum|body|integer| 否 |none|
|» pageSize|body|integer| 否 |none|
|» tenantId|body|integer(int64)| 否 |none|
|» orgNo|body|integer(int64)| 否 |none|
|» orgNoList|body|[integer]| 否 |none|
|» operatorId|body|integer(int64)| 否 |none|
|» operatorName|body|string| 否 |none|
|» idList|body|[integer]| 否 |主键id|
|» settlementAccountPid|body|string| 否 |结算商户pid|
|» diffResult|body|string| 否 |比对结果 st_diff_result 01:一致 02:不一致 03:未匹配到数据|
|» settlementAccountName|body|string| 否 |结算账号名称|
|» billMonthStart|body|string| 否 |账单月份|
|» billMonthEnd|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "policyNo": "",
      "policyName": "",
      "bizCycle": "",
      "incentiveObjectPid": "",
      "incentiveObjectName": "",
      "contractAccountPid": "",
      "contractAccountName": "",
      "contractMerchantMid": "",
      "contractMerchantName": "",
      "settlementAccountPid": "",
      "settlementAccountName": "",
      "settlementMerchantMid": "",
      "settlementMerchantName": "",
      "payMerchantId": "",
      "payMerchantName": "",
      "productCode": "",
      "billMonth": "",
      "providerPid": "",
      "writeOffMonth": "",
      "writeOffAmount": 0,
      "settlementAmount": 0,
      "settlement": "",
      "settlementRemark": "",
      "bankRemark": "",
      "bankMonth": "",
      "bankAmount": 0,
      "diffResult": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultAlipayBdBillVO](#schemapaginationresultalipaybdbillvo)|

## POST 导出Excel

POST /st/lifePay/alipayBd/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "settlementAccountPid": "string",
  "diffResult": "string",
  "settlementAccountName": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayBdBillQueryDTO](#schemaalipaybdbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/alipayBd/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 与银行流水核对

POST /st/lifePay/alipayBd/checkWithBank

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "settlementAccountPid": "string",
  "diffResult": "string",
  "settlementAccountName": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayBdBillQueryDTO](#schemaalipaybdbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultAlipayBdBillCheckBankVO](#schemarestresultalipaybdbillcheckbankvo)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/alipayBd/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 支付宝与机构账单明细数据控制层

## POST 分页查询

POST /st/lifePay/aliPayOrg/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billNo": "string",
  "billStatus": "string",
  "settleStatus": "string",
  "billCycleStart": "string",
  "billCycleEnd": "string",
  "pid": "string",
  "merchantName": "string",
  "pointType": "string",
  "alipayAccount": "string",
  "billingOrgCode": "string",
  "billingOrgName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgBillQueryDTO](#schemaalipayorgbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "billNo": "",
      "ou": "",
      "mid": "",
      "pid": "",
      "merchantName": "",
      "billCycle": "",
      "productCode": "",
      "productName": "",
      "billingItem": "",
      "contractNo": "",
      "billingAmount": 0,
      "originAmount": 0,
      "unpaidAmount": 0,
      "unbilledAmount": 0,
      "unsureAmount": 0,
      "unsettledAmount": 0,
      "billStatus": "",
      "alipayAccount": "",
      "pointType": "",
      "adjustAmount": 0,
      "shouldPayAmount": 0,
      "settleStatus": "",
      "billingCount": 0,
      "billingOrgCode": "",
      "billingOrgName": "",
      "contractPid": "",
      "contractName": "",
      "contractEmail": "",
      "createBy": "",
      "createTime": "",
      "tenantId": 0,
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultAlipayOrgBillVO](#schemapaginationresultalipayorgbillvo)|

## POST 导出Excel

POST /st/lifePay/aliPayOrg/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billNo": "string",
  "billStatus": "string",
  "settleStatus": "string",
  "billCycleStart": "string",
  "billCycleEnd": "string",
  "pid": "string",
  "merchantName": "string",
  "pointType": "string",
  "alipayAccount": "string",
  "billingOrgCode": "string",
  "billingOrgName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgBillQueryDTO](#schemaalipayorgbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/aliPayOrg/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/aliPayOrg/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 邦道与服务商账单

## POST 分页查询

POST /st/lifePay/bdProvider/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "partnerName": "string",
  "status": "string",
  "settlementDayStart": "string",
  "settlementDayEnd": "string",
  "instituteName": "string",
  "mid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BdProviderBillQueryDTO](#schemabdproviderbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "partnerName": "",
      "instituteName": "",
      "mid": "",
      "accountDay": "",
      "settlementDay": "",
      "onlineDay": "",
      "aliShouldPayAmount": 0,
      "aliActualPayAmount": 0,
      "spShouldPayAmount": 0,
      "spActualPayAmount": 0,
      "rebate": 0,
      "status": "",
      "updateBy": "",
      "updateTime": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultBdProviderBillVO](#schemapaginationresultbdproviderbillvo)|

## POST 导出Excel

POST /st/lifePay/bdProvider/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "partnerName": "string",
  "status": "string",
  "settlementDayStart": "string",
  "settlementDayEnd": "string",
  "instituteName": "string",
  "mid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BdProviderBillQueryDTO](#schemabdproviderbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/bdProvider/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量标记结算状态

POST /st/lifePay/bdProvider/batchMark

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "partnerName": "string",
  "status": "string",
  "settlementDayStart": "string",
  "settlementDayEnd": "string",
  "instituteName": "string",
  "mid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[BdProviderBillQueryDTO](#schemabdproviderbillquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/bdProvider/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 机构信息控制层

## POST 分页查询

POST /st/lifePay/institInfo/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "institutionName": "string",
  "businessType": "string",
  "onlineStatus": "string",
  "serviceProvider": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[InstitutionInfoQueryDTO](#schemainstitutioninfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {}
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultInstitutionInfoVO](#schemapaginationresultinstitutioninfovo)|

## POST 导出Excel

POST /st/lifePay/institInfo/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "institutionName": "string",
  "businessType": "string",
  "onlineStatus": "string",
  "serviceProvider": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[InstitutionInfoQueryDTO](#schemainstitutioninfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/institInfo/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/lifePay/institInfo/edit

> Body 请求参数

```json
{
  "id": 0,
  "settlementRequirements": "string",
  "collectionRequirements": "string",
  "handlingFee": "string",
  "handlingFeeUrl": "string",
  "collectionReceipt": "string",
  "collectionReceiptUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[InstitutionInfoEditDTO](#schemainstitutioninfoeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 上传文件

POST /st/lifePay/institInfo/uploadFile

> Body 请求参数

```json
{
  "id": 0,
  "settlementRequirements": "string",
  "collectionRequirements": "string",
  "handlingFee": "string",
  "handlingFeeUrl": "string",
  "collectionReceipt": "string",
  "collectionReceiptUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[InstitutionInfoEditDTO](#schemainstitutioninfoeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 服务商信息控制层

## POST 分页查询

POST /st/lifePay/providerInfo/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "serviceProvider": "string",
  "mid": "string",
  "pid": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "billingOrg": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ServiceProviderQueryDTO](#schemaserviceproviderquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {}
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultServiceProviderVO](#schemapaginationresultserviceprovidervo)|

## POST 导出Excel

POST /st/lifePay/providerInfo/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "serviceProvider": "string",
  "mid": "string",
  "pid": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "billingOrg": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ServiceProviderQueryDTO](#schemaserviceproviderquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/providerInfo/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/lifePay/providerInfo/add

> Body 请求参数

```json
{
  "id": 0,
  "serviceProvider": "string",
  "agreementType": "string",
  "billingInstitution": "string",
  "mid": "string",
  "pid": "string",
  "goLiveTime": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "lastCommissionRatio": 0,
  "rate": 0,
  "isDiscounted": "string",
  "currCommissionRatio": 0,
  "remark": "string",
  "status": "string",
  "archiveTime": "string",
  "originProvider": "string",
  "billingOrg": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ServiceProviderEditDTO](#schemaserviceprovidereditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/lifePay/providerInfo/edit

> Body 请求参数

```json
{
  "id": 0,
  "serviceProvider": "string",
  "agreementType": "string",
  "billingInstitution": "string",
  "mid": "string",
  "pid": "string",
  "goLiveTime": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "lastCommissionRatio": 0,
  "rate": 0,
  "isDiscounted": "string",
  "currCommissionRatio": 0,
  "remark": "string",
  "status": "string",
  "archiveTime": "string",
  "originProvider": "string",
  "billingOrg": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ServiceProviderEditDTO](#schemaserviceprovidereditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/lifePay/providerInfo/delete

> Body 请求参数

```json
{
  "id": 0,
  "serviceProvider": "string",
  "agreementType": "string",
  "billingInstitution": "string",
  "mid": "string",
  "pid": "string",
  "goLiveTime": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "lastCommissionRatio": 0,
  "rate": 0,
  "isDiscounted": "string",
  "currCommissionRatio": 0,
  "remark": "string",
  "status": "string",
  "archiveTime": "string",
  "originProvider": "string",
  "billingOrg": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ServiceProviderEditDTO](#schemaserviceprovidereditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/providerInfo/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 支付宝与机构账单收据单控制层

## POST 分页查询

POST /st/lifePay/aliPayOrg/receipt/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "merchantName": "string",
  "merchantLoginAccount": "string",
  "pid": "string",
  "startMonth": "string",
  "endMonth": "string",
  "createdDateStart": "string",
  "createdDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptQueryDTO](#schemaalipayorgreceiptquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {}
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultAlipayOrgReceiptVO](#schemapaginationresultalipayorgreceiptvo)|

## POST 导出Excel

POST /st/lifePay/aliPayOrg/receipt/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "merchantName": "string",
  "merchantLoginAccount": "string",
  "pid": "string",
  "startMonth": "string",
  "endMonth": "string",
  "createdDateStart": "string",
  "createdDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptQueryDTO](#schemaalipayorgreceiptquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/lifePay/aliPayOrg/receipt/edit

> Body 请求参数

```json
{
  "subject": "string",
  "text": "string",
  "from": "string",
  "to": "string",
  "cc": "string",
  "attachments": [
    {
      "key": "string"
    }
  ],
  "id": 0,
  "totalInvoiceAmount": 0,
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptEditDTO](#schemaalipayorgreceipteditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 发邮件

POST /st/lifePay/aliPayOrg/receipt/sendEmail

> Body 请求参数

```json
{
  "subject": "string",
  "text": "string",
  "from": "string",
  "to": "string",
  "cc": "string",
  "attachments": [
    {
      "key": "string"
    }
  ],
  "id": 0,
  "totalInvoiceAmount": 0,
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptEditDTO](#schemaalipayorgreceipteditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 查看收据函

POST /st/lifePay/aliPayOrg/receipt/preview

> Body 请求参数

```json
{
  "subject": "string",
  "text": "string",
  "from": "string",
  "to": "string",
  "cc": "string",
  "attachments": [
    {
      "key": "string"
    }
  ],
  "id": 0,
  "totalInvoiceAmount": 0,
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptEditDTO](#schemaalipayorgreceipteditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 下载

POST /st/lifePay/aliPayOrg/receipt/download

> Body 请求参数

```json
{
  "subject": "string",
  "text": "string",
  "from": "string",
  "to": "string",
  "cc": "string",
  "attachments": [
    {
      "key": "string"
    }
  ],
  "id": 0,
  "totalInvoiceAmount": 0,
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgReceiptEditDTO](#schemaalipayorgreceipteditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 支付宝与机构账单结算单控制层

## POST 分页查询

POST /st/lifePay/aliPayOrg/settlement/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgSettlementQueryDTO](#schemaalipayorgsettlementquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {}
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultAlipayOrgSettlementVO](#schemapaginationresultalipayorgsettlementvo)|

## POST 导出Excel

POST /st/lifePay/aliPayOrg/settlement/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgSettlementQueryDTO](#schemaalipayorgsettlementquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量生产收据单

POST /st/lifePay/aliPayOrg/settlement/batchGenerate

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlipayOrgSettlementQueryDTO](#schemaalipayorgsettlementquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 公服机构调账控制层

## POST 分页查询

POST /st/lifePay/queryPage

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settlementInstitutionName": "",
  "settlementPid": "",
  "startYearMonth": "",
  "endYearMonth": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceAdjustmentQueryDTO](#schemapublicserviceadjustmentquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "settlementInstitutionName": "",
      "settlementPid": "",
      "merchantMid": "",
      "alipayAccount": "",
      "product": "",
      "startYearMonth": "",
      "endYearMonth": "",
      "invoiceAmount": 0,
      "actualBillingAmount": 0,
      "adjustmentAmount": 0,
      "adjustmentRatio": 0,
      "adjustmentProgress": "",
      "status": "",
      "responsiblePerson": "",
      "adjustmentReason": "",
      "remark": "",
      "submissionTime": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultPublicServiceAdjustmentVO](#schemapaginationresultpublicserviceadjustmentvo)|

## POST 导出Excel

POST /st/lifePay/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "startYearMonth": "string",
  "endYearMonth": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceAdjustmentQueryDTO](#schemapublicserviceadjustmentquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 公服结算信息控制层

## POST 分页查询

POST /st/lifePay/public/settle/queryPage

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settlementInstitutionName": "",
  "settlementPid": "",
  "alipayAccount": "",
  "status": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceSettlementQueryDTO](#schemapublicservicesettlementquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {}
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultPublicServiceSettlementVO](#schemapaginationresultpublicservicesettlementvo)|

## POST 导出Excel

POST /st/lifePay/public/settle/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "alipayAccount": "string",
  "status": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceSettlementQueryDTO](#schemapublicservicesettlementquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/public/settle/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/public/settle/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 公服开票信息管理控制层

## POST 分页查询

POST /st/lifePay/public/billing/queryPage

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settlementPid": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceBillingInfoQueryDTO](#schemapublicservicebillinginfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "settlementPid": "",
      "provinceCityDistrict": "",
      "shippingAddress": "",
      "recipient": "",
      "contactPhone": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultPublicServiceBillingInfoVO](#schemapaginationresultpublicservicebillinginfovo)|

## POST 导出Excel

POST /st/lifePay/public/billing/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementPid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceBillingInfoQueryDTO](#schemapublicservicebillinginfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/public/billing/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 公服发票信息管理控制层

## POST 分页查询

POST /st/lifePay/public/invoice/queryPage

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settlementInstitutionName": "",
  "settlementPid": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceInvoiceInfoQueryDTO](#schemapublicserviceinvoiceinfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "submissionDate": "",
      "alipayMid": "",
      "settlementInstitutionName": "",
      "alipayAccount": "",
      "settlementPid": "",
      "startYearMonth": "",
      "endYearMonth": "",
      "billingAmount": 0,
      "responsiblePerson": "",
      "invoiceOrTaxServiceName": "",
      "invoiceType": "",
      "invoiceRequirements": "",
      "invoiceCode": "",
      "invoiceNumber": "",
      "billingTime": "",
      "expressOrderNumber": "",
      "logisticsStatus": "",
      "paymentStatus": "",
      "returnTicketStatus": "",
      "statusDescription": "",
      "status": "",
      "province": "",
      "city": "",
      "district": "",
      "recipientAddress": "",
      "recipient": "",
      "recipientPhone": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultPublicServiceInvoiceInfoVO](#schemapaginationresultpublicserviceinvoiceinfovo)|

## POST 导出Excel

POST /st/lifePay/public/invoice/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceInvoiceInfoQueryDTO](#schemapublicserviceinvoiceinfoquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/public/invoice/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/public/invoice/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 公服结算计划管理控制层

## POST 分页查询

POST /st/lifePay/public/settlePlan/queryPage

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settlementInstitutionName": "",
  "settlementPid": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceSettlementPlanQueryDTO](#schemapublicservicesettlementplanquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "settlementInstitutionName": "",
      "settlementPid": "",
      "settlementPeriod": "",
      "handlingFeeAmount": 0,
      "billingStatus": "",
      "collectionStatus": "",
      "adjustmentStatus": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultPublicServiceSettlementPlanVO](#schemapaginationresultpublicservicesettlementplanvo)|

## POST 导出Excel

POST /st/lifePay/public/settlePlan/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[PublicServiceSettlementPlanQueryDTO](#schemapublicservicesettlementplanquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/lifePay/public/settlePlan/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/lifePay/public/settlePlan/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 结算材料归档控制层

## POST 列表

POST /st/material/list

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "companyCode": "string",
  "bizType": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SettlementMaterialQueryDTO](#schemasettlementmaterialquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "companyName": "",
      "companyCode": "",
      "bizTypeName": "",
      "bizType": "",
      "customerName": "",
      "customerCode": "",
      "startDate": "",
      "endDate": "",
      "type": "",
      "docName": "",
      "createBy": "",
      "createTime": "",
      "updateBy": "",
      "updateTime": "",
      "delFlag": "",
      "tenantId": 0,
      "materialDetailList": [
        {
          "id": 0,
          "materialId": 0,
          "type": "",
          "fileName": "",
          "filePath": "",
          "delFlag": "",
          "createTime": "",
          "createBy": "",
          "updateTime": "",
          "updateBy": ""
        }
      ],
      "settlementCycle": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultSettlementMaterialVO](#schemapaginationresultsettlementmaterialvo)|

## POST 导出

POST /st/material/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "companyCode": "string",
  "bizType": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SettlementMaterialQueryDTO](#schemasettlementmaterialquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/material/add

> Body 请求参数

```json
{
  "id": 0,
  "companyName": "string",
  "companyCode": "string",
  "bizTypeName": "string",
  "bizType": "string",
  "customerName": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string",
  "docName": "string",
  "createBy": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateTime": "string",
  "delFlag": "string",
  "tenantId": 0,
  "materialDetailList": [
    {
      "id": 0,
      "materialId": 0,
      "type": "string",
      "fileName": "string",
      "filePath": "string",
      "delFlag": "string",
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SettlementMaterialEditDTO](#schemasettlementmaterialeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/material/edit

> Body 请求参数

```json
{
  "id": 0,
  "companyName": "string",
  "companyCode": "string",
  "bizTypeName": "string",
  "bizType": "string",
  "customerName": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string",
  "docName": "string",
  "createBy": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateTime": "string",
  "delFlag": "string",
  "tenantId": 0,
  "materialDetailList": [
    {
      "id": 0,
      "materialId": 0,
      "type": "string",
      "fileName": "string",
      "filePath": "string",
      "delFlag": "string",
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SettlementMaterialEditDTO](#schemasettlementmaterialeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/material/delete

> Body 请求参数

```json
{
  "id": 0,
  "companyName": "string",
  "companyCode": "string",
  "bizTypeName": "string",
  "bizType": "string",
  "customerName": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string",
  "docName": "string",
  "createBy": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateTime": "string",
  "delFlag": "string",
  "tenantId": 0,
  "materialDetailList": [
    {
      "id": 0,
      "materialId": 0,
      "type": "string",
      "fileName": "string",
      "filePath": "string",
      "delFlag": "string",
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SettlementMaterialEditDTO](#schemasettlementmaterialeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 新电途充电分润进度控制层

## POST 分页查询

POST /st/newcharge/profit/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "reconciliationPerson": "string",
  "ourSeal": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "returnComplete": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitQueryDTO](#schemanewchargeprofitquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "billYearMonth": "",
      "operator": "",
      "costType": "",
      "sharedIncome": 0,
      "returnAmount": 0,
      "reconciliationPerson": "",
      "billReview": "",
      "counterpartySeal": "",
      "ourSeal": "",
      "returnComplete": "",
      "remarks": "",
      "mode": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeProfitVO](#schemapaginationresultnewchargeprofitvo)|

## POST 导出Excel

POST /st/newcharge/profit/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "reconciliationPerson": "string",
  "ourSeal": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "returnComplete": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitQueryDTO](#schemanewchargeprofitquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newcharge/profit/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newcharge/profit/add

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitVO](#schemanewchargeprofitvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newcharge/profit/edit

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitVO](#schemanewchargeprofitvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newcharge/profit/batchAdd

> Body 请求参数

```json
[
  {
    "billYearMonth": "string",
    "operator": "string",
    "costType": "string",
    "sharedIncome": 0,
    "returnAmount": 0,
    "reconciliationPerson": "string",
    "billReview": "string",
    "counterpartySeal": "string",
    "ourSeal": "string",
    "returnComplete": "string",
    "remarks": "string",
    "mode": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitImportDTO](#schemanewchargeprofitimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newcharge/profit/batchOperation

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string",
  "idList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeProfitOperationDTO](#schemanewchargeprofitoperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/profit/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/newcharge/profit/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 新电途充电结算人员与运营商维护控制层

## POST 分页查询

POST /st/newcharge/maintenance/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceQueryDTO](#schemanewchargemaintenancequerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "operator": "",
      "mode": "",
      "responsiblePerson": "",
      "remarks": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeMaintenanceVO](#schemapaginationresultnewchargemaintenancevo)|

## POST 导出Excel

POST /st/newcharge/maintenance/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceQueryDTO](#schemanewchargemaintenancequerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newcharge/maintenance/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newcharge/maintenance/add

> Body 请求参数

```json
{
  "id": 0,
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string",
  "remarks": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceVO](#schemanewchargemaintenancevo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newcharge/maintenance/edit

> Body 请求参数

```json
{
  "id": 0,
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string",
  "remarks": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceVO](#schemanewchargemaintenancevo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newcharge/maintenance/batchAdd

> Body 请求参数

```json
[
  {
    "operator": "string",
    "mode": "string",
    "responsiblePerson": "string",
    "remarks": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceImportDTO](#schemanewchargemaintenanceimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newcharge/maintenance/batchOperation

> Body 请求参数

```json
{
  "idList": [
    0
  ],
  "responsiblePerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeMaintenanceOperationDTO](#schemanewchargemaintenanceoperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/maintenance/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/newcharge/maintenance/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 新电途充电购电进度控制层

## POST 分页查询

POST /st/newCharge/power/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "counterpartyConfirmation": "string",
  "ourSeal": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "billCheck": "string",
  "returnComplete": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerQueryDTO](#schemanewchargepowerquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "billYearMonth": "",
      "operator": "",
      "returnAmount": 0,
      "reconciliationPerson": "",
      "billCheck": "",
      "counterpartyConfirmation": "",
      "counterpartySeal": "",
      "ourSeal": "",
      "returnComplete": "",
      "remarks": "",
      "mode": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargePowerVO](#schemapaginationresultnewchargepowervo)|

## POST 导出Excel

POST /st/newCharge/power/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "counterpartyConfirmation": "string",
  "ourSeal": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "billCheck": "string",
  "returnComplete": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerQueryDTO](#schemanewchargepowerquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newCharge/power/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newCharge/power/add

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerVO](#schemanewchargepowervo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newCharge/power/edit

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerVO](#schemanewchargepowervo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newCharge/power/batchAdd

> Body 请求参数

```json
[
  {
    "billYearMonth": "string",
    "operator": "string",
    "returnAmount": 0,
    "reconciliationPerson": "string",
    "billCheck": "string",
    "counterpartyConfirmation": "string",
    "counterpartySeal": "string",
    "ourSeal": "string",
    "returnComplete": "string",
    "remarks": "string",
    "mode": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerImportDTO](#schemanewchargepowerimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newCharge/power/batchOperation

> Body 请求参数

```json
{
  "idList": [
    0
  ],
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargePowerOperationDTO](#schemanewchargepoweroperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newCharge/power/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 新电途线上扣款记录

## POST 分页查询

POST /st/newcharge/online/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementMethod": "string",
  "paymentStatus": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeOnlineQueryDTO](#schemanewchargeonlinequerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "operatorName": "",
      "billDate": "",
      "billMonth": "",
      "billMonthStart": "",
      "billMonthEnd": "",
      "settlementMethod": "",
      "chargingDegree": 0,
      "chargingAmount": 0,
      "chargingSettlementRatio": "",
      "dischargingDegree": 0,
      "dischargingAmount": 0,
      "dischargingStationRatio": "",
      "adjustedChargingFee": 0,
      "adjustedDischargingFee": 0,
      "difference": 0,
      "platformPayable": 0,
      "platformReceivable": 0,
      "platformPendingPay": 0,
      "platformPendingReceive": 0,
      "paymentStatus": "",
      "platformActualReceive": 0,
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "updateTime": "",
      "updateBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeOnlineVO](#schemapaginationresultnewchargeonlinevo)|

## POST 导出Excel

POST /st/newcharge/online/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementMethod": "string",
  "paymentStatus": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeOnlineQueryDTO](#schemanewchargeonlinequerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/online/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

# 新电途场站结算进度

## POST 分页查询

POST /st/newcharge/station/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "operator": "string",
  "siteName": "string",
  "settlementMethod": "string",
  "settlementStatus": "string",
  "investmentMode": "string",
  "reconciliationPerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationQueryDTO](#schemanewchargestationquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "billYearMonth": "",
      "operator": "",
      "siteName": "",
      "investmentMode": "",
      "settlementMethod": "",
      "discount": "",
      "chargingAmount": 0,
      "actualChargingFee": 0,
      "dischargingAmount": 0,
      "actualDischargingFee": 0,
      "powerLossRate": "",
      "income": 0,
      "payableAmount": 0,
      "receivableAmount": 0,
      "billingDate": "",
      "paymentDate": "",
      "settlementStatus": "",
      "remarks": "",
      "reconciliationPerson": "",
      "updateBy": "",
      "updateTime": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeStationVO](#schemapaginationresultnewchargestationvo)|

## POST 导出Excel

POST /st/newcharge/station/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "operator": "string",
  "siteName": "string",
  "settlementMethod": "string",
  "settlementStatus": "string",
  "investmentMode": "string",
  "reconciliationPerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationQueryDTO](#schemanewchargestationquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newcharge/station/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newcharge/station/add

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "siteName": "string",
  "investmentMode": "string",
  "settlementMethod": "string",
  "discount": "string",
  "chargingAmount": 0,
  "actualChargingFee": 0,
  "dischargingAmount": 0,
  "actualDischargingFee": 0,
  "powerLossRate": "string",
  "income": 0,
  "payableAmount": 0,
  "receivableAmount": 0,
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationVO](#schemanewchargestationvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newcharge/station/edit

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "siteName": "string",
  "investmentMode": "string",
  "settlementMethod": "string",
  "discount": "string",
  "chargingAmount": 0,
  "actualChargingFee": 0,
  "dischargingAmount": 0,
  "actualDischargingFee": 0,
  "powerLossRate": "string",
  "income": 0,
  "payableAmount": 0,
  "receivableAmount": 0,
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationVO](#schemanewchargestationvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newcharge/station/batchAdd

> Body 请求参数

```json
[
  {
    "billYearMonth": "string",
    "operator": "string",
    "siteName": "string",
    "investmentMode": "string",
    "settlementMethod": "string",
    "discount": "string",
    "chargingAmount": 0,
    "actualChargingFee": 0,
    "dischargingAmount": 0,
    "actualDischargingFee": 0,
    "powerLossRate": "string",
    "income": 0,
    "payableAmount": 0,
    "receivableAmount": 0,
    "billingDate": "string",
    "paymentDate": "string",
    "settlementStatus": "string",
    "remarks": "string",
    "reconciliationPerson": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationImportDTO](#schemanewchargestationimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newcharge/station/batchOperation

> Body 请求参数

```json
{
  "idList": [
    0
  ],
  "settlementMethod": "string",
  "discount": "string",
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "reconciliationPerson": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationOperationDTO](#schemanewchargestationoperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/station/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/newcharge/station/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 新电途场地回款统计

## POST 分页查询

POST /st/newcharge/station/payment/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "fundPath": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentQueryDTO](#schemanewchargestationpaymentquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "operatorName": "",
      "fundPath": "",
      "totalReceivable": 0,
      "custodyRecoveryAmount": 0,
      "offlinePaymentAmount": 0,
      "electricityPurchaseDeduction": 0,
      "totalPaymentAmount": 0,
      "pendingPaymentAmount": 0,
      "remarks": "",
      "updateBy": "",
      "updateTime": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeStationPaymentVO](#schemapaginationresultnewchargestationpaymentvo)|

## POST 导出Excel

POST /st/newcharge/station/payment/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "fundPath": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentQueryDTO](#schemanewchargestationpaymentquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newcharge/station/payment/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newcharge/station/payment/add

> Body 请求参数

```json
{
  "id": 0,
  "operatorName": "string",
  "fundPath": "string",
  "totalReceivable": 0,
  "custodyRecoveryAmount": 0,
  "offlinePaymentAmount": 0,
  "electricityPurchaseDeduction": 0,
  "totalPaymentAmount": 0,
  "pendingPaymentAmount": 0,
  "remarks": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentVO](#schemanewchargestationpaymentvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newcharge/station/payment/edit

> Body 请求参数

```json
{
  "id": 0,
  "operatorName": "string",
  "fundPath": "string",
  "totalReceivable": 0,
  "custodyRecoveryAmount": 0,
  "offlinePaymentAmount": 0,
  "electricityPurchaseDeduction": 0,
  "totalPaymentAmount": 0,
  "pendingPaymentAmount": 0,
  "remarks": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentVO](#schemanewchargestationpaymentvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newcharge/station/payment/batchAdd

> Body 请求参数

```json
[
  {
    "operatorName": "string",
    "fundPath": "string",
    "totalReceivable": 0,
    "custodyRecoveryAmount": 0,
    "offlinePaymentAmount": 0,
    "electricityPurchaseDeduction": 0,
    "totalPaymentAmount": 0,
    "pendingPaymentAmount": 0,
    "remarks": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentImportDTO](#schemanewchargestationpaymentimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newcharge/station/payment/batchOperation

> Body 请求参数

```json
{
  "idList": [
    0
  ],
  "fundPath": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeStationPaymentOperationDTO](#schemanewchargestationpaymentoperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/station/payment/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/newcharge/station/payment/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 新电途他投结算进度

## POST 分页查询

POST /st/newcharge/third/queryPage

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "investorName": "string",
  "paymentProgress": "string",
  "siteName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdQueryDTO](#schemanewchargethirdquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": [
    {
      "id": 0,
      "billYearMonth": "",
      "investorName": "",
      "operator": "",
      "siteName": "",
      "stationRatio": "",
      "investorRatio": "",
      "platformRatio": "",
      "chargingAmount": 0,
      "chargingFee": 0,
      "dischargingAmount": 0,
      "dischargingFee": 0,
      "electricityRate": "",
      "energyManagementIncome": 0,
      "energyManagementFee": 0,
      "assetIncome": 0,
      "platformIncome": 0,
      "paymentProgress": "",
      "remarks": "",
      "reconciliationPerson": "",
      "updateBy": "",
      "updateTime": "",
      "tenantId": 0,
      "createTime": "",
      "createBy": "",
      "dataSource": ""
    }
  ],
  "traceId": "",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[PaginationResultNewChargeThirdVO](#schemapaginationresultnewchargethirdvo)|

## POST 导出Excel

POST /st/newcharge/third/exportExcel

> Body 请求参数

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "investorName": "string",
  "paymentProgress": "string",
  "siteName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdQueryDTO](#schemanewchargethirdquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 删除

POST /st/newcharge/third/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |[The{@code Long} class wraps a value of the primitive type{@code|
|Authorization|header|string| 否 |none|

#### 详细说明

**id**: [The{@code Long} class wraps a value of the primitive type{@code
* long} in an object. An object of type{@code Long} contains a
single field whose type is{@code long}.

<p> In addition, this class provides several methods for converting
a{@code long} to a{@code String} and a{@code String} to a{@code
* long}, as well as other constants and methods useful when dealing
with a{@code long}.

<p>Implementation note: The implementations of the "bit twiddling"
methods (such as{@link #highestOneBit(long) highestOneBit} and
{@link #numberOfTrailingZeros(long) numberOfTrailingZeros}) are
based on material from Henry S. Warren, Jr.'s <i>Hacker's
Delight</i>, (Addison Wesley, 2002).]
-9223372036854775808 :A constant holding the minimum value a{@code long} can
have, -2<sup>63</sup>.
9223372036854775807 :A constant holding the maximum value a{@code long} can
have, 2<sup>63</sup>-1.
64 :The number of bits used to represent a{@code long} value in two's
complement binary form.
8 :The number of bytes used to represent a{@code long} value in two's
complement binary form.

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 新增

POST /st/newcharge/third/add

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "investorName": "string",
  "operator": "string",
  "siteName": "string",
  "stationRatio": "string",
  "investorRatio": "string",
  "platformRatio": "string",
  "chargingAmount": 0,
  "chargingFee": 0,
  "dischargingAmount": 0,
  "dischargingFee": 0,
  "electricityRate": "string",
  "energyManagementIncome": 0,
  "energyManagementFee": 0,
  "assetIncome": 0,
  "platformIncome": 0,
  "paymentProgress": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdVO](#schemanewchargethirdvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 编辑

POST /st/newcharge/third/edit

> Body 请求参数

```json
{
  "id": 0,
  "billYearMonth": "string",
  "investorName": "string",
  "operator": "string",
  "siteName": "string",
  "stationRatio": "string",
  "investorRatio": "string",
  "platformRatio": "string",
  "chargingAmount": 0,
  "chargingFee": 0,
  "dischargingAmount": 0,
  "dischargingFee": 0,
  "electricityRate": "string",
  "energyManagementIncome": 0,
  "energyManagementFee": 0,
  "assetIncome": 0,
  "platformIncome": 0,
  "paymentProgress": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdVO](#schemanewchargethirdvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量新增

POST /st/newcharge/third/batchAdd

> Body 请求参数

```json
[
  {
    "billYearMonth": "string",
    "investorName": "string",
    "operator": "string",
    "siteName": "string",
    "stationRatio": "string",
    "investorRatio": "string",
    "platformRatio": "string",
    "chargingAmount": 0,
    "chargingFee": 0,
    "dischargingAmount": 0,
    "dischargingFee": 0,
    "electricityRate": "string",
    "energyManagementIncome": 0,
    "energyManagementFee": 0,
    "assetIncome": 0,
    "platformIncome": 0,
    "paymentProgress": "string",
    "remarks": "string",
    "reconciliationPerson": "string"
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdImportDTO](#schemanewchargethirdimportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 批量操作

POST /st/newcharge/third/batchOperation

> Body 请求参数

```json
{
  "idList": [
    0
  ],
  "paymentProgress": "string",
  "reconciliationPerson": "string",
  "remarks": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[NewChargeThirdOperationDTO](#schemanewchargethirdoperationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## POST 导入Excel

POST /st/newcharge/third/importExcel

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |[MultipartFile]|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": "",
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultString](#schemarestresultstring)|

## GET 获取下拉列表，{key: "字段名", value: list}

GET /st/newcharge/third/getDropLists

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "message": "",
  "data": {
    "": [
      ""
    ]
  },
  "traceId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestResultMapListString](#schemarestresultmapliststring)|

# 数据模型

<h2 id="tocS_BankFlowVO">BankFlowVO</h2>

<a id="schemabankflowvo"></a>
<a id="schema_BankFlowVO"></a>
<a id="tocSbankflowvo"></a>
<a id="tocsbankflowvo"></a>

```json
{
  "id": 0,
  "industry": "string",
  "isNucleated": "string",
  "nucleationSubmitTime": "string",
  "reasonClassification": "string",
  "subReasonClassification": "string",
  "cleanupClassification": "string",
  "remark": "string",
  "matchedBillMonth": "string",
  "unNucleatedAmount": 0,
  "ourAccountNumber": "string",
  "bankTransactionDate": "string",
  "currency": "string",
  "debitAmount": 0,
  "creditAmount": 0,
  "treasuryFlowType": "string",
  "bankRemark": "string",
  "counterpartyBankName": "string",
  "openingBank": "string",
  "counterpartyAccountNumber": "string",
  "supplementaryNotes": "string",
  "flowStatus": "string",
  "flowDestination": "string",
  "flowDestinationAccount": "string",
  "bankFlowNumber": "string",
  "createBy": "string",
  "createTime": "string",
  "tenantId": 0,
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|industry|string|false|none||行业|
|isNucleated|string|false|none||是否核销|
|nucleationSubmitTime|string|false|none||提交核销时间|
|reasonClassification|string|false|none||原因分类|
|subReasonClassification|string|false|none||子原因分类|
|cleanupClassification|string|false|none||清理分类|
|remark|string|false|none||备注|
|matchedBillMonth|string|false|none||匹配的账单月份|
|unNucleatedAmount|number|false|none||未核销金额|
|ourAccountNumber|string|false|none||本方账号|
|bankTransactionDate|string|false|none||银行交易日期(sec)|
|currency|string|false|none||币种|
|debitAmount|number|false|none||借方金额(单位:元)|
|creditAmount|number|false|none||贷方金额(单位:元)|
|treasuryFlowType|string|false|none||财资流水类型|
|bankRemark|string|false|none||银行备注|
|counterpartyBankName|string|false|none||对方银行户名|
|openingBank|string|false|none||开户行|
|counterpartyAccountNumber|string|false|none||对方账号|
|supplementaryNotes|string|false|none||补充说明|
|flowStatus|string|false|none||流水状态|
|flowDestination|string|false|none||流水去向|
|flowDestinationAccount|string|false|none||流水去向账号|
|bankFlowNumber|string|false|none||银行流水号|
|createBy|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|tenantId|integer(int64)|false|none||租户号|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_StMaterialDetail">StMaterialDetail</h2>

<a id="schemastmaterialdetail"></a>
<a id="schema_StMaterialDetail"></a>
<a id="tocSstmaterialdetail"></a>
<a id="tocsstmaterialdetail"></a>

```json
{
  "id": 0,
  "materialId": 0,
  "type": "string",
  "fileName": "string",
  "filePath": "string",
  "delFlag": "string",
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|materialId|integer(int64)|false|none||结算材料id|
|type|string|false|none||类型 字典:st_material_type|
|fileName|string|false|none||文件名称|
|filePath|string|false|none||文件地址|
|delFlag|string|false|none||删除标志（0正常 1删除）|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|

<h2 id="tocS_NewChargeProfitVO">NewChargeProfitVO</h2>

<a id="schemanewchargeprofitvo"></a>
<a id="schema_NewChargeProfitVO"></a>
<a id="tocSnewchargeprofitvo"></a>
<a id="tocsnewchargeprofitvo"></a>

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|costType|string|false|none||费用类型|
|sharedIncome|number|false|none||分摊收入(元)|
|returnAmount|number|false|none||回票金额(元)|
|reconciliationPerson|string|false|none||对账负责人|
|billReview|string|false|none||账单复核|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|
|mode|string|false|none||模式|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargeMaintenanceVO">NewChargeMaintenanceVO</h2>

<a id="schemanewchargemaintenancevo"></a>
<a id="schema_NewChargeMaintenanceVO"></a>
<a id="tocSnewchargemaintenancevo"></a>
<a id="tocsnewchargemaintenancevo"></a>

```json
{
  "id": 0,
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string",
  "remarks": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|operator|string|false|none||运营商名称|
|mode|string|false|none||模式|
|responsiblePerson|string|false|none||负责人|
|remarks|string|false|none||备注信息|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultBankFlowVO">PaginationResultBankFlowVO</h2>

<a id="schemapaginationresultbankflowvo"></a>
<a id="schema_PaginationResultBankFlowVO"></a>
<a id="tocSpaginationresultbankflowvo"></a>
<a id="tocspaginationresultbankflowvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "industry": "string",
      "isNucleated": "string",
      "nucleationSubmitTime": "string",
      "reasonClassification": "string",
      "subReasonClassification": "string",
      "cleanupClassification": "string",
      "remark": "string",
      "matchedBillMonth": "string",
      "unNucleatedAmount": 0,
      "ourAccountNumber": "string",
      "bankTransactionDate": "string",
      "currency": "string",
      "debitAmount": 0,
      "creditAmount": 0,
      "treasuryFlowType": "string",
      "bankRemark": "string",
      "counterpartyBankName": "string",
      "openingBank": "string",
      "counterpartyAccountNumber": "string",
      "supplementaryNotes": "string",
      "flowStatus": "string",
      "flowDestination": "string",
      "flowDestinationAccount": "string",
      "bankFlowNumber": "string",
      "createBy": "string",
      "createTime": "string",
      "tenantId": 0,
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[BankFlowVO](#schemabankflowvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_SettlementMaterialVO">SettlementMaterialVO</h2>

<a id="schemasettlementmaterialvo"></a>
<a id="schema_SettlementMaterialVO"></a>
<a id="tocSsettlementmaterialvo"></a>
<a id="tocssettlementmaterialvo"></a>

```json
{
  "id": 0,
  "companyName": "string",
  "companyCode": "string",
  "bizTypeName": "string",
  "bizType": "string",
  "customerName": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string",
  "docName": "string",
  "createBy": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateTime": "string",
  "delFlag": "string",
  "tenantId": 0,
  "materialDetailList": [
    {
      "id": 0,
      "materialId": 0,
      "type": "string",
      "fileName": "string",
      "filePath": "string",
      "delFlag": "string",
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string"
    }
  ],
  "settlementCycle": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|companyName|string|false|none||所属公司名称|
|companyCode|string|false|none||所属公司编码 字典:st_material_company|
|bizTypeName|string|false|none||所属业务名称|
|bizType|string|false|none||所属业务 字典:st_material_biz|
|customerName|string|false|none||客户名称|
|customerCode|string|false|none||客户编码 字典:st_material_customer|
|startDate|string|false|none||结算周期开始|
|endDate|string|false|none||结算周期结束|
|type|string|false|none||类型 字典:st_material_type|
|docName|string|false|none||文件名称|
|createBy|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|delFlag|string|false|none||删除标志（0正常 1删除）|
|tenantId|integer(int64)|false|none||租户号|
|materialDetailList|[[StMaterialDetail](#schemastmaterialdetail)]|false|none||材料明细|
|settlementCycle|string|false|none||结算周期|

<h2 id="tocS_PaginationResultNewChargeProfitVO">PaginationResultNewChargeProfitVO</h2>

<a id="schemapaginationresultnewchargeprofitvo"></a>
<a id="schema_PaginationResultNewChargeProfitVO"></a>
<a id="tocSpaginationresultnewchargeprofitvo"></a>
<a id="tocspaginationresultnewchargeprofitvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "billYearMonth": "string",
      "operator": "string",
      "costType": "string",
      "sharedIncome": 0,
      "returnAmount": 0,
      "reconciliationPerson": "string",
      "billReview": "string",
      "counterpartySeal": "string",
      "ourSeal": "string",
      "returnComplete": "string",
      "remarks": "string",
      "mode": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeProfitVO](#schemanewchargeprofitvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PaginationResultNewChargeMaintenanceVO">PaginationResultNewChargeMaintenanceVO</h2>

<a id="schemapaginationresultnewchargemaintenancevo"></a>
<a id="schema_PaginationResultNewChargeMaintenanceVO"></a>
<a id="tocSpaginationresultnewchargemaintenancevo"></a>
<a id="tocspaginationresultnewchargemaintenancevo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "operator": "string",
      "mode": "string",
      "responsiblePerson": "string",
      "remarks": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeMaintenanceVO](#schemanewchargemaintenancevo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_BankFlowQueryDTO">BankFlowQueryDTO</h2>

<a id="schemabankflowquerydto"></a>
<a id="schema_BankFlowQueryDTO"></a>
<a id="tocSbankflowquerydto"></a>
<a id="tocsbankflowquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "remark": "string",
  "counterpartyBankName": "string",
  "matchedBillMonthStart": "string",
  "matchedBillMonthEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|remark|string|false|none||备注pid|
|counterpartyBankName|string|false|none||银行户名|
|matchedBillMonthStart|string|false|none||账单月份开始|
|matchedBillMonthEnd|string|false|none||账单月份结束|

<h2 id="tocS_PaginationResultSettlementMaterialVO">PaginationResultSettlementMaterialVO</h2>

<a id="schemapaginationresultsettlementmaterialvo"></a>
<a id="schema_PaginationResultSettlementMaterialVO"></a>
<a id="tocSpaginationresultsettlementmaterialvo"></a>
<a id="tocspaginationresultsettlementmaterialvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "companyName": "string",
      "companyCode": "string",
      "bizTypeName": "string",
      "bizType": "string",
      "customerName": "string",
      "customerCode": "string",
      "startDate": "string",
      "endDate": "string",
      "type": "string",
      "docName": "string",
      "createBy": "string",
      "createTime": "string",
      "updateBy": "string",
      "updateTime": "string",
      "delFlag": "string",
      "tenantId": 0,
      "materialDetailList": [
        {
          "id": 0,
          "materialId": 0,
          "type": "string",
          "fileName": "string",
          "filePath": "string",
          "delFlag": "string",
          "createTime": "string",
          "createBy": "string",
          "updateTime": "string",
          "updateBy": "string"
        }
      ],
      "settlementCycle": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[SettlementMaterialVO](#schemasettlementmaterialvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargeProfitQueryDTO">NewChargeProfitQueryDTO</h2>

<a id="schemanewchargeprofitquerydto"></a>
<a id="schema_NewChargeProfitQueryDTO"></a>
<a id="tocSnewchargeprofitquerydto"></a>
<a id="tocsnewchargeprofitquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "reconciliationPerson": "string",
  "ourSeal": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "returnComplete": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|operator|string|false|none||运营商|
|reconciliationPerson|string|false|none||对账负责人|
|ourSeal|string|false|none||我方盖章|
|billReview|string|false|none||账单复核|
|counterpartySeal|string|false|none||对方盖章|
|returnComplete|string|false|none||回票完成|
|billYearMonthStart|string|false|none||账单年月开始|
|billYearMonthEnd|string|false|none||账单年月结束|

<h2 id="tocS_NewChargeMaintenanceQueryDTO">NewChargeMaintenanceQueryDTO</h2>

<a id="schemanewchargemaintenancequerydto"></a>
<a id="schema_NewChargeMaintenanceQueryDTO"></a>
<a id="tocSnewchargemaintenancequerydto"></a>
<a id="tocsnewchargemaintenancequerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|operator|string|false|none||运营商|
|mode|string|false|none||模式|
|responsiblePerson|string|false|none||负责人|

<h2 id="tocS_RestResultString">RestResultString</h2>

<a id="schemarestresultstring"></a>
<a id="schema_RestResultString"></a>
<a id="tocSrestresultstring"></a>
<a id="tocsrestresultstring"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": "string",
  "traceId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|string|false|none||none|
|traceId|string|false|none||none|

<h2 id="tocS_SettlementMaterialQueryDTO">SettlementMaterialQueryDTO</h2>

<a id="schemasettlementmaterialquerydto"></a>
<a id="schema_SettlementMaterialQueryDTO"></a>
<a id="tocSsettlementmaterialquerydto"></a>
<a id="tocssettlementmaterialquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "companyCode": "string",
  "bizType": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|companyCode|string|false|none||所属公司名称编码 字典:st_material_company|
|bizType|string|false|none||所属业务编码 字典:st_material_biz|
|customerCode|string|false|none||客户名称编码 字典:st_material_customer|
|startDate|string|false|none||结算周期开始 yyyy-MM-dd|
|endDate|string|false|none||结算周期结束 yyyy-MM-dd|
|type|string|false|none||类型编码 字典:st_material_type|

<h2 id="tocS_MapListString">MapListString</h2>

<a id="schemamapliststring"></a>
<a id="schema_MapListString"></a>
<a id="tocSmapliststring"></a>
<a id="tocsmapliststring"></a>

```json
{
  "key": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|[string]|false|none||none|

<h2 id="tocS_AlipayBdBillCheckBankVO">AlipayBdBillCheckBankVO</h2>

<a id="schemaalipaybdbillcheckbankvo"></a>
<a id="schema_AlipayBdBillCheckBankVO"></a>
<a id="tocSalipaybdbillcheckbankvo"></a>
<a id="tocsalipaybdbillcheckbankvo"></a>

```json
{
  "sameNum": 0,
  "diffNum": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|sameNum|integer|false|none||none|
|diffNum|integer|false|none||none|

<h2 id="tocS_NewChargeProfitImportDTO">NewChargeProfitImportDTO</h2>

<a id="schemanewchargeprofitimportdto"></a>
<a id="schema_NewChargeProfitImportDTO"></a>
<a id="tocSnewchargeprofitimportdto"></a>
<a id="tocsnewchargeprofitimportdto"></a>

```json
{
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|costType|string|false|none||费用类型|
|sharedIncome|number|false|none||分摊收入(元)|
|returnAmount|number|false|none||回票金额(元)|
|reconciliationPerson|string|false|none||对账负责人|
|billReview|string|false|none||账单复核|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|
|mode|string|false|none||模式|

<h2 id="tocS_NewChargeMaintenanceImportDTO">NewChargeMaintenanceImportDTO</h2>

<a id="schemanewchargemaintenanceimportdto"></a>
<a id="schema_NewChargeMaintenanceImportDTO"></a>
<a id="tocSnewchargemaintenanceimportdto"></a>
<a id="tocsnewchargemaintenanceimportdto"></a>

```json
{
  "operator": "string",
  "mode": "string",
  "responsiblePerson": "string",
  "remarks": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|operator|string|false|none||运营商名称|
|mode|string|false|none||模式|
|responsiblePerson|string|false|none||负责人|
|remarks|string|false|none||备注信息|

<h2 id="tocS_RestResultMapListString">RestResultMapListString</h2>

<a id="schemarestresultmapliststring"></a>
<a id="schema_RestResultMapListString"></a>
<a id="tocSrestresultmapliststring"></a>
<a id="tocsrestresultmapliststring"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": {
    "key": [
      "string"
    ]
  },
  "traceId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[MapListString](#schemamapliststring)|false|none||none|
|traceId|string|false|none||none|

<h2 id="tocS_RestResultAlipayBdBillCheckBankVO">RestResultAlipayBdBillCheckBankVO</h2>

<a id="schemarestresultalipaybdbillcheckbankvo"></a>
<a id="schema_RestResultAlipayBdBillCheckBankVO"></a>
<a id="tocSrestresultalipaybdbillcheckbankvo"></a>
<a id="tocsrestresultalipaybdbillcheckbankvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": {
    "sameNum": 0,
    "diffNum": 0
  },
  "traceId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[AlipayBdBillCheckBankVO](#schemaalipaybdbillcheckbankvo)|false|none||none|
|traceId|string|false|none||none|

<h2 id="tocS_SettlementMaterialEditDTO">SettlementMaterialEditDTO</h2>

<a id="schemasettlementmaterialeditdto"></a>
<a id="schema_SettlementMaterialEditDTO"></a>
<a id="tocSsettlementmaterialeditdto"></a>
<a id="tocssettlementmaterialeditdto"></a>

```json
{
  "id": 0,
  "companyName": "string",
  "companyCode": "string",
  "bizTypeName": "string",
  "bizType": "string",
  "customerName": "string",
  "customerCode": "string",
  "startDate": "string",
  "endDate": "string",
  "type": "string",
  "docName": "string",
  "createBy": "string",
  "createTime": "string",
  "updateBy": "string",
  "updateTime": "string",
  "delFlag": "string",
  "tenantId": 0,
  "materialDetailList": [
    {
      "id": 0,
      "materialId": 0,
      "type": "string",
      "fileName": "string",
      "filePath": "string",
      "delFlag": "string",
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|companyName|string|false|none||所属公司名称|
|companyCode|string|false|none||所属公司编码 字典:st_material_company|
|bizTypeName|string|false|none||所属业务名称|
|bizType|string|false|none||所属业务 字典:st_material_biz|
|customerName|string|false|none||客户名称|
|customerCode|string|false|none||客户编码 字典:st_material_customer|
|startDate|string|false|none||结算周期开始|
|endDate|string|false|none||结算周期结束|
|type|string|false|none||类型 字典:st_material_type|
|docName|string|false|none||文件名称|
|createBy|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|delFlag|string|false|none||删除标志（0正常 1删除）|
|tenantId|integer(int64)|false|none||租户号|
|materialDetailList|[[StMaterialDetail](#schemastmaterialdetail)]|false|none||材料明细|

<h2 id="tocS_NewChargeProfitOperationDTO">NewChargeProfitOperationDTO</h2>

<a id="schemanewchargeprofitoperationdto"></a>
<a id="schema_NewChargeProfitOperationDTO"></a>
<a id="tocSnewchargeprofitoperationdto"></a>
<a id="tocsnewchargeprofitoperationdto"></a>

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "costType": "string",
  "sharedIncome": 0,
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billReview": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string",
  "idList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|costType|string|false|none||费用类型|
|sharedIncome|number|false|none||分摊收入(元)|
|returnAmount|number|false|none||回票金额(元)|
|reconciliationPerson|string|false|none||对账负责人|
|billReview|string|false|none||账单复核|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|
|mode|string|false|none||模式|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|
|idList|[integer]|false|none||id集合|

<h2 id="tocS_NewChargeMaintenanceOperationDTO">NewChargeMaintenanceOperationDTO</h2>

<a id="schemanewchargemaintenanceoperationdto"></a>
<a id="schema_NewChargeMaintenanceOperationDTO"></a>
<a id="tocSnewchargemaintenanceoperationdto"></a>
<a id="tocsnewchargemaintenanceoperationdto"></a>

```json
{
  "idList": [
    0
  ],
  "responsiblePerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|idList|[integer]|false|none||id列表|
|responsiblePerson|string|false|none||负责人|

<h2 id="tocS_AlipayBdBillVO">AlipayBdBillVO</h2>

<a id="schemaalipaybdbillvo"></a>
<a id="schema_AlipayBdBillVO"></a>
<a id="tocSalipaybdbillvo"></a>
<a id="tocsalipaybdbillvo"></a>

```json
{
  "id": 0,
  "policyNo": "string",
  "policyName": "string",
  "bizCycle": "string",
  "incentiveObjectPid": "string",
  "incentiveObjectName": "string",
  "contractAccountPid": "string",
  "contractAccountName": "string",
  "contractMerchantMid": "string",
  "contractMerchantName": "string",
  "settlementAccountPid": "string",
  "settlementAccountName": "string",
  "settlementMerchantMid": "string",
  "settlementMerchantName": "string",
  "payMerchantId": "string",
  "payMerchantName": "string",
  "productCode": "string",
  "billMonth": "string",
  "providerPid": "string",
  "writeOffMonth": "string",
  "writeOffAmount": 0,
  "settlementAmount": 0,
  "settlement": "string",
  "settlementRemark": "string",
  "bankRemark": "string",
  "bankMonth": "string",
  "bankAmount": 0,
  "diffResult": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|policyNo|string|false|none||政策号|
|policyName|string|false|none||政策名称|
|bizCycle|string|false|none||业务周期|
|incentiveObjectPid|string|false|none||激励对象pid|
|incentiveObjectName|string|false|none||激励对象名称|
|contractAccountPid|string|false|none||签约商户pid|
|contractAccountName|string|false|none||签约账号名称|
|contractMerchantMid|string|false|none||签约商户mid|
|contractMerchantName|string|false|none||签约商户名称|
|settlementAccountPid|string|false|none||结算商户pid|
|settlementAccountName|string|false|none||结算账号名称|
|settlementMerchantMid|string|false|none||结算商户mid|
|settlementMerchantName|string|false|none||结算商户名称|
|payMerchantId|string|false|none||付款商户|
|payMerchantName|string|false|none||付款商户名称|
|productCode|string|false|none||产品码|
|billMonth|string|false|none||账单月份|
|providerPid|string|false|none||服务商pid|
|writeOffMonth|string|false|none||核销月份|
|writeOffAmount|number|false|none||核销金额|
|settlementAmount|number|false|none||结算金额|
|settlement|string|false|none||是否结算|
|settlementRemark|string|false|none||不结算原因|
|bankRemark|string|false|none||银行流水备注|
|bankMonth|string|false|none||银行匹配账单月份|
|bankAmount|number|false|none||银行贷方金额|
|diffResult|string|false|none||比对结果 01:一致 02::不一致 03:未匹配到数据|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultAlipayBdBillVO">PaginationResultAlipayBdBillVO</h2>

<a id="schemapaginationresultalipaybdbillvo"></a>
<a id="schema_PaginationResultAlipayBdBillVO"></a>
<a id="tocSpaginationresultalipaybdbillvo"></a>
<a id="tocspaginationresultalipaybdbillvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "policyNo": "string",
      "policyName": "string",
      "bizCycle": "string",
      "incentiveObjectPid": "string",
      "incentiveObjectName": "string",
      "contractAccountPid": "string",
      "contractAccountName": "string",
      "contractMerchantMid": "string",
      "contractMerchantName": "string",
      "settlementAccountPid": "string",
      "settlementAccountName": "string",
      "settlementMerchantMid": "string",
      "settlementMerchantName": "string",
      "payMerchantId": "string",
      "payMerchantName": "string",
      "productCode": "string",
      "billMonth": "string",
      "providerPid": "string",
      "writeOffMonth": "string",
      "writeOffAmount": 0,
      "settlementAmount": 0,
      "settlement": "string",
      "settlementRemark": "string",
      "bankRemark": "string",
      "bankMonth": "string",
      "bankAmount": 0,
      "diffResult": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[AlipayBdBillVO](#schemaalipaybdbillvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_AlipayBdBillQueryDTO">AlipayBdBillQueryDTO</h2>

<a id="schemaalipaybdbillquerydto"></a>
<a id="schema_AlipayBdBillQueryDTO"></a>
<a id="tocSalipaybdbillquerydto"></a>
<a id="tocsalipaybdbillquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "settlementAccountPid": "string",
  "diffResult": "string",
  "settlementAccountName": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|idList|[integer]|false|none||主键id|
|settlementAccountPid|string|false|none||结算商户pid|
|diffResult|string|false|none||比对结果 00:不一致 01:一致|
|settlementAccountName|string|false|none||结算账号名称|
|billMonthStart|string|false|none||账单月份|
|billMonthEnd|string|false|none||none|

<h2 id="tocS_NewChargePowerVO">NewChargePowerVO</h2>

<a id="schemanewchargepowervo"></a>
<a id="schema_NewChargePowerVO"></a>
<a id="tocSnewchargepowervo"></a>
<a id="tocsnewchargepowervo"></a>

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|returnAmount|number|false|none||回票金额(元)|
|reconciliationPerson|string|false|none||对账负责人|
|billCheck|string|false|none||账单核对|
|counterpartyConfirmation|string|false|none||对方确认|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|
|mode|string|false|none||模式|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargeOnlineVO">NewChargeOnlineVO</h2>

<a id="schemanewchargeonlinevo"></a>
<a id="schema_NewChargeOnlineVO"></a>
<a id="tocSnewchargeonlinevo"></a>
<a id="tocsnewchargeonlinevo"></a>

```json
{
  "id": 0,
  "operatorName": "string",
  "billDate": "string",
  "billMonth": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string",
  "settlementMethod": "string",
  "chargingDegree": 0,
  "chargingAmount": 0,
  "chargingSettlementRatio": "string",
  "dischargingDegree": 0,
  "dischargingAmount": 0,
  "dischargingStationRatio": "string",
  "adjustedChargingFee": 0,
  "adjustedDischargingFee": 0,
  "difference": 0,
  "platformPayable": 0,
  "platformReceivable": 0,
  "platformPendingPay": 0,
  "platformPendingReceive": 0,
  "paymentStatus": "string",
  "platformActualReceive": 0,
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|operatorName|string|false|none||运营商名称|
|billDate|string|false|none||账单日|
|billMonth|string|false|none||账单月|
|billMonthStart|string|false|none||账单月开始|
|billMonthEnd|string|false|none||账单月结束|
|settlementMethod|string|false|none||结算方式|
|chargingDegree|number|false|none||充电量度数|
|chargingAmount|number|false|none||充电量金额|
|chargingSettlementRatio|string|false|none||充电结算比例|
|dischargingDegree|number|false|none||放电量度数|
|dischargingAmount|number|false|none||放电量金额|
|dischargingStationRatio|string|false|none||放电结算比例|
|adjustedChargingFee|number|false|none||调整充电费|
|adjustedDischargingFee|number|false|none||调整放电费|
|difference|number|false|none||税差|
|platformPayable|number|false|none||平台应付|
|platformReceivable|number|false|none||平台应收|
|platformPendingPay|number|false|none||平台待付|
|platformPendingReceive|number|false|none||平台待收|
|paymentStatus|string|false|none||付款状态|
|platformActualReceive|number|false|none||轧差后平台实收|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_AlipayOrgBillVO">AlipayOrgBillVO</h2>

<a id="schemaalipayorgbillvo"></a>
<a id="schema_AlipayOrgBillVO"></a>
<a id="tocSalipayorgbillvo"></a>
<a id="tocsalipayorgbillvo"></a>

```json
{
  "id": 0,
  "billNo": "string",
  "ou": "string",
  "mid": "string",
  "pid": "string",
  "merchantName": "string",
  "billCycle": "string",
  "productCode": "string",
  "productName": "string",
  "billingItem": "string",
  "contractNo": "string",
  "billingAmount": 0,
  "originAmount": 0,
  "unpaidAmount": 0,
  "unbilledAmount": 0,
  "unsureAmount": 0,
  "unsettledAmount": 0,
  "billStatus": "string",
  "alipayAccount": "string",
  "pointType": "string",
  "adjustAmount": 0,
  "shouldPayAmount": 0,
  "settleStatus": "string",
  "billingCount": 0,
  "billingOrgCode": "string",
  "billingOrgName": "string",
  "contractPid": "string",
  "contractName": "string",
  "contractEmail": "string",
  "createBy": "string",
  "createTime": "string",
  "tenantId": 0,
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billNo|string|false|none||账单编码|
|ou|string|false|none||OU|
|mid|string|false|none||MID|
|pid|string|false|none||PID|
|merchantName|string|false|none||商户名称|
|billCycle|string|false|none||账单周期|
|productCode|string|false|none||产品码|
|productName|string|false|none||产品名称|
|billingItem|string|false|none||计费项|
|contractNo|string|false|none||合约编号|
|billingAmount|number|false|none||计费量|
|originAmount|number|false|none||原账单金额（元）|
|unpaidAmount|number|false|none||未出账金额（元）|
|unbilledAmount|number|false|none||未开票金额（元）|
|unsureAmount|number|false|none||未确认金额（元）|
|unsettledAmount|number|false|none||未结算金额（元）|
|billStatus|string|false|none||账单状态|
|alipayAccount|string|false|none||支付宝账号|
|pointType|string|false|none||结算时点类型|
|adjustAmount|number|false|none||调账金额（元）|
|shouldPayAmount|number|false|none||应结算（元）|
|settleStatus|string|false|none||结算状态|
|billingCount|integer|false|none||计费笔数|
|billingOrgCode|string|false|none||出账机构简称|
|billingOrgName|string|false|none||出账机构名称|
|contractPid|string|false|none||签约PID|
|contractName|string|false|none||签约商户名称|
|contractEmail|string|false|none||签约PID邮箱|
|createBy|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|tenantId|integer(int64)|false|none||租户号|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultNewChargePowerVO">PaginationResultNewChargePowerVO</h2>

<a id="schemapaginationresultnewchargepowervo"></a>
<a id="schema_PaginationResultNewChargePowerVO"></a>
<a id="tocSpaginationresultnewchargepowervo"></a>
<a id="tocspaginationresultnewchargepowervo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "billYearMonth": "string",
      "operator": "string",
      "returnAmount": 0,
      "reconciliationPerson": "string",
      "billCheck": "string",
      "counterpartyConfirmation": "string",
      "counterpartySeal": "string",
      "ourSeal": "string",
      "returnComplete": "string",
      "remarks": "string",
      "mode": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargePowerVO](#schemanewchargepowervo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PaginationResultNewChargeOnlineVO">PaginationResultNewChargeOnlineVO</h2>

<a id="schemapaginationresultnewchargeonlinevo"></a>
<a id="schema_PaginationResultNewChargeOnlineVO"></a>
<a id="tocSpaginationresultnewchargeonlinevo"></a>
<a id="tocspaginationresultnewchargeonlinevo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "operatorName": "string",
      "billDate": "string",
      "billMonth": "string",
      "billMonthStart": "string",
      "billMonthEnd": "string",
      "settlementMethod": "string",
      "chargingDegree": 0,
      "chargingAmount": 0,
      "chargingSettlementRatio": "string",
      "dischargingDegree": 0,
      "dischargingAmount": 0,
      "dischargingStationRatio": "string",
      "adjustedChargingFee": 0,
      "adjustedDischargingFee": 0,
      "difference": 0,
      "platformPayable": 0,
      "platformReceivable": 0,
      "platformPendingPay": 0,
      "platformPendingReceive": 0,
      "paymentStatus": "string",
      "platformActualReceive": 0,
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeOnlineVO](#schemanewchargeonlinevo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PaginationResultAlipayOrgBillVO">PaginationResultAlipayOrgBillVO</h2>

<a id="schemapaginationresultalipayorgbillvo"></a>
<a id="schema_PaginationResultAlipayOrgBillVO"></a>
<a id="tocSpaginationresultalipayorgbillvo"></a>
<a id="tocspaginationresultalipayorgbillvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "billNo": "string",
      "ou": "string",
      "mid": "string",
      "pid": "string",
      "merchantName": "string",
      "billCycle": "string",
      "productCode": "string",
      "productName": "string",
      "billingItem": "string",
      "contractNo": "string",
      "billingAmount": 0,
      "originAmount": 0,
      "unpaidAmount": 0,
      "unbilledAmount": 0,
      "unsureAmount": 0,
      "unsettledAmount": 0,
      "billStatus": "string",
      "alipayAccount": "string",
      "pointType": "string",
      "adjustAmount": 0,
      "shouldPayAmount": 0,
      "settleStatus": "string",
      "billingCount": 0,
      "billingOrgCode": "string",
      "billingOrgName": "string",
      "contractPid": "string",
      "contractName": "string",
      "contractEmail": "string",
      "createBy": "string",
      "createTime": "string",
      "tenantId": 0,
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[AlipayOrgBillVO](#schemaalipayorgbillvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargePowerQueryDTO">NewChargePowerQueryDTO</h2>

<a id="schemanewchargepowerquerydto"></a>
<a id="schema_NewChargePowerQueryDTO"></a>
<a id="tocSnewchargepowerquerydto"></a>
<a id="tocsnewchargepowerquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "counterpartyConfirmation": "string",
  "ourSeal": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "billCheck": "string",
  "returnComplete": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|operator|string|false|none||运营商|
|counterpartyConfirmation|string|false|none||对方确认状态|
|ourSeal|string|false|none||我方盖章状态|
|billYearMonthStart|string|false|none||账单年月开始|
|billYearMonthEnd|string|false|none||账单年月结束|
|billCheck|string|false|none||账单核对状态|
|returnComplete|string|false|none||回票完成状态|

<h2 id="tocS_NewChargeOnlineQueryDTO">NewChargeOnlineQueryDTO</h2>

<a id="schemanewchargeonlinequerydto"></a>
<a id="schema_NewChargeOnlineQueryDTO"></a>
<a id="tocSnewchargeonlinequerydto"></a>
<a id="tocsnewchargeonlinequerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementMethod": "string",
  "paymentStatus": "string",
  "billMonthStart": "string",
  "billMonthEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||运营商|
|settlementMethod|string|false|none||结算方式|
|paymentStatus|string|false|none||付款状态|
|billMonthStart|string|false|none||账单月开始|
|billMonthEnd|string|false|none||账单月结束|

<h2 id="tocS_AlipayOrgBillQueryDTO">AlipayOrgBillQueryDTO</h2>

<a id="schemaalipayorgbillquerydto"></a>
<a id="schema_AlipayOrgBillQueryDTO"></a>
<a id="tocSalipayorgbillquerydto"></a>
<a id="tocsalipayorgbillquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billNo": "string",
  "billStatus": "string",
  "settleStatus": "string",
  "billCycleStart": "string",
  "billCycleEnd": "string",
  "pid": "string",
  "merchantName": "string",
  "pointType": "string",
  "alipayAccount": "string",
  "billingOrgCode": "string",
  "billingOrgName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|billNo|string|false|none||账单编码|
|billStatus|string|false|none||账单状态|
|settleStatus|string|false|none||结算状态|
|billCycleStart|string|false|none||账单周期开始|
|billCycleEnd|string|false|none||账单周期结束|
|pid|string|false|none||PID|
|merchantName|string|false|none||商户名称|
|pointType|string|false|none||结算时点类型|
|alipayAccount|string|false|none||支付宝账号|
|billingOrgCode|string|false|none||出账机构简称|
|billingOrgName|string|false|none||出账机构名称|

<h2 id="tocS_NewChargePowerImportDTO">NewChargePowerImportDTO</h2>

<a id="schemanewchargepowerimportdto"></a>
<a id="schema_NewChargePowerImportDTO"></a>
<a id="tocSnewchargepowerimportdto"></a>
<a id="tocsnewchargepowerimportdto"></a>

```json
{
  "billYearMonth": "string",
  "operator": "string",
  "returnAmount": 0,
  "reconciliationPerson": "string",
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string",
  "mode": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|returnAmount|number|false|none||回票金额(元)|
|reconciliationPerson|string|false|none||对账负责人|
|billCheck|string|false|none||账单核对|
|counterpartyConfirmation|string|false|none||对方确认|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|
|mode|string|false|none||模式|

<h2 id="tocS_BdProviderBillVO">BdProviderBillVO</h2>

<a id="schemabdproviderbillvo"></a>
<a id="schema_BdProviderBillVO"></a>
<a id="tocSbdproviderbillvo"></a>
<a id="tocsbdproviderbillvo"></a>

```json
{
  "id": 0,
  "partnerName": "string",
  "instituteName": "string",
  "mid": "string",
  "accountDay": "string",
  "settlementDay": "string",
  "onlineDay": "string",
  "aliShouldPayAmount": 0,
  "aliActualPayAmount": 0,
  "spShouldPayAmount": 0,
  "spActualPayAmount": 0,
  "rebate": 0,
  "status": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|partnerName|string|false|none||合作伙伴名|
|instituteName|string|false|none||机构名称|
|mid|string|false|none||MID-Merchant_no|
|accountDay|string|false|none||账户日期account_day|
|settlementDay|string|false|none||结算期(month)|
|onlineDay|string|false|none||上线日期online_date(day)|
|aliShouldPayAmount|number|false|none||支付宝应收|
|aliActualPayAmount|number|false|none||支付宝实收|
|spShouldPayAmount|number|false|none||服务商应收|
|spActualPayAmount|number|false|none||服务商实收|
|rebate|number|false|none||返佣比例|
|status|string|false|none||结算状态|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargePowerOperationDTO">NewChargePowerOperationDTO</h2>

<a id="schemanewchargepoweroperationdto"></a>
<a id="schema_NewChargePowerOperationDTO"></a>
<a id="tocSnewchargepoweroperationdto"></a>
<a id="tocsnewchargepoweroperationdto"></a>

```json
{
  "idList": [
    0
  ],
  "billCheck": "string",
  "counterpartyConfirmation": "string",
  "counterpartySeal": "string",
  "ourSeal": "string",
  "returnComplete": "string",
  "remarks": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|idList|[integer]|false|none||id列表|
|billCheck|string|false|none||账单核对|
|counterpartyConfirmation|string|false|none||对方确认|
|counterpartySeal|string|false|none||对方盖章|
|ourSeal|string|false|none||我方盖章|
|returnComplete|string|false|none||回票完成|
|remarks|string|false|none||备注|

<h2 id="tocS_PaginationResultBdProviderBillVO">PaginationResultBdProviderBillVO</h2>

<a id="schemapaginationresultbdproviderbillvo"></a>
<a id="schema_PaginationResultBdProviderBillVO"></a>
<a id="tocSpaginationresultbdproviderbillvo"></a>
<a id="tocspaginationresultbdproviderbillvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "partnerName": "string",
      "instituteName": "string",
      "mid": "string",
      "accountDay": "string",
      "settlementDay": "string",
      "onlineDay": "string",
      "aliShouldPayAmount": 0,
      "aliActualPayAmount": 0,
      "spShouldPayAmount": 0,
      "spActualPayAmount": 0,
      "rebate": 0,
      "status": "string",
      "updateBy": "string",
      "updateTime": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[BdProviderBillVO](#schemabdproviderbillvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_BdProviderBillQueryDTO">BdProviderBillQueryDTO</h2>

<a id="schemabdproviderbillquerydto"></a>
<a id="schema_BdProviderBillQueryDTO"></a>
<a id="tocSbdproviderbillquerydto"></a>
<a id="tocsbdproviderbillquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ],
  "partnerName": "string",
  "status": "string",
  "settlementDayStart": "string",
  "settlementDayEnd": "string",
  "instituteName": "string",
  "mid": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|idList|[integer]|false|none||id列表|
|partnerName|string|false|none||合作伙伴名|
|status|string|false|none||结算状态|
|settlementDayStart|string|false|none||结算周期(month)开始|
|settlementDayEnd|string|false|none||结算周期(month)结束|
|instituteName|string|false|none||机构名称|
|mid|string|false|none||MID-Merchant_no|

<h2 id="tocS_InstitutionInfoVO">InstitutionInfoVO</h2>

<a id="schemainstitutioninfovo"></a>
<a id="schema_InstitutionInfoVO"></a>
<a id="tocSinstitutioninfovo"></a>
<a id="tocsinstitutioninfovo"></a>

```json
{
  "id": 0,
  "institutionName": "string",
  "institutionCode": "string",
  "billingInstitutionCode": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "businessType": "string",
  "collectionInstitution": "string",
  "primaryServiceProvider": "string",
  "secondaryServiceProvider": "string",
  "onlineStatus": "string",
  "auditStatus": "string",
  "goLiveTime": "string",
  "settlementRequirements": "string",
  "collectionRequirements": "string",
  "handlingFee": "string",
  "handlingFeeUrl": "string",
  "collectionReceipt": "string",
  "collectionReceiptUrl": "string",
  "alipayBd": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|institutionName|string|false|none||机构名称|
|institutionCode|string|false|none||机构编码|
|billingInstitutionCode|string|false|none||出账机构编码|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|businessType|string|false|none||业务类型|
|collectionInstitution|string|false|none||销账机构|
|primaryServiceProvider|string|false|none||一级服务商|
|secondaryServiceProvider|string|false|none||二级服务商|
|onlineStatus|string|false|none||上线状态|
|auditStatus|string|false|none||审核状态|
|goLiveTime|string|false|none||上线时间|
|settlementRequirements|string|false|none||结算单要求|
|collectionRequirements|string|false|none||收据要求|
|handlingFee|string|false|none||手续函|
|handlingFeeUrl|string|false|none||手续函url|
|collectionReceipt|string|false|none||收据函|
|collectionReceiptUrl|string|false|none||收据函url|
|alipayBd|string|false|none||支付宝BD|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultInstitutionInfoVO">PaginationResultInstitutionInfoVO</h2>

<a id="schemapaginationresultinstitutioninfovo"></a>
<a id="schema_PaginationResultInstitutionInfoVO"></a>
<a id="tocSpaginationresultinstitutioninfovo"></a>
<a id="tocspaginationresultinstitutioninfovo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "institutionName": "string",
      "institutionCode": "string",
      "billingInstitutionCode": "string",
      "settlementInstitutionName": "string",
      "settlementPid": "string",
      "businessType": "string",
      "collectionInstitution": "string",
      "primaryServiceProvider": "string",
      "secondaryServiceProvider": "string",
      "onlineStatus": "string",
      "auditStatus": "string",
      "goLiveTime": "string",
      "settlementRequirements": "string",
      "collectionRequirements": "string",
      "handlingFee": "string",
      "handlingFeeUrl": "string",
      "collectionReceipt": "string",
      "collectionReceiptUrl": "string",
      "alipayBd": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[InstitutionInfoVO](#schemainstitutioninfovo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_InstitutionInfoQueryDTO">InstitutionInfoQueryDTO</h2>

<a id="schemainstitutioninfoquerydto"></a>
<a id="schema_InstitutionInfoQueryDTO"></a>
<a id="tocSinstitutioninfoquerydto"></a>
<a id="tocsinstitutioninfoquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "institutionName": "string",
  "businessType": "string",
  "onlineStatus": "string",
  "serviceProvider": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|institutionName|string|false|none||机构名称|
|businessType|string|false|none||业务类型|
|onlineStatus|string|false|none||上线状态|
|serviceProvider|string|false|none||服务商|

<h2 id="tocS_InstitutionInfoEditDTO">InstitutionInfoEditDTO</h2>

<a id="schemainstitutioninfoeditdto"></a>
<a id="schema_InstitutionInfoEditDTO"></a>
<a id="tocSinstitutioninfoeditdto"></a>
<a id="tocsinstitutioninfoeditdto"></a>

```json
{
  "id": 0,
  "settlementRequirements": "string",
  "collectionRequirements": "string",
  "handlingFee": "string",
  "handlingFeeUrl": "string",
  "collectionReceipt": "string",
  "collectionReceiptUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|settlementRequirements|string|false|none||结算单要求|
|collectionRequirements|string|false|none||收据要求|
|handlingFee|string|false|none||手续函名称|
|handlingFeeUrl|string|false|none||手续函url|
|collectionReceipt|string|false|none||收据函名称|
|collectionReceiptUrl|string|false|none||收据函url|

<h2 id="tocS_ServiceProviderVO">ServiceProviderVO</h2>

<a id="schemaserviceprovidervo"></a>
<a id="schema_ServiceProviderVO"></a>
<a id="tocSserviceprovidervo"></a>
<a id="tocsserviceprovidervo"></a>

```json
{
  "id": 0,
  "serviceProvider": "string",
  "agreementType": "string",
  "billingInstitution": "string",
  "mid": "string",
  "pid": "string",
  "goLiveTime": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "lastCommissionRatio": 0,
  "rate": 0,
  "isDiscounted": "string",
  "currCommissionRatio": 0,
  "remark": "string",
  "status": "string",
  "archiveTime": "string",
  "originProvider": "string",
  "billingOrg": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|serviceProvider|string|false|none||服务商|
|agreementType|string|false|none||协议类型|
|billingInstitution|string|false|none||出账机构|
|mid|string|false|none||mid|
|pid|string|false|none||pid|
|goLiveTime|string|false|none||上线时间|
|commissionStartTime|string|false|none||返佣起始时间|
|commissionEndTime|string|false|none||返佣截止时间|
|lastCommissionRatio|number|false|none||上年协议返佣比例|
|rate|number|false|none||费率|
|isDiscounted|string|false|none||是否打折|
|currCommissionRatio|number|false|none||今年协议返佣比例|
|remark|string|false|none||备注|
|status|string|false|none||状态|
|archiveTime|string|false|none||归档时间|
|originProvider|string|false|none||原服务商|
|billingOrg|string|false|none||出账机构|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultServiceProviderVO">PaginationResultServiceProviderVO</h2>

<a id="schemapaginationresultserviceprovidervo"></a>
<a id="schema_PaginationResultServiceProviderVO"></a>
<a id="tocSpaginationresultserviceprovidervo"></a>
<a id="tocspaginationresultserviceprovidervo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "serviceProvider": "string",
      "agreementType": "string",
      "billingInstitution": "string",
      "mid": "string",
      "pid": "string",
      "goLiveTime": "string",
      "commissionStartTime": "string",
      "commissionEndTime": "string",
      "lastCommissionRatio": 0,
      "rate": 0,
      "isDiscounted": "string",
      "currCommissionRatio": 0,
      "remark": "string",
      "status": "string",
      "archiveTime": "string",
      "originProvider": "string",
      "billingOrg": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[ServiceProviderVO](#schemaserviceprovidervo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_ServiceProviderQueryDTO">ServiceProviderQueryDTO</h2>

<a id="schemaserviceproviderquerydto"></a>
<a id="schema_ServiceProviderQueryDTO"></a>
<a id="tocSserviceproviderquerydto"></a>
<a id="tocsserviceproviderquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "serviceProvider": "string",
  "mid": "string",
  "pid": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "billingOrg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|serviceProvider|string|false|none||服务商|
|mid|string|false|none||mid|
|pid|string|false|none||pid|
|commissionStartTime|string|false|none||返佣起始时间|
|commissionEndTime|string|false|none||返佣截止时间|
|billingOrg|string|false|none||出账机构|

<h2 id="tocS_NewChargeStationVO">NewChargeStationVO</h2>

<a id="schemanewchargestationvo"></a>
<a id="schema_NewChargeStationVO"></a>
<a id="tocSnewchargestationvo"></a>
<a id="tocsnewchargestationvo"></a>

```json
{
  "id": 0,
  "billYearMonth": "string",
  "operator": "string",
  "siteName": "string",
  "investmentMode": "string",
  "settlementMethod": "string",
  "discount": "string",
  "chargingAmount": 0,
  "actualChargingFee": 0,
  "dischargingAmount": 0,
  "actualDischargingFee": 0,
  "powerLossRate": "string",
  "income": 0,
  "payableAmount": 0,
  "receivableAmount": 0,
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商，字典：st_new_charge_station|
|siteName|string|false|none||站点名称|
|investmentMode|string|false|none||投资模式，字典：st_new_charge_station_invest|
|settlementMethod|string|false|none||结算方式，字典：st_new_charge_station_pay_method|
|discount|string|false|none||折扣|
|chargingAmount|number|false|none||充电量|
|actualChargingFee|number|false|none||实际充电费|
|dischargingAmount|number|false|none||放电量|
|actualDischargingFee|number|false|none||实际放电费|
|powerLossRate|string|false|none||电损率|
|income|number|false|none||收益(元)|
|payableAmount|number|false|none||应付金额(元)|
|receivableAmount|number|false|none||应回金额(元)|
|billingDate|string|false|none||出账日期|
|paymentDate|string|false|none||回款日期|
|settlementStatus|string|false|none||结算进度，字典：st_new_charge_station_pay_status|
|remarks|string|false|none||备注|
|reconciliationPerson|string|false|none||对账负责人|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_ServiceProviderEditDTO">ServiceProviderEditDTO</h2>

<a id="schemaserviceprovidereditdto"></a>
<a id="schema_ServiceProviderEditDTO"></a>
<a id="tocSserviceprovidereditdto"></a>
<a id="tocsserviceprovidereditdto"></a>

```json
{
  "id": 0,
  "serviceProvider": "string",
  "agreementType": "string",
  "billingInstitution": "string",
  "mid": "string",
  "pid": "string",
  "goLiveTime": "string",
  "commissionStartTime": "string",
  "commissionEndTime": "string",
  "lastCommissionRatio": 0,
  "rate": 0,
  "isDiscounted": "string",
  "currCommissionRatio": 0,
  "remark": "string",
  "status": "string",
  "archiveTime": "string",
  "originProvider": "string",
  "billingOrg": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|serviceProvider|string|false|none||服务商|
|agreementType|string|false|none||协议类型|
|billingInstitution|string|false|none||出账机构|
|mid|string|false|none||mid|
|pid|string|false|none||pid|
|goLiveTime|string|false|none||上线时间|
|commissionStartTime|string|false|none||返佣起始时间|
|commissionEndTime|string|false|none||返佣截止时间|
|lastCommissionRatio|number|false|none||上年协议返佣比例|
|rate|number|false|none||费率|
|isDiscounted|string|false|none||是否打折|
|currCommissionRatio|number|false|none||今年协议返佣比例|
|remark|string|false|none||备注|
|status|string|false|none||状态|
|archiveTime|string|false|none||归档时间|
|originProvider|string|false|none||原服务商|
|billingOrg|string|false|none||出账机构|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultNewChargeStationVO">PaginationResultNewChargeStationVO</h2>

<a id="schemapaginationresultnewchargestationvo"></a>
<a id="schema_PaginationResultNewChargeStationVO"></a>
<a id="tocSpaginationresultnewchargestationvo"></a>
<a id="tocspaginationresultnewchargestationvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "billYearMonth": "string",
      "operator": "string",
      "siteName": "string",
      "investmentMode": "string",
      "settlementMethod": "string",
      "discount": "string",
      "chargingAmount": 0,
      "actualChargingFee": 0,
      "dischargingAmount": 0,
      "actualDischargingFee": 0,
      "powerLossRate": "string",
      "income": 0,
      "payableAmount": 0,
      "receivableAmount": 0,
      "billingDate": "string",
      "paymentDate": "string",
      "settlementStatus": "string",
      "remarks": "string",
      "reconciliationPerson": "string",
      "updateBy": "string",
      "updateTime": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeStationVO](#schemanewchargestationvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_AlipayOrgReceiptVO">AlipayOrgReceiptVO</h2>

<a id="schemaalipayorgreceiptvo"></a>
<a id="schema_AlipayOrgReceiptVO"></a>
<a id="tocSalipayorgreceiptvo"></a>
<a id="tocsalipayorgreceiptvo"></a>

```json
{
  "id": 0,
  "merchantName": "string",
  "merchantLoginAccount": "string",
  "pid": "string",
  "startMonth": "string",
  "endMonth": "string",
  "generatedDate": "string",
  "invoiceAmount": 0,
  "collectionRequirements": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|merchantName|string|false|none||商户名称|
|merchantLoginAccount|string|false|none||商户登录账号|
|pid|string|false|none||PID|
|startMonth|string|false|none||起始年月|
|endMonth|string|false|none||截止年月|
|generatedDate|string|false|none||生成日期|
|invoiceAmount|number|false|none||开票金额|
|collectionRequirements|string|false|none||收据要求|
|shippingAddress|string|false|none||寄送地址|
|recipient|string|false|none||收件人|
|phone|string|false|none||电话|
|remark|string|false|none||备注|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargeStationQueryDTO">NewChargeStationQueryDTO</h2>

<a id="schemanewchargestationquerydto"></a>
<a id="schema_NewChargeStationQueryDTO"></a>
<a id="tocSnewchargestationquerydto"></a>
<a id="tocsnewchargestationquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string",
  "operator": "string",
  "siteName": "string",
  "settlementMethod": "string",
  "settlementStatus": "string",
  "investmentMode": "string",
  "reconciliationPerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|billYearMonthStart|string|false|none||账单年月开始|
|billYearMonthEnd|string|false|none||账单年月结束|
|operator|string|false|none||运营商|
|siteName|string|false|none||站点名称|
|settlementMethod|string|false|none||结算方式|
|settlementStatus|string|false|none||结算进度|
|investmentMode|string|false|none||投资模式|
|reconciliationPerson|string|false|none||对账人|

<h2 id="tocS_PaginationResultAlipayOrgReceiptVO">PaginationResultAlipayOrgReceiptVO</h2>

<a id="schemapaginationresultalipayorgreceiptvo"></a>
<a id="schema_PaginationResultAlipayOrgReceiptVO"></a>
<a id="tocSpaginationresultalipayorgreceiptvo"></a>
<a id="tocspaginationresultalipayorgreceiptvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "merchantName": "string",
      "merchantLoginAccount": "string",
      "pid": "string",
      "startMonth": "string",
      "endMonth": "string",
      "generatedDate": "string",
      "invoiceAmount": 0,
      "collectionRequirements": "string",
      "shippingAddress": "string",
      "recipient": "string",
      "phone": "string",
      "remark": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[AlipayOrgReceiptVO](#schemaalipayorgreceiptvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargeStationImportDTO">NewChargeStationImportDTO</h2>

<a id="schemanewchargestationimportdto"></a>
<a id="schema_NewChargeStationImportDTO"></a>
<a id="tocSnewchargestationimportdto"></a>
<a id="tocsnewchargestationimportdto"></a>

```json
{
  "billYearMonth": "string",
  "operator": "string",
  "siteName": "string",
  "investmentMode": "string",
  "settlementMethod": "string",
  "discount": "string",
  "chargingAmount": 0,
  "actualChargingFee": 0,
  "dischargingAmount": 0,
  "actualDischargingFee": 0,
  "powerLossRate": "string",
  "income": 0,
  "payableAmount": 0,
  "receivableAmount": 0,
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "remarks": "string",
  "reconciliationPerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|billYearMonth|string|false|none||账单年月|
|operator|string|false|none||运营商|
|siteName|string|false|none||站点名称|
|investmentMode|string|false|none||投资模式|
|settlementMethod|string|false|none||结算方式|
|discount|string|false|none||折扣|
|chargingAmount|number|false|none||充电量|
|actualChargingFee|number|false|none||实际充电费|
|dischargingAmount|number|false|none||放电量|
|actualDischargingFee|number|false|none||实际放电费|
|powerLossRate|string|false|none||电损率|
|income|number|false|none||收益(元)|
|payableAmount|number|false|none||应付金额(元)|
|receivableAmount|number|false|none||应回金额(元)|
|billingDate|string|false|none||出账日期|
|paymentDate|string|false|none||回款日期|
|settlementStatus|string|false|none||结算进度|
|remarks|string|false|none||备注|
|reconciliationPerson|string|false|none||对账负责人|

<h2 id="tocS_AlipayOrgReceiptQueryDTO">AlipayOrgReceiptQueryDTO</h2>

<a id="schemaalipayorgreceiptquerydto"></a>
<a id="schema_AlipayOrgReceiptQueryDTO"></a>
<a id="tocSalipayorgreceiptquerydto"></a>
<a id="tocsalipayorgreceiptquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "merchantName": "string",
  "merchantLoginAccount": "string",
  "pid": "string",
  "startMonth": "string",
  "endMonth": "string",
  "createdDateStart": "string",
  "createdDateEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|merchantName|string|false|none||商户名称|
|merchantLoginAccount|string|false|none||商户登录账号|
|pid|string|false|none||PID|
|startMonth|string|false|none||起始和截止年月开始|
|endMonth|string|false|none||起始和截止年月结束|
|createdDateStart|string|false|none||生成日期开始|
|createdDateEnd|string|false|none||生成日期结束|

<h2 id="tocS_NewChargeStationOperationDTO">NewChargeStationOperationDTO</h2>

<a id="schemanewchargestationoperationdto"></a>
<a id="schema_NewChargeStationOperationDTO"></a>
<a id="tocSnewchargestationoperationdto"></a>
<a id="tocsnewchargestationoperationdto"></a>

```json
{
  "idList": [
    0
  ],
  "settlementMethod": "string",
  "discount": "string",
  "billingDate": "string",
  "paymentDate": "string",
  "settlementStatus": "string",
  "reconciliationPerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|idList|[integer]|false|none||id集合|
|settlementMethod|string|false|none||结算方式|
|discount|string|false|none||折扣|
|billingDate|string|false|none||出账日期|
|paymentDate|string|false|none||回款日期|
|settlementStatus|string|false|none||结算进度|
|reconciliationPerson|string|false|none||对账负责人|

<h2 id="tocS_AlipayOrgReceiptEditDTO">AlipayOrgReceiptEditDTO</h2>

<a id="schemaalipayorgreceipteditdto"></a>
<a id="schema_AlipayOrgReceiptEditDTO"></a>
<a id="tocSalipayorgreceipteditdto"></a>
<a id="tocsalipayorgreceipteditdto"></a>

```json
{
  "subject": "string",
  "text": "string",
  "from": "string",
  "to": "string",
  "cc": "string",
  "attachments": [
    {
      "key": "string"
    }
  ],
  "id": 0,
  "totalInvoiceAmount": 0,
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "remark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|subject|string|false|none||主题|
|text|string|false|none||正文内容|
|from|string|false|none||发件人|
|to|string|false|none||要发送的邮箱 多个人用逗号分割|
|cc|string|false|none||要抄送的邮箱 多个人用逗号分割|
|attachments|[[MapString](#schemamapstring)]|false|none||附件url|
|id|integer(int64)|false|none||主键|
|totalInvoiceAmount|number|false|none||开票金额合计|
|province|string|false|none||省份|
|provinceCode|string|false|none||省份区域编码|
|city|string|false|none||城市|
|cityCode|string|false|none||城市区域编码|
|shippingAddress|string|false|none||详细地址|
|recipient|string|false|none||收件人|
|phone|string|false|none||电话|
|remark|string|false|none||备注|

<h2 id="tocS_MapString">MapString</h2>

<a id="schemamapstring"></a>
<a id="schema_MapString"></a>
<a id="tocSmapstring"></a>
<a id="tocsmapstring"></a>

```json
{
  "key": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||none|

<h2 id="tocS_NewChargeStationPaymentVO">NewChargeStationPaymentVO</h2>

<a id="schemanewchargestationpaymentvo"></a>
<a id="schema_NewChargeStationPaymentVO"></a>
<a id="tocSnewchargestationpaymentvo"></a>
<a id="tocsnewchargestationpaymentvo"></a>

```json
{
  "id": 0,
  "operatorName": "string",
  "fundPath": "string",
  "totalReceivable": 0,
  "custodyRecoveryAmount": 0,
  "offlinePaymentAmount": 0,
  "electricityPurchaseDeduction": 0,
  "totalPaymentAmount": 0,
  "pendingPaymentAmount": 0,
  "remarks": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|operatorName|string|false|none||运营商名称|
|fundPath|string|false|none||资金路径 字典: st_new_charge_station_fund_path|
|totalReceivable|number|false|none||应收款总金额|
|custodyRecoveryAmount|number|false|none||管存回收金额|
|offlinePaymentAmount|number|false|none||线下回款金额|
|electricityPurchaseDeduction|number|false|none||购电款抵扣金额|
|totalPaymentAmount|number|false|none||合计回款金额|
|pendingPaymentAmount|number|false|none||待回款金额|
|remarks|string|false|none||备注|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_AlipayOrgSettlementVO">AlipayOrgSettlementVO</h2>

<a id="schemaalipayorgsettlementvo"></a>
<a id="schema_AlipayOrgSettlementVO"></a>
<a id="tocSalipayorgsettlementvo"></a>
<a id="tocsalipayorgsettlementvo"></a>

```json
{
  "id": 0,
  "generatedDate": "string",
  "merchantName": "string",
  "billingOrgCode": "string",
  "merchantLoginAccount": "string",
  "pid": "string",
  "startMonth": "string",
  "endMonth": "string",
  "invoiceAmount": 0,
  "settlementRequirements": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "phone": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "detailedAddress": "string",
  "settlementLeader": "string",
  "alipayBd": "string",
  "checkUrl": "string",
  "downloadUrl": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|generatedDate|string|false|none||生成日期|
|merchantName|string|false|none||商户名称|
|billingOrgCode|string|false|none||出账机构简称|
|merchantLoginAccount|string|false|none||商户登录账号|
|pid|string|false|none||PID|
|startMonth|string|false|none||起始年月|
|endMonth|string|false|none||截止年月|
|invoiceAmount|number|false|none||开票金额|
|settlementRequirements|string|false|none||结算单要求|
|shippingAddress|string|false|none||寄送地址|
|recipient|string|false|none||收件人|
|phone|string|false|none||电话|
|province|string|false|none||省份|
|city|string|false|none||城市|
|district|string|false|none||区县|
|detailedAddress|string|false|none||详细地址|
|settlementLeader|string|false|none||结算负责人|
|alipayBd|string|false|none||支付宝BD|
|checkUrl|string|false|none||查看地址|
|downloadUrl|string|false|none||下载地址|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultNewChargeStationPaymentVO">PaginationResultNewChargeStationPaymentVO</h2>

<a id="schemapaginationresultnewchargestationpaymentvo"></a>
<a id="schema_PaginationResultNewChargeStationPaymentVO"></a>
<a id="tocSpaginationresultnewchargestationpaymentvo"></a>
<a id="tocspaginationresultnewchargestationpaymentvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "operatorName": "string",
      "fundPath": "string",
      "totalReceivable": 0,
      "custodyRecoveryAmount": 0,
      "offlinePaymentAmount": 0,
      "electricityPurchaseDeduction": 0,
      "totalPaymentAmount": 0,
      "pendingPaymentAmount": 0,
      "remarks": "string",
      "updateBy": "string",
      "updateTime": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeStationPaymentVO](#schemanewchargestationpaymentvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PaginationResultAlipayOrgSettlementVO">PaginationResultAlipayOrgSettlementVO</h2>

<a id="schemapaginationresultalipayorgsettlementvo"></a>
<a id="schema_PaginationResultAlipayOrgSettlementVO"></a>
<a id="tocSpaginationresultalipayorgsettlementvo"></a>
<a id="tocspaginationresultalipayorgsettlementvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "generatedDate": "string",
      "merchantName": "string",
      "billingOrgCode": "string",
      "merchantLoginAccount": "string",
      "pid": "string",
      "startMonth": "string",
      "endMonth": "string",
      "invoiceAmount": 0,
      "settlementRequirements": "string",
      "shippingAddress": "string",
      "recipient": "string",
      "phone": "string",
      "province": "string",
      "city": "string",
      "district": "string",
      "detailedAddress": "string",
      "settlementLeader": "string",
      "alipayBd": "string",
      "checkUrl": "string",
      "downloadUrl": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[AlipayOrgSettlementVO](#schemaalipayorgsettlementvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargeStationPaymentQueryDTO">NewChargeStationPaymentQueryDTO</h2>

<a id="schemanewchargestationpaymentquerydto"></a>
<a id="schema_NewChargeStationPaymentQueryDTO"></a>
<a id="tocSnewchargestationpaymentquerydto"></a>
<a id="tocsnewchargestationpaymentquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "fundPath": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||运营商名称|
|fundPath|string|false|none||资金路径|

<h2 id="tocS_AlipayOrgSettlementQueryDTO">AlipayOrgSettlementQueryDTO</h2>

<a id="schemaalipayorgsettlementquerydto"></a>
<a id="schema_AlipayOrgSettlementQueryDTO"></a>
<a id="tocSalipayorgsettlementquerydto"></a>
<a id="tocsalipayorgsettlementquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "idList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|idList|[integer]|false|none||id列表|

<h2 id="tocS_NewChargeStationPaymentImportDTO">NewChargeStationPaymentImportDTO</h2>

<a id="schemanewchargestationpaymentimportdto"></a>
<a id="schema_NewChargeStationPaymentImportDTO"></a>
<a id="tocSnewchargestationpaymentimportdto"></a>
<a id="tocsnewchargestationpaymentimportdto"></a>

```json
{
  "operatorName": "string",
  "fundPath": "string",
  "totalReceivable": 0,
  "custodyRecoveryAmount": 0,
  "offlinePaymentAmount": 0,
  "electricityPurchaseDeduction": 0,
  "totalPaymentAmount": 0,
  "pendingPaymentAmount": 0,
  "remarks": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|operatorName|string|false|none||运营商名称|
|fundPath|string|false|none||资金路径 字典:|
|totalReceivable|number|false|none||应收款总金额|
|custodyRecoveryAmount|number|false|none||管存回收金额|
|offlinePaymentAmount|number|false|none||线下回款金额|
|electricityPurchaseDeduction|number|false|none||购电款抵扣金额|
|totalPaymentAmount|number|false|none||合计回款金额|
|pendingPaymentAmount|number|false|none||待回款金额|
|remarks|string|false|none||备注|

<h2 id="tocS_PublicServiceAdjustmentVO">PublicServiceAdjustmentVO</h2>

<a id="schemapublicserviceadjustmentvo"></a>
<a id="schema_PublicServiceAdjustmentVO"></a>
<a id="tocSpublicserviceadjustmentvo"></a>
<a id="tocspublicserviceadjustmentvo"></a>

```json
{
  "id": 0,
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "merchantMid": "string",
  "alipayAccount": "string",
  "product": "string",
  "startYearMonth": "string",
  "endYearMonth": "string",
  "invoiceAmount": 0,
  "actualBillingAmount": 0,
  "adjustmentAmount": 0,
  "adjustmentRatio": 0,
  "adjustmentProgress": "string",
  "status": "string",
  "responsiblePerson": "string",
  "adjustmentReason": "string",
  "remark": "string",
  "submissionTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|merchantMid|string|false|none||商户号MID|
|alipayAccount|string|false|none||支付宝账号|
|product|string|false|none||产品|
|startYearMonth|string|false|none||起始年月|
|endYearMonth|string|false|none||截至年月|
|invoiceAmount|number|false|none||账单金额（元）|
|actualBillingAmount|number|false|none||实际开票金额（元）|
|adjustmentAmount|number|false|none||调账金额|
|adjustmentRatio|number|false|none||调账比例|
|adjustmentProgress|string|false|none||调账进度|
|status|string|false|none||状态|
|responsiblePerson|string|false|none||负责人|
|adjustmentReason|string|false|none||调账原因|
|remark|string|false|none||备注|
|submissionTime|string|false|none||提交时间|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargeStationPaymentOperationDTO">NewChargeStationPaymentOperationDTO</h2>

<a id="schemanewchargestationpaymentoperationdto"></a>
<a id="schema_NewChargeStationPaymentOperationDTO"></a>
<a id="tocSnewchargestationpaymentoperationdto"></a>
<a id="tocsnewchargestationpaymentoperationdto"></a>

```json
{
  "idList": [
    0
  ],
  "fundPath": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|idList|[integer]|false|none||none|
|fundPath|string|false|none||none|

<h2 id="tocS_PaginationResultPublicServiceAdjustmentVO">PaginationResultPublicServiceAdjustmentVO</h2>

<a id="schemapaginationresultpublicserviceadjustmentvo"></a>
<a id="schema_PaginationResultPublicServiceAdjustmentVO"></a>
<a id="tocSpaginationresultpublicserviceadjustmentvo"></a>
<a id="tocspaginationresultpublicserviceadjustmentvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "settlementInstitutionName": "string",
      "settlementPid": "string",
      "merchantMid": "string",
      "alipayAccount": "string",
      "product": "string",
      "startYearMonth": "string",
      "endYearMonth": "string",
      "invoiceAmount": 0,
      "actualBillingAmount": 0,
      "adjustmentAmount": 0,
      "adjustmentRatio": 0,
      "adjustmentProgress": "string",
      "status": "string",
      "responsiblePerson": "string",
      "adjustmentReason": "string",
      "remark": "string",
      "submissionTime": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[PublicServiceAdjustmentVO](#schemapublicserviceadjustmentvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargeThirdVO">NewChargeThirdVO</h2>

<a id="schemanewchargethirdvo"></a>
<a id="schema_NewChargeThirdVO"></a>
<a id="tocSnewchargethirdvo"></a>
<a id="tocsnewchargethirdvo"></a>

```json
{
  "id": 0,
  "billYearMonth": "string",
  "investorName": "string",
  "operator": "string",
  "siteName": "string",
  "stationRatio": "string",
  "investorRatio": "string",
  "platformRatio": "string",
  "chargingAmount": 0,
  "chargingFee": 0,
  "dischargingAmount": 0,
  "dischargingFee": 0,
  "electricityRate": "string",
  "energyManagementIncome": 0,
  "energyManagementFee": 0,
  "assetIncome": 0,
  "platformIncome": 0,
  "paymentProgress": "string",
  "remarks": "string",
  "reconciliationPerson": "string",
  "updateBy": "string",
  "updateTime": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|billYearMonth|string|false|none||账单年月|
|investorName|string|false|none||资方名称|
|operator|string|false|none||运营商|
|siteName|string|false|none||站点名称|
|stationRatio|string|false|none||场站比例|
|investorRatio|string|false|none||资方比例|
|platformRatio|string|false|none||平台比例|
|chargingAmount|number|false|none||充电量|
|chargingFee|number|false|none||充电费|
|dischargingAmount|number|false|none||放电量|
|dischargingFee|number|false|none||放电费|
|electricityRate|string|false|none||电损率|
|energyManagementIncome|number|false|none||能源管理收益金额|
|energyManagementFee|number|false|none||能源管理服务费|
|assetIncome|number|false|none||资产收益|
|platformIncome|number|false|none||平台收入|
|paymentProgress|string|false|none||付款进度 字典：st_new_charge_station_third_status|
|remarks|string|false|none||备注|
|reconciliationPerson|string|false|none||负责人|
|updateBy|string|false|none||更新人|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PublicServiceAdjustmentQueryDTO">PublicServiceAdjustmentQueryDTO</h2>

<a id="schemapublicserviceadjustmentquerydto"></a>
<a id="schema_PublicServiceAdjustmentQueryDTO"></a>
<a id="tocSpublicserviceadjustmentquerydto"></a>
<a id="tocspublicserviceadjustmentquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "startYearMonth": "string",
  "endYearMonth": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|startYearMonth|string|false|none||账单周期开始|
|endYearMonth|string|false|none||账单周期结束|

<h2 id="tocS_PaginationResultNewChargeThirdVO">PaginationResultNewChargeThirdVO</h2>

<a id="schemapaginationresultnewchargethirdvo"></a>
<a id="schema_PaginationResultNewChargeThirdVO"></a>
<a id="tocSpaginationresultnewchargethirdvo"></a>
<a id="tocspaginationresultnewchargethirdvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "billYearMonth": "string",
      "investorName": "string",
      "operator": "string",
      "siteName": "string",
      "stationRatio": "string",
      "investorRatio": "string",
      "platformRatio": "string",
      "chargingAmount": 0,
      "chargingFee": 0,
      "dischargingAmount": 0,
      "dischargingFee": 0,
      "electricityRate": "string",
      "energyManagementIncome": 0,
      "energyManagementFee": 0,
      "assetIncome": 0,
      "platformIncome": 0,
      "paymentProgress": "string",
      "remarks": "string",
      "reconciliationPerson": "string",
      "updateBy": "string",
      "updateTime": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[NewChargeThirdVO](#schemanewchargethirdvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PublicServiceSettlementVO">PublicServiceSettlementVO</h2>

<a id="schemapublicservicesettlementvo"></a>
<a id="schema_PublicServiceSettlementVO"></a>
<a id="tocSpublicservicesettlementvo"></a>
<a id="tocspublicservicesettlementvo"></a>

```json
{
  "id": 0,
  "institutionCode": "string",
  "billingInstitutionCode": "string",
  "billingInstitutionName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "status": "string",
  "alipayAccount": "string",
  "actualSettlementMethod": "string",
  "actualSettlementCycle": "string",
  "actualSettlementMonth": "string",
  "settlementSpecialist": "string",
  "handlingFeeSettlementMethod": "string",
  "handlingFeeCalculationStandard": 0,
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|institutionCode|string|false|none||机构编码|
|billingInstitutionCode|string|false|none||出账机构编码|
|billingInstitutionName|string|false|none||出账机构名称|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|status|string|false|none||状态|
|alipayAccount|string|false|none||支付宝账号|
|actualSettlementMethod|string|false|none||实际结算方式|
|actualSettlementCycle|string|false|none||实际结算周期|
|actualSettlementMonth|string|false|none||实际结算月份|
|settlementSpecialist|string|false|none||结算专员|
|handlingFeeSettlementMethod|string|false|none||手续费结算方式|
|handlingFeeCalculationStandard|number|false|none||手续费计算标准|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_NewChargeThirdQueryDTO">NewChargeThirdQueryDTO</h2>

<a id="schemanewchargethirdquerydto"></a>
<a id="schema_NewChargeThirdQueryDTO"></a>
<a id="tocSnewchargethirdquerydto"></a>
<a id="tocsnewchargethirdquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "operator": "string",
  "investorName": "string",
  "paymentProgress": "string",
  "siteName": "string",
  "billYearMonthStart": "string",
  "billYearMonthEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|operator|string|false|none||运营商|
|investorName|string|false|none||资方名称|
|paymentProgress|string|false|none||付款进度|
|siteName|string|false|none||站点名称|
|billYearMonthStart|string|false|none||账单年月开始|
|billYearMonthEnd|string|false|none||账单年月结束|

<h2 id="tocS_PaginationResultPublicServiceSettlementVO">PaginationResultPublicServiceSettlementVO</h2>

<a id="schemapaginationresultpublicservicesettlementvo"></a>
<a id="schema_PaginationResultPublicServiceSettlementVO"></a>
<a id="tocSpaginationresultpublicservicesettlementvo"></a>
<a id="tocspaginationresultpublicservicesettlementvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "institutionCode": "string",
      "billingInstitutionCode": "string",
      "billingInstitutionName": "string",
      "settlementInstitutionName": "string",
      "settlementPid": "string",
      "status": "string",
      "alipayAccount": "string",
      "actualSettlementMethod": "string",
      "actualSettlementCycle": "string",
      "actualSettlementMonth": "string",
      "settlementSpecialist": "string",
      "handlingFeeSettlementMethod": "string",
      "handlingFeeCalculationStandard": 0,
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[PublicServiceSettlementVO](#schemapublicservicesettlementvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_NewChargeThirdImportDTO">NewChargeThirdImportDTO</h2>

<a id="schemanewchargethirdimportdto"></a>
<a id="schema_NewChargeThirdImportDTO"></a>
<a id="tocSnewchargethirdimportdto"></a>
<a id="tocsnewchargethirdimportdto"></a>

```json
{
  "billYearMonth": "string",
  "investorName": "string",
  "operator": "string",
  "siteName": "string",
  "stationRatio": "string",
  "investorRatio": "string",
  "platformRatio": "string",
  "chargingAmount": 0,
  "chargingFee": 0,
  "dischargingAmount": 0,
  "dischargingFee": 0,
  "electricityRate": "string",
  "energyManagementIncome": 0,
  "energyManagementFee": 0,
  "assetIncome": 0,
  "platformIncome": 0,
  "paymentProgress": "string",
  "remarks": "string",
  "reconciliationPerson": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|billYearMonth|string|false|none||账单年月|
|investorName|string|false|none||资方名称|
|operator|string|false|none||运营商|
|siteName|string|false|none||站点名称|
|stationRatio|string|false|none||场站比例|
|investorRatio|string|false|none||资方比例|
|platformRatio|string|false|none||平台比例|
|chargingAmount|number|false|none||充电量|
|chargingFee|number|false|none||充电费|
|dischargingAmount|number|false|none||放电量|
|dischargingFee|number|false|none||放电费|
|electricityRate|string|false|none||电损率|
|energyManagementIncome|number|false|none||能源管理收益金额|
|energyManagementFee|number|false|none||能源管理服务费|
|assetIncome|number|false|none||资产收益|
|platformIncome|number|false|none||平台收入|
|paymentProgress|string|false|none||付款进度|
|remarks|string|false|none||备注|
|reconciliationPerson|string|false|none||负责人|

<h2 id="tocS_PublicServiceSettlementQueryDTO">PublicServiceSettlementQueryDTO</h2>

<a id="schemapublicservicesettlementquerydto"></a>
<a id="schema_PublicServiceSettlementQueryDTO"></a>
<a id="tocSpublicservicesettlementquerydto"></a>
<a id="tocspublicservicesettlementquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "alipayAccount": "string",
  "status": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|alipayAccount|string|false|none||支付宝账号|
|status|string|false|none||状态|

<h2 id="tocS_NewChargeThirdOperationDTO">NewChargeThirdOperationDTO</h2>

<a id="schemanewchargethirdoperationdto"></a>
<a id="schema_NewChargeThirdOperationDTO"></a>
<a id="tocSnewchargethirdoperationdto"></a>
<a id="tocsnewchargethirdoperationdto"></a>

```json
{
  "idList": [
    0
  ],
  "paymentProgress": "string",
  "reconciliationPerson": "string",
  "remarks": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|idList|[integer]|false|none||id集合|
|paymentProgress|string|false|none||付款进度|
|reconciliationPerson|string|false|none||负责人|
|remarks|string|false|none||备注|

<h2 id="tocS_PublicServiceBillingInfoVO">PublicServiceBillingInfoVO</h2>

<a id="schemapublicservicebillinginfovo"></a>
<a id="schema_PublicServiceBillingInfoVO"></a>
<a id="tocSpublicservicebillinginfovo"></a>
<a id="tocspublicservicebillinginfovo"></a>

```json
{
  "id": 0,
  "settlementPid": "string",
  "provinceCityDistrict": "string",
  "shippingAddress": "string",
  "recipient": "string",
  "contactPhone": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|settlementPid|string|false|none||结算PID|
|provinceCityDistrict|string|false|none||省市区|
|shippingAddress|string|false|none||寄送地址|
|recipient|string|false|none||收件人|
|contactPhone|string|false|none||联系电话|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultPublicServiceBillingInfoVO">PaginationResultPublicServiceBillingInfoVO</h2>

<a id="schemapaginationresultpublicservicebillinginfovo"></a>
<a id="schema_PaginationResultPublicServiceBillingInfoVO"></a>
<a id="tocSpaginationresultpublicservicebillinginfovo"></a>
<a id="tocspaginationresultpublicservicebillinginfovo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "settlementPid": "string",
      "provinceCityDistrict": "string",
      "shippingAddress": "string",
      "recipient": "string",
      "contactPhone": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[PublicServiceBillingInfoVO](#schemapublicservicebillinginfovo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PublicServiceBillingInfoQueryDTO">PublicServiceBillingInfoQueryDTO</h2>

<a id="schemapublicservicebillinginfoquerydto"></a>
<a id="schema_PublicServiceBillingInfoQueryDTO"></a>
<a id="tocSpublicservicebillinginfoquerydto"></a>
<a id="tocspublicservicebillinginfoquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementPid": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|settlementPid|string|false|none||PID|

<h2 id="tocS_PublicServiceInvoiceInfoVO">PublicServiceInvoiceInfoVO</h2>

<a id="schemapublicserviceinvoiceinfovo"></a>
<a id="schema_PublicServiceInvoiceInfoVO"></a>
<a id="tocSpublicserviceinvoiceinfovo"></a>
<a id="tocspublicserviceinvoiceinfovo"></a>

```json
{
  "id": 0,
  "submissionDate": "string",
  "alipayMid": "string",
  "settlementInstitutionName": "string",
  "alipayAccount": "string",
  "settlementPid": "string",
  "startYearMonth": "string",
  "endYearMonth": "string",
  "billingAmount": 0,
  "responsiblePerson": "string",
  "invoiceOrTaxServiceName": "string",
  "invoiceType": "string",
  "invoiceRequirements": "string",
  "invoiceCode": "string",
  "invoiceNumber": "string",
  "billingTime": "string",
  "expressOrderNumber": "string",
  "logisticsStatus": "string",
  "paymentStatus": "string",
  "returnTicketStatus": "string",
  "statusDescription": "string",
  "status": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "recipientAddress": "string",
  "recipient": "string",
  "recipientPhone": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|submissionDate|string|false|none||提交日期|
|alipayMid|string|false|none||支付宝mid|
|settlementInstitutionName|string|false|none||结算机构名称|
|alipayAccount|string|false|none||支付宝账号|
|settlementPid|string|false|none||结算pid|
|startYearMonth|string|false|none||起始年月|
|endYearMonth|string|false|none||截至年月|
|billingAmount|number|false|none||开票金额|
|responsiblePerson|string|false|none||负责人|
|invoiceOrTaxServiceName|string|false|none||发票货物或应税劳务服务名称|
|invoiceType|string|false|none||发票类型|
|invoiceRequirements|string|false|none||开票要求|
|invoiceCode|string|false|none||发票代码|
|invoiceNumber|string|false|none||发票号|
|billingTime|string|false|none||开票时间|
|expressOrderNumber|string|false|none||快递单号|
|logisticsStatus|string|false|none||物流状态|
|paymentStatus|string|false|none||付款状态|
|returnTicketStatus|string|false|none||退票状态|
|statusDescription|string|false|none||情况说明|
|status|string|false|none||状态|
|province|string|false|none||省|
|city|string|false|none||市|
|district|string|false|none||区|
|recipientAddress|string|false|none||收件地址|
|recipient|string|false|none||收件人|
|recipientPhone|string|false|none||收件电话|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultPublicServiceInvoiceInfoVO">PaginationResultPublicServiceInvoiceInfoVO</h2>

<a id="schemapaginationresultpublicserviceinvoiceinfovo"></a>
<a id="schema_PaginationResultPublicServiceInvoiceInfoVO"></a>
<a id="tocSpaginationresultpublicserviceinvoiceinfovo"></a>
<a id="tocspaginationresultpublicserviceinvoiceinfovo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "submissionDate": "string",
      "alipayMid": "string",
      "settlementInstitutionName": "string",
      "alipayAccount": "string",
      "settlementPid": "string",
      "startYearMonth": "string",
      "endYearMonth": "string",
      "billingAmount": 0,
      "responsiblePerson": "string",
      "invoiceOrTaxServiceName": "string",
      "invoiceType": "string",
      "invoiceRequirements": "string",
      "invoiceCode": "string",
      "invoiceNumber": "string",
      "billingTime": "string",
      "expressOrderNumber": "string",
      "logisticsStatus": "string",
      "paymentStatus": "string",
      "returnTicketStatus": "string",
      "statusDescription": "string",
      "status": "string",
      "province": "string",
      "city": "string",
      "district": "string",
      "recipientAddress": "string",
      "recipient": "string",
      "recipientPhone": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[PublicServiceInvoiceInfoVO](#schemapublicserviceinvoiceinfovo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PublicServiceInvoiceInfoQueryDTO">PublicServiceInvoiceInfoQueryDTO</h2>

<a id="schemapublicserviceinvoiceinfoquerydto"></a>
<a id="schema_PublicServiceInvoiceInfoQueryDTO"></a>
<a id="tocSpublicserviceinvoiceinfoquerydto"></a>
<a id="tocspublicserviceinvoiceinfoquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|

<h2 id="tocS_PublicServiceSettlementPlanVO">PublicServiceSettlementPlanVO</h2>

<a id="schemapublicservicesettlementplanvo"></a>
<a id="schema_PublicServiceSettlementPlanVO"></a>
<a id="tocSpublicservicesettlementplanvo"></a>
<a id="tocspublicservicesettlementplanvo"></a>

```json
{
  "id": 0,
  "settlementInstitutionName": "string",
  "settlementPid": "string",
  "settlementPeriod": "string",
  "handlingFeeAmount": 0,
  "billingStatus": "string",
  "collectionStatus": "string",
  "adjustmentStatus": "string",
  "tenantId": 0,
  "createTime": "string",
  "createBy": "string",
  "updateTime": "string",
  "updateBy": "string",
  "dataSource": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|
|settlementPeriod|string|false|none||结算周期|
|handlingFeeAmount|number|false|none||手续费金额|
|billingStatus|string|false|none||开票状态|
|collectionStatus|string|false|none||收款状态|
|adjustmentStatus|string|false|none||调账状态|
|tenantId|integer(int64)|false|none||租户号|
|createTime|string|false|none||创建时间|
|createBy|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updateBy|string|false|none||更新人|
|dataSource|string|false|none||数据来源|

<h2 id="tocS_PaginationResultPublicServiceSettlementPlanVO">PaginationResultPublicServiceSettlementPlanVO</h2>

<a id="schemapaginationresultpublicservicesettlementplanvo"></a>
<a id="schema_PaginationResultPublicServiceSettlementPlanVO"></a>
<a id="tocSpaginationresultpublicservicesettlementplanvo"></a>
<a id="tocspaginationresultpublicservicesettlementplanvo"></a>

```json
{
  "success": true,
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": 0,
      "settlementInstitutionName": "string",
      "settlementPid": "string",
      "settlementPeriod": "string",
      "handlingFeeAmount": 0,
      "billingStatus": "string",
      "collectionStatus": "string",
      "adjustmentStatus": "string",
      "tenantId": 0,
      "createTime": "string",
      "createBy": "string",
      "updateTime": "string",
      "updateBy": "string",
      "dataSource": "string"
    }
  ],
  "traceId": "string",
  "pageNum": 0,
  "pageSize": 0,
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|string|false|none||none|
|message|string|false|none||none|
|data|[[PublicServiceSettlementPlanVO](#schemapublicservicesettlementplanvo)]|false|none||none|
|traceId|string|false|none||none|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|total|integer|false|none||none|

<h2 id="tocS_PublicServiceSettlementPlanQueryDTO">PublicServiceSettlementPlanQueryDTO</h2>

<a id="schemapublicservicesettlementplanquerydto"></a>
<a id="schema_PublicServiceSettlementPlanQueryDTO"></a>
<a id="tocSpublicservicesettlementplanquerydto"></a>
<a id="tocspublicservicesettlementplanquerydto"></a>

```json
{
  "pageNum": 0,
  "pageSize": 0,
  "tenantId": 0,
  "orgNo": 0,
  "orgNoList": [
    0
  ],
  "operatorId": 0,
  "operatorName": "string",
  "settlementInstitutionName": "string",
  "settlementPid": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||none|
|pageSize|integer|false|none||none|
|tenantId|integer(int64)|false|none||none|
|orgNo|integer(int64)|false|none||none|
|orgNoList|[integer]|false|none||none|
|operatorId|integer(int64)|false|none||none|
|operatorName|string|false|none||none|
|settlementInstitutionName|string|false|none||结算机构名称|
|settlementPid|string|false|none||结算PID|

