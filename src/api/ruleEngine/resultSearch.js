import request from "@/utils/request";
//结果查询
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/ledger/urgencyDegree/list",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/aging/config/delete",
      method: "post",
      data: query,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/checkItemDef/update",
      method: "post",
      data: query,
    });
  },
  //查询日志
  queryLog(data) {
    return request({
      url: "/ledger/urgencyDegree/log/list",
      method: "get",
      params: data,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/ledger/urgencyDegree/save",
      method: "post",
      data: query,
    });
  },
  //状态切换
  changeStatus(query) {
    return request({
      url: "/ledger/urgencyDegree/changeStatus",
      method: "post",
      data: query,
    });
  },

  // 告警详情相关接口
  // 获取告警详情
  getAlarmDetail(query) {
    return request({
      url: "/ruleEngine/alarm/detail",
      method: "get",
      params: query,
    });
  },

  // 获取预警趋势数据
  getTrendData(query) {
    return request({
      url: "/ruleEngine/alarm/trend",
      method: "post",
      data: query,
    });
  },

  // 获取规则表达式数据
  getRuleExpression(query) {
    return request({
      url: "/ruleEngine/alarm/ruleExpression",
      method: "get",
      params: query,
    });
  },

  // 获取规则逻辑数据
  getRuleLogicData(query) {
    return request({
      url: "/ruleEngine/alarm/ruleLogic",
      method: "get",
      params: query,
    });
  },

  // 获取上下文数据
  getContextData(query) {
    return request({
      url: "/ruleEngine/alarm/contextData",
      method: "get",
      params: query,
    });
  },

  // 获取处理记录
  getProcessRecords(query) {
    return request({
      url: "/ruleEngine/alarm/processRecords",
      method: "get",
      params: query,
    });
  },

  // 获取执行日志
  getExecutionLogs(query) {
    return request({
      url: "/ruleEngine/alarm/executionLogs",
      method: "get",
      params: query,
    });
  },

  // 解除告警
  releaseAlarm(data) {
    return request({
      url: "/ruleEngine/alarm/release",
      method: "post",
      data: data,
    });
  },

  // 获取解除进度选项
  getReleaseStatusOptions() {
    return request({
      url: "/ruleEngine/alarm/releaseStatusOptions",
      method: "get",
    });
  },
};
