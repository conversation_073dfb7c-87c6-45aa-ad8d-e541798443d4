import request from "@/utils/request";

export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/material/list",
      method: "post",
      data: data,
    });
  },

  // 新增物料
  add(data) {
    return request({
      url: "/material/add",
      method: "post",
      data: data,
    });
  },

  // 更新物料
  update(data) {
    return request({
      url: "/material/update",
      method: "post",
      data: data,
    });
  },

  // 删除物料
  del(id) {
    return request({
      url: `/material/delete/${id}`,
      method: "delete",
    });
  },

  // 批量删除
  deleteData(data) {
    return request({
      url: "/material/batchDelete",
      method: "post",
      data: data,
    });
  },

  // 检查物料是否可删除（是否有出入库记录）
  checkDeletable(id) {
    return request({
      url: `/material/checkDeletable/${id}`,
      method: "get",
    });
  },

  // 物料编码唯一性校验
  checkCodeUnique(code, id) {
    return request({
      url: "/material/checkCodeUnique",
      method: "post",
      data: { code, id },
    });
  },

  // 状态切换
  changeStatus(data) {
    return request({
      url: "/material/changeStatus",
      method: "post",
      data: data,
    });
  },

  // 查询日志
  queryLog(data) {
    return request({
      url: "/material/queryLog",
      method: "post",
      data: data,
    });
  },

  // 导出
  export(data) {
    return request({
      url: "/material/export",
      method: "post",
      data: data,
    });
  },

  // 查询物料分类列表（用于自动完成）
  queryMaterialTypes(params) {
    return request({
      url: "/material/queryMaterialTypes",
      method: "post",
      data: params,
    });
  },

  // 查询物料名称列表（用于自动完成）
  queryMaterialNames(params) {
    return request({
      url: "/material/queryMaterialNames",
      method: "post",
      data: params,
    });
  },

  // 查询规格型号列表（用于自动完成）
  queryModelNumbers(params) {
    return request({
      url: "/material/queryModelNumbers",
      method: "post",
      data: params,
    });
  },

  // 查询生产厂家列表（用于自动完成）
  queryManufacturers(params) {
    return request({
      url: "/material/queryManufacturers",
      method: "post",
      data: params,
    });
  },
};
