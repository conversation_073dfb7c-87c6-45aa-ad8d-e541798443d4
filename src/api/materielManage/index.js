import request from "@/utils/request";

export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/wlMaterialInfo/list",
      method: "post",
      data: data,
    });
  },

  // 新增物料
  add(data) {
    return request({
      url: "/wlMaterialInfo/add",
      method: "post",
      data: data,
    });
  },

  // 更新物料
  update(data) {
    return request({
      url: "/wlMaterialInfo/update",
      method: "post",
      data: data,
    });
  },

  // 删除物料
  del(id) {
    return request({
      url: `/wlMaterialInfo/delete/${id}`,
      method: "delete",
    });
  },

  // 批量删除
  deleteData(data) {
    return request({
      url: "/wlMaterialInfo/batchDelete",
      method: "post",
      data: data,
    });
  },

  // 检查物料是否可删除（是否有出入库记录）
  checkDeletable(id) {
    return request({
      url: `/wlMaterialInfo/checkDeletable/${id}`,
      method: "get",
    });
  },

  // 物料编码唯一性校验
  checkCodeUnique(code, id) {
    return request({
      url: "/wlMaterialInfo/checkCodeUnique",
      method: "post",
      data: { code, id },
    });
  },

  // 状态切换
  changeStatus(data) {
    return request({
      url: "/wlMaterialInfo/changeStatus",
      method: "post",
      data: data,
    });
  },

  // 查询日志
  queryLog(data) {
    return request({
      url: "/wlMaterialInfo/queryLog",
      method: "post",
      data: data,
    });
  },

  // 导出
  export(data) {
    return request({
      url: "/wlMaterialInfo/export",
      method: "post",
      data: data,
    });
  },

  // 查询物料分类列表（用于自动完成）
  queryMaterialTypes(params) {
    return request({
      url: "/wlMaterialInfo/queryMaterialTypes",
      method: "post",
      data: params,
    });
  },

  // 查询物料名称列表（用于自动完成）
  queryMaterialNames(params) {
    return request({
      url: "/wlMaterialInfo/queryMaterialNames",
      method: "post",
      data: params,
    });
  },

  // 查询规格型号列表（用于自动完成）
  queryModelNumbers(params) {
    return request({
      url: "/wlMaterialInfo/queryModelNumbers",
      method: "post",
      data: params,
    });
  },

  // 查询生产厂家列表（用于自动完成）
  queryManufacturers(params) {
    return request({
      url: "/wlMaterialInfo/queryManufacturers",
      method: "post",
      data: params,
    });
  },
};
