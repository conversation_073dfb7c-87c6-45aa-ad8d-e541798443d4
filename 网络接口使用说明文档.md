# 充电维保系统网络接口使用说明文档

## 目录

- [1. 网络请求封装](#1-网络请求封装)
  - [1.1 基础请求封装](#11-基础请求封装)
  - [1.2 工作流请求封装](#12-工作流请求封装)
  - [1.3 二进制数据请求封装](#13-二进制数据请求封装)
  - [1.4 WebSocket封装](#14-websocket封装)
- [2. API模块组织结构](#2-api模块组织结构)
- [3. 接口使用方式](#3-接口使用方式)
  - [3.1 基本使用方式](#31-基本使用方式)
  - [3.2 导出文件](#32-导出文件)
  - [3.3 上传文件](#33-上传文件)
  - [3.4 WebSocket使用](#34-websocket使用)
- [4. 环境配置](#4-环境配置)
- [5. 常见问题与解决方案](#5-常见问题与解决方案)

## 1. 网络请求封装

### 1.1 基础请求封装

系统使用 `axios` 进行网络请求封装，主要封装文件为 `src/utils/request.js`。该封装提供了以下功能：

- 统一的请求基础路径配置
- 请求和响应拦截器
- 统一的错误处理
- Token 认证
- 数据加密/解密处理
- 超时处理
- 文件下载支持

基础请求实例创建：

```javascript
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  paramsSerializer: (params) => qs.stringify(params, { indices: false }),
  // 超时
  timeout: 100000,
  headers:
    location.protocol == "https:" ? { ...encryptHeader, ...decryptHeader } : {},
});
```

请求拦截器主要处理：
- 添加 Token 到请求头
- 处理加密请求
- 处理文件流请求

响应拦截器主要处理：
- 统一的错误码处理
- 登录状态过期处理
- 响应数据解密
- 文件下载处理

### 1.2 工作流请求封装

系统针对工作流相关请求进行了单独封装，文件为 `src/utils/processRequest.js`。该封装主要用于处理工作流相关的接口请求，与基础请求封装类似，但有以下区别：

- 使用不同的 baseURL（`VUE_APP_BASE_API_PROCESS`）
- 请求中自动添加工作流应用标识（`app: process.env.VUE_APP_WHALE_FLOW_KEY`）
- 针对工作流特定的响应处理

```javascript
// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API_PROCESS,
  timeout: 10000
});
```

### 1.3 二进制数据请求封装

系统针对二进制数据（如文件下载）请求进行了单独封装，文件为 `src/utils/request_blob.js`。该封装主要用于处理二进制数据的下载和处理。

```javascript
const service1 = axios.create({
  baseURL: apiPrefix, // url = base url + request url
  timeout: 5000 // request timeout
});
```

### 1.4 WebSocket封装

系统提供了 WebSocket 连接的封装，文件为 `src/utils/websocket.js`。该封装提供了以下功能：

- WebSocket 连接的建立和关闭
- 心跳检测机制
- 自动重连机制
- 消息发送和接收处理

```javascript
export function initWebSocket() {
  if (typeof (WebSocket) === 'undefined') {
    Message.error('您的浏览器不支持WebSocket，无法获取数据')
    return false
  }

  // ws请求完整地址
  const requstWsUrl = wsUrl;
  websock = new WebSocket(requstWsUrl)
  
  // 设置各种事件处理器
  websock.onmessage = function (e) {
    websocketonmessage(e)
  }
  websock.onopen = function () {
    websocketOpen()
    // 开启心跳检测
    reset();
  }
  // ...
}
```

## 2. API模块组织结构

系统的 API 接口按照业务模块进行组织，主要位于 `src/api` 目录下。每个业务模块通常有自己的 API 文件或目录。

主要的 API 模块包括：

- **通用接口**：`src/api/common.js`
- **登录认证**：`src/api/login.js`
- **系统管理**：`src/api/system/`
- **充电站管理**：`src/api/chargingStation/`
- **工单管理**：`src/api/workOrderWorkbench/`
- **首页相关**：`src/api/homePage/`
- **档案管理**：`src/api/archive/`
- **需求池管理**：`src/api/demandPool/`
- **工程项目**：`src/api/engineerProject/`
- **台账管理**：`src/api/ledger/`
- **运维管理**：`src/api/operationMaintenanceManage/`
- **工作流相关**：`src/api/process/`

每个 API 模块通常导出多个函数，每个函数对应一个接口请求。

## 3. 接口使用方式

### 3.1 基本使用方式

系统中 API 接口的使用方式主要有两种：

#### 方式一：直接导入 API 函数

```javascript
import { queryStationList, exportStationExcel } from "@/api/chargingStation/index.js";

// 在组件方法中使用
methods: {
  async loadData() {
    const res = await queryStationList(this.params);
    this.tableData = res.data;
    this.tablePage.total = res.total;
  }
}
```

#### 方式二：导入整个 API 模块

```javascript
import api from "@/api/archive/index.js";

// 在组件方法中使用
methods: {
  async loadData() {
    const res = await api.getTableData(this.params);
    this.tableData = res.data;
    this.tablePage.total = res.total;
  }
}
```

### 3.2 导出文件

系统支持文件导出功能，通常通过设置 `responseType: "blob"` 来处理：

```javascript
// API 定义
export function exportExcel(data) {
  return request({
    url: "/station/export",
    method: "post",
    data: data,
    responseType: "blob"
  });
}

// 使用方式
exportExcel(params).then(blob => {
  const fileName = `站点信息_${new Date().getTime()}.xlsx`;
  fileDownload(blob, fileName);
});
```

系统还提供了一个导出混入 `exportMixin`，可以简化导出操作。

### 3.3 上传文件

系统提供了文件上传组件，主要有：
- `FileUpload`：单文件上传组件
- `MultiFileUpload`：多文件上传组件
- `BatchUpload`：批量上传组件

上传接口通常使用 FormData 格式：

```javascript
export function uploadFile(data) {
  const formData = new FormData();
  formData.append('file', data.file);
  // 添加其他参数
  
  return request({
    url: "/upload/file",
    method: "post",
    data: formData
  });
}
```

### 3.4 WebSocket使用

系统提供了 WebSocket 的使用方式：

```javascript
import { sendWebsocket, closeWebsocket } from "@/utils/websocket";

// 建立连接并发送消息
sendWebsocket(
  process.env.VUE_APP_WEBSOCKET_URL,
  JSON.stringify(data),
  this.handleMessage,
  this.handleError
);

// 处理接收到的消息
handleMessage(data) {
  const message = JSON.parse(data);
  // 处理消息
}

// 关闭连接
closeWebsocket();
```

## 4. 环境配置

系统提供了不同环境的配置文件：

- 开发环境：`.env.development`
- 测试环境：`.env.staging`
- 生产环境：`.env.production`

主要配置项包括：

```
# 基础 API 路径
VUE_APP_BASE_API = '/charging-maintenance-server'

# 工作流 API 路径
VUE_APP_BASE_API_PROCESS = "https://test-napi.bangdao-tech.com/charging-maintenance-flow"

# WebSocket 路径
VUE_APP_WEBSOCKET_URL = 'wss://test-napi.bangdao-tech.com/jnsw-project-test/webSocket'

# 工作流应用标识
VUE_APP_WHALE_FLOW_KEY = 'SF-CM'

# 文件上传路径
VUE_APP_BASE_UPLOAD_URL = "https://test-napi.bangdao-tech.com/charging-maintenance-server/flow-docking/api/v1/files/upload"
```

## 5. 常见问题与解决方案

### 5.1 请求超时

默认请求超时时间为 100 秒（基础请求）或 10 秒（工作流请求）。如果遇到请求超时问题，可以：

- 检查网络连接
- 检查服务器状态
- 对于特定请求，可以单独设置更长的超时时间

```javascript
export function longTimeRequest(data) {
  return request({
    url: "/api/longTime",
    method: "post",
    data: data,
    timeout: 300000 // 设置为 5 分钟
  });
}
```

### 5.2 文件下载问题

如果文件下载出现问题，可能的原因和解决方案：

- 确保设置了正确的 `responseType: "blob"`
- 检查文件名编码问题，可能需要处理中文文件名
- 使用 `fileDownload` 工具函数处理下载

### 5.3 Token 过期处理

系统已内置 Token 过期处理机制，当接收到 `code == 40000` 的响应时，会提示用户重新登录。

### 5.4 加密请求问题

系统支持请求加密，如果遇到加密相关问题：

- 检查是否正确配置了加密头 `bdgatewayencryptiontype`
- 检查加密/解密函数是否正常工作
- 在开发环境可以临时关闭加密进行调试

### 5.5 跨域问题

开发环境已配置代理解决跨域问题，如果仍然遇到跨域问题：

- 检查 `vue.config.js` 中的代理配置
- 确保后端服务已正确配置 CORS 头
- 使用浏览器插件或工具进行调试
