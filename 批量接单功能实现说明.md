# 批量接单功能实现说明

## 功能概述

在 `src/views/ledger/commonEmail/originalData.vue` 页面中成功实现了批量接单功能，支持配置接单和复核接单两个场景的批量操作。

## 实现的功能特性

### 1. 列表勾选功能

- ✅ 为数据列表添加了多选框（checkbox）
- ✅ 只有符合接单条件的数据行才允许被勾选
- ✅ 接单权限判断逻辑：参考 `modalConfig.customOperationTypes` 中接单按钮的 `condition` 字段
- ✅ 不符合接单条件的数据行，其勾选框为禁用状态

### 2. 批量接单按钮交互

- ✅ 添加了【批量接单】下拉按钮
- ✅ 按钮启用条件：列表中至少勾选了一条数据（勾选条数 > 0）
- ✅ 点击按钮后显示下拉选项：【配置接单】、【复核接单】
- ✅ 下拉选项为单选模式

### 3. 当前页/全部页选择器

- ✅ 实现了页面选择器组件，包含"当前页"和"全部页"两个选项
- ✅ 当选择"当前页"时：传参使用已勾选数据的 `mailId` 数组
- ✅ 当选择"全部页"时：传参使用 `allPageFlag: true` 标识

### 4. 接单弹窗调用

- ✅ 选择配置接单或复核接单后，调用现有的接单弹窗
- ✅ 支持批量参数传递：`mailIds`（当前页模式）或 `allPageFlag`（全部页模式）
- ✅ 传递 `receiveType` 参数区分接单类型（1：配置接单，2：复核接单）

### 5. 批量接单接口参数

- ✅ 使用专门的批量接单接口 `/publicEmail/batchReceiveOrder`
- ✅ 单个接单仍使用原有接口 `/publicEmail/receiveOrder`
- ✅ 支持的传参格式：
  - `mailIds`: 主键 ID 数组（当选择"当前页"时使用）
  - `allPageFlag`: 布尔值，true 表示全部页操作
  - `receiveType`: 接单类型（1：配置接单，2：复核接单）
  - 查询条件参数（全部页模式时传递）

### 6. 数据刷新

- ✅ 接单操作完成后（弹窗确定按钮点击后），自动刷新列表数据
- ✅ 批量操作成功后自动清空选中数据

## 技术实现细节

### 新增的数据属性

```javascript
data() {
  return {
    // 批量操作相关
    selectPage: "1", // 当前页/全部页选择
    selectedData: [], // 已选择的数据
  }
}
```

### 新增的计算属性

```javascript
computed: {
  // 批量操作按钮是否禁用
  isBatchOperationDisabled() {
    return this.selectPage === "1" && this.selectedData.length === 0;
  }
}
```

### 新增的方法

1. `handleBatchCommand(command)` - 处理批量操作下拉菜单命令
2. `handleBatchAccept(receiveType)` - 批量接单处理
3. `handleBatchSelect(arr)` - 批量选择处理
4. `clearSelectedData()` - 清空选中的数据

### 新增的 API 接口

在 `src/api/ledger/commonEmail.js` 中添加了新的批量接单接口：

```javascript
// 批量接单
batchAccept(data) {
  return request({
    url: "/publicEmail/batchReceiveOrder",
    method: "post",
    data: data,
  });
}
```

### 修改的配置

1. **表格配置**：

   - 设置 `keyField: "mailId"` 用于行标识
   - 添加 `checkboxConfig.checkMethod` 方法控制勾选框启用/禁用
   - 全部页模式时禁用所有勾选框
   - 当前页模式时根据接单条件控制勾选框

2. **事件监听**：

   - 添加 `@handleBatchSelect` 事件监听
   - 添加 `selectPage` 监听器，切换到全部页时自动清空选中数据

3. **弹窗确认处理**：
   - 修改 `modalConfirmHandler` 方法支持批量操作
   - 批量操作时显示不同的确认文本
   - 批量操作成功后自动清空选中数据
   - 智能接口选择：批量接单使用 `api.batchAccept()`，单个接单使用 `api.accept()`

### 接口选择逻辑

```javascript
// 根据是否为批量操作选择不同的接口
let res;
if (crudOperationType === "accept" && isBatchOperation) {
  // 批量接单使用专门的批量接口
  res = await api.batchAccept(params);
} else {
  // 单个操作使用原有接口
  res = await api[crudOperationType](params);
}
```

## 用户交互流程

1. 用户在列表中勾选符合接单条件的数据行
2. 选择"当前页"或"全部页"模式
3. 点击【批量接单】按钮
4. 从下拉菜单中选择【配置接单】或【复核接单】
5. 系统打开接单弹窗，预填批量操作参数
6. 用户在弹窗中完成接单信息填写
7. 点击确定后系统执行批量接单操作
8. 操作成功后刷新列表并清空选中状态

## 代码质量保证

- ✅ 通过 ESLint 代码检查
- ✅ 遵循项目现有的代码风格和架构
- ✅ 保持与 BuseCrud 组件的一致性
- ✅ 复用现有的 API 接口和错误处理机制
- ✅ 严格遵循 Vue 2 + Element UI 技术栈要求

## 注意事项

1. 批量接单功能完全复用现有的单个接单业务逻辑
2. 权限控制与单个接单保持一致
3. 全部页模式下会传递当前的查询条件给后端
4. 批量操作的确认提示会根据操作类型动态显示
5. 操作成功后会自动刷新数据并清空选中状态

## 测试建议

1. 测试不同接单状态下的勾选框启用/禁用逻辑
2. 测试当前页和全部页模式的参数传递
3. 测试配置接单和复核接单的业务逻辑
4. 测试批量操作的权限控制
5. 测试操作成功后的数据刷新和状态清空
