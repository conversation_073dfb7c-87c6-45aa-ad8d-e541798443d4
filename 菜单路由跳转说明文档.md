# 菜单路由跳转机制说明文档

本文档详细说明了系统中点击左侧菜单栏进行路由跳转的实现机制，包括菜单数据的获取、处理和路由跳转的完整流程。

## 1. 整体架构

系统采用 Vue.js + Vue Router + Vuex 实现路由管理，菜单结构由后端接口提供，前端负责渲染和处理点击事件。整体架构分为以下几个部分：

- **菜单数据获取**：从后端 API 获取菜单数据
- **路由生成**：根据菜单数据动态生成路由
- **菜单渲染**：将菜单数据渲染为左侧菜单栏
- **点击事件处理**：处理菜单点击事件并进行路由跳转

## 2. 菜单数据获取

系统在用户登录后，通过 API 获取菜单数据，相关代码位于：

### 2.1 API 定义

```javascript
// src/api/menu.js
import request from '@/utils/request';

// 获取路由
export const getRouters = () => {
  return request({
    url: '/getRouters',
    method: 'get'
  });
};
```

### 2.2 权限和路由生成

在 Vuex 的 permission 模块中处理菜单数据并生成路由：

```javascript
// src/store/modules/permission.js
import { constantRoutes } from '@/router';
import { getRouters } from '@/api/menu';
import Layout from '@/layout/index';

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes
    },
    // ...其他 mutations
  },
  actions: {
    // 生成路由
    GenerateRoutes({ commit }) {
      return new Promise(resolve => {
        // 向后端请求路由数据
        getRouters().then(res => {
          const sdata = JSON.parse(JSON.stringify(res.data))
          const rdata = JSON.parse(JSON.stringify(res.data))
          const sidebarRoutes = filterAsyncRouter(sdata)
          const rewriteRoutes = filterAsyncRouter(rdata, false, true)
          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
          commit('SET_ROUTES', rewriteRoutes)
          commit('SET_SIDEBAR_ROUTERS', sidebarRoutes)
          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))
          commit('SET_DEFAULT_ROUTES', sidebarRoutes)
          commit('SET_TOPBAR_ROUTES', sidebarRoutes)
          resolve(rewriteRoutes)
        });
      });
    }
  }
};

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, hasParent = false) {
  return asyncRouterMap.filter(route => {
    if (route.component) {
      // Layout组件特殊处理
      if (!hasParent && route.component === 'Layout') {
        route.component = Layout;
      } else if (route.component !== 'Layout') {
        route.component = loadView(route.component);
      } else {
        route.component = {
          render(c) {
            return c('router-view');
          }
        };
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, true);
    }
    return true;
  });
}

export const loadView = view => {
  // 路由懒加载
  return resolve => require([`@/views/${view}`], resolve);
};
```

### 2.3 路由注册

在 `src/permission.js` 中，系统会在用户登录后获取用户信息，然后生成并注册路由：

```javascript
router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch('GetInfo')
          .then(res => {
            // 拉取user_info
            const roles = res.data.roles;
            store.dispatch('GenerateRoutes', { roles }).then(accessRoutes => {
              // 动态添加可访问路由表
              for(let i = 0, length = accessRoutes.length; i < length; i += 1){
                  const element = accessRoutes[i];
                  router.addRoute(element);
              }
              // ...
            });
          });
      }
    }
  }
});
```

## 3. 菜单渲染

系统使用递归组件渲染菜单，主要涉及以下组件：

### 3.1 侧边栏容器组件

```vue
<!-- src/layout/components/Sidebar/index.vue -->
<template>
  <div :class="{ 'has-logo': showLogo }" class="wrap-border">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
        :default-openeds="defaultOpeneds"
      >
        <sidebar-item
          v-for="route in routers"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
export default {
  // ...
  computed: {
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    // ...其他计算属性
  }
};
</script>
```

### 3.2 菜单项组件

```vue
<!-- src/layout/components/Sidebar/SidebarItem.vue -->
<template>
  <div v-if="!item.hidden" class="menu-wrapper">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
          (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
          !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <item
            :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
            :title="onlyOneChild.meta.title"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
    >
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="item.meta && item.meta.icon"
          :title="item.meta.title"
        />
      </template>
      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from "path";
import { isExternal } from "@/utils/validate";
import Item from "./Item";
import AppLink from "./Link";

export default {
  name: "SidebarItem",
  components: { Item, AppLink },
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: "",
    },
  },
  data() {
    this.onlyOneChild = null;
    return {
      acc: this.$store.getters.name,
    };
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      // ...判断是否只有一个子菜单的逻辑
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
  },
};
</script>
```

### 3.3 链接组件

```vue
<!-- src/layout/components/Sidebar/Link.vue -->
<template>
  <component v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from '@/utils/validate';

export default {
  props: {
    to: {
      type: String,
      required: true
    }
  },
  methods: {
    linkProps(url) {
      if (isExternal(url)) {
        return {
          is: 'a',
          href: url,
          target: '_blank',
          rel: 'noopener'
        };
      }
      return {
        is: 'router-link',
        to: url
      };
    }
  }
};
</script>
```

## 4. 菜单点击与路由跳转

当用户点击菜单项时，系统通过以下机制实现路由跳转：

### 4.1 菜单项点击处理

系统使用 Vue Router 的 `<router-link>` 组件处理菜单点击。在 `SidebarItem.vue` 中，通过 `<app-link>` 组件（封装了 `<router-link>`）实现点击跳转：

```vue
<app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
  <el-menu-item
    :index="resolvePath(onlyOneChild.path)"
    :class="{ 'submenu-title-noDropdown': !isNest }"
  >
    <item
      :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
      :title="onlyOneChild.meta.title"
    />
  </el-menu-item>
</app-link>
```

`app-link` 组件会根据路径类型（内部路由或外部链接）决定使用 `<router-link>` 还是 `<a>` 标签：

```javascript
// src/layout/components/Sidebar/Link.vue
methods: {
  linkProps(url) {
    if (isExternal(url)) {
      return {
        is: 'a',
        href: url,
        target: '_blank',
        rel: 'noopener'
      };
    }
    return {
      is: 'router-link',
      to: url
    };
  }
}
```

### 4.2 路由跳转流程

1. 用户点击菜单项
2. 如果是内部路由，`<router-link>` 组件触发 Vue Router 的导航
3. Vue Router 根据路由配置加载对应的组件
4. 组件在 `<app-main>` 区域渲染

```vue
<!-- src/layout/components/AppMain.vue -->
<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>
```

### 4.3 顶部导航栏的路由跳转

系统还实现了顶部导航栏的路由跳转，通过 `handleSelect` 方法处理菜单点击：

```javascript
// src/components/TopNav/index.vue
methods: {
  // 菜单选择事件
  handleSelect(key, keyPath) {
    if (key.indexOf("http://") !== -1 || key.indexOf("https://") !== -1) {
      // http(s):// 路径新窗口打开
      window.open(key, "_blank");
    } else {
      if (key.substring(0,1) == '/') {
        this.$router.push({path: key})
      } else {
        let newPath = keyPath[keyPath.length - 2] + "/" + keyPath[keyPath.length - 1]
        this.$router.push({path: newPath})
      }
    }
  },
  // 当前激活的路由
  activeRoutes(key) {
    var routes = [];
    if (this.childrenMenus && this.childrenMenus.length > 0) {
      this.childrenMenus.map((item) => {
        if (key == item.parentPath || (key == "index" && "" == item.path)) {
          routes.push(item);
        }
      });
    }
    this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
  },
}
```

## 5. 路由缓存机制

系统实现了路由缓存机制，通过 `<keep-alive>` 组件缓存已访问的页面，提高用户体验：

```vue
<!-- src/layout/components/AppMain.vue -->
<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  data() {
    return {
      //开启缓存的页面整理如下（router.js中设置noCache:false且name需和页面的name保持一致）
      jumpPages: [
        "operationWorkOrderNew", //运维工单
        "maintenanceStationList", //站点档案/运维-站点信息
        // ...其他需要缓存的页面
      ]
    };
  }
};
</script>
```

## 6. 总结

系统的菜单路由跳转机制主要包括以下几个步骤：

1. 用户登录后，系统从后端获取菜单数据
2. 根据菜单数据动态生成路由并注册到 Vue Router
3. 使用递归组件渲染菜单结构
4. 用户点击菜单项时，通过 Vue Router 进行路由跳转
5. 目标组件在 `<app-main>` 区域渲染，并根据配置决定是否缓存

这种设计实现了菜单数据与前端路由的解耦，使系统能够根据用户权限动态生成菜单和路由，提高了系统的灵活性和安全性。
